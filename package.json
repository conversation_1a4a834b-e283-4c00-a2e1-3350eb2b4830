{"name": "demo", "version": "1.0.0", "description": "A Vue.js project", "author": "weijinyao <<EMAIL>>", "private": true, "scripts": {"dev": "webpack serve --progress --config build/webpack.dev.conf.js", "dev:local": "webpack serve --progress --config build/webpack.dev.conf.js --host 0.0.0.0 --port 8081", "dev:debug": "webpack serve --progress --config build/webpack.dev.conf.js --host localhost --port 8081", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build:test": "NODE_OPTIONS=\"--openssl-legacy-provider\" node build/buildTest.js", "build": "NODE_OPTIONS=\"--openssl-legacy-provider\" node build/build.js", "build:analyze": "NODE_OPTIONS=\"--openssl-legacy-provider\" npm run build --report", "build:analyze-test": "NODE_OPTIONS=\"--openssl-legacy-provider\" npm run build:test --report"}, "dependencies": {"axios": "0.19.0", "babel-runtime": "^6.26.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "path": "^0.12.7", "sass": "^1.89.2", "vant": "^2.12.54", "vconsole": "^3.15.1", "vue": "^2.5.2", "vue-router": "^3.0.1", "vuex": "^3.1.1", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "compression-webpack-plugin": "^5.0.2", "copy-webpack-plugin": "^6.4.0", "css-loader": "^3.6.0", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^4.5.0", "mini-css-extract-plugin": "^1.6.0", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^3.0.0", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^10.0.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "terser-webpack-plugin": "^4.2.3", "url-loader": "^0.5.8", "vue-hot-reload-api": "^2.3.4", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.5.2", "webpack": "^4.47.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.15.2", "webpack-merge": "^4.2.2"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}