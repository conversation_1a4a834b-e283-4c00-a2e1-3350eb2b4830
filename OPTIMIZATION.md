# 蓝信驾驶舱IAM接入逻辑调整 - 前端实现步骤

## 项目概述
将蓝信入口地址从后端接口改为前端页面地址，实现前端统一处理用户身份认证和跳转逻辑。
**新路由：** `/hr_mobile/loginIAM`

## 前端代码实现步骤

### 步骤1：创建认证入口页面
**文件路径：** `src/views/auth/loginIAM.vue`

```javascript
<template>
  <div class="auth-container">
    <div class="loading-container" v-if="isLoading">
      <van-loading type="spinner" color="#1989fa">认证中...</van-loading>
    </div>
    <div class="error-container" v-else-if="errorMessage">
      <van-icon name="warning-o" color="#ee0a24" size="24" />
      <p>{{ errorMessage }}</p>
      <van-button type="primary" @click="retryAuth">重试</van-button>
    </div>
  </div>
</template>

<script>
import { getUserIdByTicket, setUserldCookie, getUserldCookie } from '@/utils/auth'
import { getQueryParams } from '@/utils/index'

export default {
  name: 'LoginIAM',
  data() {
    return {
      isLoading: true,
      errorMessage: '',
      retryCount: 0,
      maxRetries: 3
    }
  },
  async mounted() {
    await this.handleAuth()
  },
  methods: {
    async handleAuth() {
      try {
        this.isLoading = true
        this.errorMessage = ''
        
        // 步骤1：检查cookie中是否缓存userld
        const cachedUserld = getUserldCookie()
        if (cachedUserld) {
          console.log('发现缓存的userld:', cachedUserld)
          this.redirectToTarget()
          return
        }
        
        // 步骤2：检查URL参数ticket
        const query = getQueryParams()
        const ticket = query.ticket
        const redirectUrl = query.redirectUrl || '/hr_mobile/home'
        
        if (ticket) {
          console.log('发现ticket参数:', ticket)
          await this.processTicket(ticket, redirectUrl)
        } else {
          console.log('未发现ticket参数，重定向到IAM')
          this.redirectToIAM(redirectUrl)
        }
      } catch (error) {
        console.error('认证处理失败:', error)
        this.handleError(error)
      }
    },
    
    async processTicket(ticket, redirectUrl) {
      try {
        // 调用后端接口获取userld
        const response = await getUserIdByTicket(ticket, redirectUrl)
        
        if (response && response.code === 200 && response.data) {
          const userld = response.data.userld || response.data.userId
          if (userld) {
            console.log('获取到userld:', userld)
            // 缓存userld
            setUserldCookie(userld)
            // 跳转到目标页面
            this.redirectToTarget(redirectUrl)
          } else {
            throw new Error('接口返回的userld为空')
          }
        } else {
          throw new Error('接口调用失败')
        }
      } catch (error) {
        console.error('处理ticket失败:', error)
        this.handleError(error)
      }
    },
    
    redirectToIAM(redirectUrl) {
      const currentUrl = window.location.origin + '/hr_mobile/loginIAM'
      const serviceUrl = `${currentUrl}?redirectUrl=${encodeURIComponent(redirectUrl)}`
      const iamUrl = 'https://iam.cec.com.cn/cas/login'
      const fullIamUrl = `${iamUrl}?service=${encodeURIComponent(serviceUrl)}`
      
      console.log('重定向到IAM:', fullIamUrl)
      window.location.href = fullIamUrl
    },
    
    redirectToTarget(redirectUrl = '/hr_mobile/home') {
      console.log('跳转到目标页面:', redirectUrl)
      this.$router.push(redirectUrl)
    },
    
    handleError(error) {
      this.isLoading = false
      this.errorMessage = '认证失败，请重试'
      console.error('认证错误:', error)
    },
    
    retryAuth() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        this.handleAuth()
      } else {
        this.errorMessage = '重试次数已达上限，请刷新页面重试'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f7f8fa;
}

.loading-container {
  text-align: center;
  
  .van-loading {
    margin-bottom: 16px;
  }
}

.error-container {
  text-align: center;
  padding: 20px;
  
  p {
    margin: 16px 0;
    color: #646566;
  }
}
</style>
```

### 步骤2：创建认证工具方法
**文件路径：** `src/utils/auth.js`

```javascript
import request from '@/http/request'
import { getQueryParams } from './index'

// Cookie管理
export const setUserldCookie = (userld, expires = 7) => {
  const date = new Date()
  date.setTime(date.getTime() + (expires * 24 * 60 * 60 * 1000))
  const expiresStr = `expires=${date.toUTCString()}`
  const domain = process.env.NODE_ENV === 'production' ? '; domain=.cec.com.cn' : ''
  document.cookie = `cec_hr_lx_userld=${userld}; ${expiresStr}; path=/; SameSite=Lax${domain}`
}

export const getUserldCookie = () => {
  const cookies = document.cookie.split(';')
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'cec_hr_lx_userld') {
      return value
    }
  }
  return null
}

export const clearUserldCookie = () => {
  const domain = process.env.NODE_ENV === 'production' ? '; domain=.cec.com.cn' : ''
  document.cookie = `cec_hr_lx_userld=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax${domain}`
}

// IAM接口调用
export const getUserIdByTicket = (ticket, redirectUrl) => {
  return request({
    url: '/blade-visual/getUserIdByTicket',
    method: 'POST',
    params: {
      ticket: ticket,
      redirectUrl: redirectUrl
    }
  })
}

// 认证状态检查
export const checkAuthStatus = () => {
  const userld = getUserldCookie()
  const query = getQueryParams()
  return {
    hasUserld: !!userld,
    hasTicket: !!query.ticket,
    userld: userld,
    ticket: query.ticket
  }
}

// 构建IAM重定向URL
export const buildIAMRedirectUrl = (redirectUrl) => {
  const currentUrl = window.location.origin + '/hr_mobile/loginIAM'
  const serviceUrl = `${currentUrl}?redirectUrl=${encodeURIComponent(redirectUrl)}`
  const iamUrl = 'https://iam.cec.com.cn/cas/login'
  return `${iamUrl}?service=${encodeURIComponent(serviceUrl)}`
}
```

### 步骤3：更新路由配置
**文件路径：** `src/router/index.js`

```javascript
// 在现有路由配置中添加新路由
{
  path: "/loginIAM",
  name: "loginIAM",
  component: () => import(/* webpackChunkName: "auth" */ "@/views/auth/loginIAM.vue"),
  meta: {
    title: "用户认证",
    keepAlive: false,
    requiresAuth: false // 不需要认证，避免循环
  }
}
```

### 步骤4：更新API接口
**文件路径：** `src/http/api.js`

```javascript
// 在现有API中添加IAM相关接口
export function getUserIdByTicket(ticket, redirectUrl) {
  return request({
    url: "/blade-visual/getUserIdByTicket",
    method: "POST",
    params: {
      ticket: ticket,
      redirectUrl: redirectUrl
    }
  });
}

export function validateUserld(userld) {
  return request({
    url: "/blade-visual/validateUserld",
    method: "POST",
    params: {
      userld: userld
    }
  });
}
```

### 步骤5：优化路由守卫
**文件路径：** `src/router/index.js`

```javascript
// 在现有的router.beforeEach中添加认证逻辑
import { checkAuthStatus, buildIAMRedirectUrl } from '@/utils/auth'

router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    setDocumentTitle(to.meta.title);
  }

  // 记录页面访问时长
  reportViewDuration(to, from, {})

  // 认证逻辑
  if (to.path === '/loginIAM') {
    // 认证页面不需要额外处理
    next()
    return
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    const authStatus = checkAuthStatus()
    
    if (!authStatus.hasUserld) {
      // 没有userld，重定向到认证页面
      const redirectUrl = to.fullPath
      const iamUrl = buildIAMRedirectUrl(redirectUrl)
      window.location.href = iamUrl
      return
    }
  }

  next()
})
```

### 步骤6：更新主应用入口
**文件路径：** `src/App.vue`

```javascript
// 在mounted中添加认证检查
import { checkAuthStatus } from '@/utils/auth'

export default {
  // ... 现有代码 ...
  mounted() {
    // 检查认证状态
    const authStatus = checkAuthStatus()
    if (!authStatus.hasUserld && this.$route.path !== '/loginIAM') {
      // 如果没有认证且不在认证页面，重定向到认证页面
      this.$router.push('/loginIAM')
    }
    
    // 现有逻辑
    getUserId()
    window.addEventListener('pageshow', function(event) {
      if(event.persisted) location.reload();
    });
    
    this.handleRouteCompanyId();
  }
  // ... 现有代码 ...
}
```

## 实现要点

### 1. Cookie配置
- **域名设置**：生产环境使用 `.cec.com.cn` 域名
- **安全设置**：使用 `SameSite=Lax` 防止CSRF攻击
- **过期时间**：默认7天，可根据需要调整

### 2. 错误处理
- **重试机制**：最多重试3次
- **用户提示**：友好的错误信息
- **日志记录**：详细的控制台日志

### 3. 安全考虑
- **参数验证**：验证ticket和userld的有效性
- **URL编码**：正确处理特殊字符
- **XSS防护**：避免直接插入用户输入

### 4. 性能优化
- **异步处理**：避免阻塞主线程
- **缓存机制**：合理使用Cookie缓存
- **加载状态**：提供用户反馈

## 测试用例

### 1. 正常流程测试
```javascript
// 测试场景：用户首次访问
// 预期结果：重定向到IAM，认证后返回应用

// 测试场景：用户已有缓存
// 预期结果：直接跳转到目标页面

// 测试场景：用户带有ticket参数
// 预期结果：调用接口获取userld，然后跳转
```

### 2. 异常流程测试
```javascript
// 测试场景：ticket无效
// 预期结果：显示错误信息，提供重试选项

// 测试场景：网络异常
// 预期结果：显示网络错误，提供重试选项

// 测试场景：接口返回异常
// 预期结果：显示相应错误信息
```

## 部署注意事项

1. **环境配置**：确保不同环境的域名配置正确
2. **HTTPS要求**：生产环境必须使用HTTPS
3. **跨域配置**：确保IAM域名在CORS白名单中
4. **监控告警**：添加认证失败监控

这个实现方案提供了完整的前端认证流程，包括用户身份检查、IAM重定向、Cookie管理和错误处理等核心功能。 
