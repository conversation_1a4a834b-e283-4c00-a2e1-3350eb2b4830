<template>
  <div class="auth-container">
    <div class="loading-container" v-if="isLoading">
      <van-loading type="spinner" color="#1989fa">认证中...</van-loading>
    </div>
    <div class="error-container" v-else-if="errorMessage">
      <van-icon name="warning-o" color="#ee0a24" size="24" />
      <p>{{ errorMessage }}</p>
      <van-button type="primary" @click="retryAuth">重试</van-button>
    </div>
  </div>
</template>

<script>
import { setUserldCookie, getUserldCookie } from '@/utils/auth'
import { getUserIdByTicket } from '@/http/api'
import { getQueryParams } from '@/utils/index'

export default {
  name: 'LoginIAM',
  data() {
    return {
      isLoading: true,
      errorMessage: '',
      retryCount: 0,
      maxRetries: 3
    }
  },
  async mounted() {
    await this.handleAuth()
  },
  methods: {
    async handleAuth() {
      try {
        this.isLoading = true
        this.errorMessage = ''
        
        // 步骤1：检查cookie中是否缓存userld
        const cachedUserld = getUserldCookie()
        if (cachedUserld) {
          console.log('发现缓存的userld:', cachedUserld)
          console.log('用户跳转地址:', this.$route.query.redirectUrl)
          this.redirectToTarget(this.$route.query.redirectUrl)
          return
        }
        
        const { redirectUrl, otherParams } = this.parseUrlParams()

        // 步骤2：检查URL参数ticket
        const query = getQueryParams()
        const ticket = query.ticket
        console.log('loginIAM - 当前URL参数:', query)
        
        // 解析URL参数
        console.log('目标跳转地址:', redirectUrl)
        console.log('其他参数:', otherParams)
        
        if (ticket) {
          console.log('发现ticket参数:', ticket)
          const authUrl = `${location.origin}/hr_mobile/loginIAM?params=${query.params}`
          console.log('authUrl', authUrl)
          await this.processTicket(ticket, authUrl)
        } else {
          console.log('未发现ticket参数，重定向到IAM')
          this.redirectToIAM(redirectUrl, otherParams)
        }
      } catch (error) {
        console.error('认证处理失败:', error)
        this.handleError(error)
      }
    },
    
    // 构建service URL的公共方法
    buildServiceUrl(redirectUrl, otherParams = {}) {
      const currentUrl = `${location.origin}/hr_mobile/loginIAM`
      
      // 构建包含所有参数的serviceUrl，并对参数进行加密
      const serviceParams = new URLSearchParams({
        redirectUrl: redirectUrl,
        ...otherParams
      })
      
      // 对serviceParams进行base64编码，避免IAM系统误识别参数
      const encodedParams = btoa(serviceParams.toString())
      const serviceUrl = `${currentUrl}?params=${encodeURIComponent(encodedParams)}`
      
      console.log('构建service URL:')
      console.log('- 当前应用URL:', currentUrl)
      console.log('- 原始参数:', serviceParams.toString())
      console.log('- 加密后参数:', encodedParams)
      console.log('- 最终service URL:', serviceUrl)
      
      return serviceUrl
    },
    
    // 解析URL参数的公共方法
    parseUrlParams() {
      const query = getQueryParams()
      let redirectUrl = '/home'
      let otherParams = {}
      
      if (query.params) {
        // 处理加密参数（从IAM回调）
        try {
          const decodedParams = atob(query.params)
          const paramsObj = new URLSearchParams(decodedParams)
          
          redirectUrl = paramsObj.get('redirectUrl') || '/home'
          otherParams = Object.fromEntries(paramsObj.entries())
          
          // 移除redirectUrl，避免重复
          delete otherParams.redirectUrl
          
          console.log('解密后的参数:', otherParams)
          console.log('解密后的redirectUrl:', redirectUrl)
        } catch (error) {
          console.error('参数解密失败:', error)
          // 如果解密失败，尝试从query中直接获取
          redirectUrl = query.redirectUrl || '/home'
          const { ticket: _, redirectUrl: __, params: ___, ...fallbackParams } = query
          otherParams = fallbackParams
        }
      } else {
        // 处理直接参数（从路由守卫传递）
        redirectUrl = query.redirectUrl || '/home'
        const { ticket: _, redirectUrl: __, ...fallbackParams } = query
        otherParams = fallbackParams
      }
      
      return { redirectUrl, otherParams }
    },
    
    async processTicket(ticket, authUrl) {
      try {
        // 调用后端接口获取userld，传递service URL
        console.log('发起请求获取用户信息', ticket, authUrl)
        const response = await getUserIdByTicket(ticket, authUrl)
        console.log(response, '结束请求获取用户信息')
        if (response && response.code === 200 && response.data) {
          const userld = response.data.userld || response.data.userId
          if (userld) {
            console.log('获取到userld:', userld)
            // 缓存userld
            setUserldCookie(userld)
            const { redirectUrl, otherParams } = this.parseUrlParams()
            // 跳转到目标页面，传递其他参数
            this.redirectToTarget(redirectUrl, otherParams)
          } else {
            throw new Error('接口返回的userld为空')
          }
        } else {
          throw new Error('接口调用失败')
        }
      } catch (error) {
        console.error('处理ticket失败:', error)
        this.handleError(error)
      }
    },
    
    redirectToIAM(redirectUrl, otherParams = {}) {
      // 使用公共方法构建service URL
      const serviceUrl = this.buildServiceUrl(redirectUrl, otherParams)
      
      const iamUrl = 'https://iam.cec.com.cn/cas/login'
      const fullIamUrl = `${iamUrl}?service=${encodeURIComponent(serviceUrl)}`
      
      console.log('重定向到IAM:', fullIamUrl)
      console.log('IAM回调地址:', serviceUrl)
      console.log('传递的参数:', otherParams)
      window.location.href = fullIamUrl
    },
    
    redirectToTarget(redirectUrl = '/home', otherParams = {}) {
        // 从redirectUrl中截取路由部分，去掉域名和端口
        let routePath = redirectUrl
        
        // 如果是完整的URL，提取路径部分
        if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
            try {
                const url = new URL(redirectUrl)
                routePath = url.pathname + url.search + url.hash
            } catch (error) {
                console.error('URL解析失败:', error)
                routePath = '/home'
            }
        } else if (redirectUrl.startsWith('//')) {
            // 处理协议相对URL
            try {
                const url = new URL(redirectUrl, window.location.origin)
                routePath = url.pathname + url.search + url.hash
            } catch (error) {
                console.error('URL解析失败:', error)
                routePath = '/home'
            }
        }
        
        // 确保路径以/开头
        if (!routePath.startsWith('/')) {
            routePath = '/' + routePath
        }
        
        // 移除 /hr_mobile 前缀，因为路由配置中已经设置了 base: "hr_mobile"
        if (routePath.startsWith('/hr_mobile/')) {
            routePath = routePath.replace('/hr_mobile', '')
        } else if (routePath === '/hr_mobile') {
            routePath = '/'
        }
        
        console.log('原始redirectUrl:', redirectUrl)
        console.log('提取的路由路径:', routePath)
        console.log('其他参数:', otherParams)
        
        // 使用Vue Router进行前端路由跳转，使用replace避免增加路由历史
        this.$router.replace({
            path: routePath,
            query: otherParams
        })
    },
    
    handleError(error) {
      this.isLoading = false
      this.errorMessage = '认证失败，请重试'
      console.error('认证错误:', error)
    },
    
    retryAuth() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        this.handleAuth()
      } else {
        this.errorMessage = '重试次数已达上限，请刷新页面重试'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f7f8fa;
}

.loading-container {
  text-align: center;
  
  .van-loading {
    margin-bottom: 16px;
  }
}

.error-container {
  text-align: center;
  padding: 20px;
  
  p {
    margin: 16px 0;
    color: #646566;
  }
}
</style> 