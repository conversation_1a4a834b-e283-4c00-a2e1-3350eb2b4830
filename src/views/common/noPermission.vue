<template>
  <div class="no-permission-container">
    <div class="content">
      <div class="icon-wrapper">
        <van-icon name="warning-o" size="60" color="#ff6b6b" />
      </div>
      <h2 class="title">暂无访问权限</h2>
      <p class="description">您当前没有访问此页面的权限，请联系管理员开通相关权限</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoPermission',
  methods: {
    goHome() {
      this.$router.replace('/home')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.no-permission-container {
  overflow: hidden;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  padding: 20px;
}

.content {
  overflow: hidden;
  text-align: center;
  background: white;
  padding: 40px 30px;
  max-width: 400px;
  width: 100%;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 600;
}

.description {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30px;
}
</style>
