<template>
  <div class="content">
    <img class="header" src="../../assets/img/ranking/report_header_bg.png" alt="" />

    <div class="list">
      <Card v-for="(item, index) in data" :key="index" :title="item.title" :data="item.list"/>
    </div>
  </div>
</template>

<script>
import { getReportList } from "@/http/api"
import { reportTypeList } from '@/constants'

import Card from "./components/ReportCard/index.vue"

export default {
  name: "viewReport",
  data() {
    return {
      data: []
    }
  },
  components: {
    Card
  },
  methods: {
    async init() {
      const res = await getReportList()
      const reportList = []
      for (const key in res.data) {
        const title = reportTypeList.find(item => item.key == key).name
        reportList.push({
          title,
          list: res.data[key]
        })
      }
      this.data = reportList.sort((a, b) => a.busOrder - b.busOrder)
    }
  },
  beforeDestroy(){
    this.$loading.hide()
  },
  mounted() {
    this.init()
  }
}
</script>

<style lang="scss" scoped>
  .content {
    // background: url(../../assets/img/ranking/report_bg.png);

    .header {
      width: 100%;
      height: auto;
    }

    .list {
      padding: .4rem .36rem;
      padding-top: 0;

      .card-container {
        margin-bottom: .4rem;
      }
    }
  }
</style>