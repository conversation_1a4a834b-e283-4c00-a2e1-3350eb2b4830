<template>
  <div class="card-container">
    <div class="card-title">{{ title }}</div>
    <div class="report-first">
      <div class="month" @click="preview(data[0].ossName)">{{ data[0] ? formatName(data[0].fileName) : "" }}</div>
      <div class="view-more" v-if="data && data.length > 1" :class="show ? 'show' : ''" @click="toggle">
        查看更多
        <img v-if="show" src="../../../../assets/img/traingle-up-gray.png" alt="" class="traiangle" />
        <img v-else src="../../../../assets/img/traingle-down-blue.png" alt="" class="traiangle" />
      </div>
    </div>
    <div class="month-list" :class="show ? 'show' : ''">
      <van-collapse v-model="activeName" accordion>
        <van-collapse-item :title="item.yearName + '年'" title-class="titleClass" :name="index"
          v-for="(item, index) in padData" :key="index">

          <div v-for="(item1, index1) in item.files" :key="index1" class="month" @click="preview(item1.ossName)">{{
            item1.fileName }}</div>
        </van-collapse-item>


      </van-collapse>
    </div>

  </div>
</template>

<script>
import dayjs from "dayjs";
import { getReport } from "@/http/api"

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      show: false,
      activeName: "0",
      padData: []
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        let currentYear = dayjs().format("YYYY")
        let yearName = newVal.slice(1).map(item => {
          return item.fileName.split('年')[0];
        })
        yearName = [...new Set(yearName)]
        console.log(yearName);
        yearName.forEach((item, index) => {
          if (currentYear+"" === item) {
            this.activeName =index
          }
          let files = []
          newVal.slice(1).forEach(element => {
            if (element.fileName.split("年")[0] == item) {
              files.push({ fileName: element.fileName.split(".")[0], ossName: element.ossName })
            }
          })
          this.padData.push({ yearName: item, files, })
        });

      }
    }
  },
  methods: {
    toggle() {
      this.show = !this.show
    },
    formatName(fileName) {
      const name = fileName.split('.')[0]
      return name
    },
    async preview(name = '') {
      if (!name) return
      this.$loading.show()
      const res = await getReport(name)
      this.$loading.hide()
      let blob = new Blob([res], { type: 'application/pdf;chartset=UTF-8' })
      const url = window.URL.createObjectURL(blob);
      this.$router.push({ path: `/pdfPreview?pdfurl=${url}` });
    },
  }
}
</script>

<style lang="scss" scoped>
.card-container {
  padding: 0.36rem .3rem .3rem .3rem;
  border-radius: .16rem;
  box-shadow: 5px 6px 20px 0 rgba(74, 103, 157, 0.10);
  background-image: linear-gradient(180deg, #e4f0fa 0%, #fff .5rem);

  .card-title {
    font-size: .34rem;
    margin-bottom: .1rem;
  }

  .report-first {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: .3rem;
    color: #666;

    .view-more {
      color: #5193FD;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      min-width: 2.4rem;
      padding: .1rem 0;

      &.show {
        color: #666 !important;


      }

      .traiangle {
        width: .35rem;
        height: .17rem;
        margin-left: .15rem;
      }
    }
  }

  .month-list {
    height: 0;
    border-bottom: .01rem solid #3688f8;
    overflow: hidden;

    &.show {
      height: auto;

      ::v-deep .titleClass {
        font-size: .3rem;
        color: #666;
      }
    }


  }

  .month {
    font-size: .3rem;
    color: #666;
    padding: .1rem 0;
  }

}
</style>