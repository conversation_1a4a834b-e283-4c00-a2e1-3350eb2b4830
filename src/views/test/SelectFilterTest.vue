<template>
  <div class="test-page">
    <div class="header">
      <h2>SelectFilter 组件测试</h2>
    </div>

    <van-cell-group title="基础用法">
      <select-filter
        v-model="selectedArea"
        :options="areaOptions"
        label="地区"
        placeholder="请选择所在地区"
        title="选择地区"
        @change="handleAreaChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </van-cell-group>

    <van-cell-group title="基础用法">
      <van-popup v-model="show" round position="bottom">
        <van-cascader
          v-model="cascaderValue"
          title="请选择所在地区"
          :options="options"
          @close="show = false"
          @finish="onFinish"
        />
      </van-popup>
    </van-cell-group>
  </div>
</template>

<script>
import SelectFilter from '@/components/SelectFilter.vue'

export default {
  name: 'SelectFilterTest',
  components: {
    SelectFilter
  },
  data() {
    return {
      show: false,
      cascaderValue: [],
      options: [
        {
          text: '浙江省',
          value: '330000',
          children: [{ text: '杭州市', value: '330100' }],
        },
        {
          text: '江苏省',
          value: '320000',
          children: [{ text: '南京市', value: '320100' }],
        },
      ],

      selectedArea: 'hangzhou',
      selectedCity: '',
      selectedType: '',
      selectedLongText: '',
      eventLogs: [],
      areaOptions: [
        { text: '杭州', value: 'hangzhou' },
        { text: '宁波', value: 'ningbo' },
        { text: '温州', value: 'wenzhou' },
        { text: '嘉兴', value: 'jiaxing' },
        { text: '湖州', value: 'huzhou' },
        { text: '绍兴', value: 'shaoxing' },
        { text: '金华', value: 'jinhua' },
        { text: '衢州', value: 'quzhou' },
        { text: '舟山', value: 'zhoushan' },
        { text: '台州', value: 'taizhou' },
        { text: '丽水', value: 'lishui' }
      ],
      cityOptions: [
        { text: '北京', value: 'beijing' },
        { text: '上海', value: 'shanghai' },
        { text: '广州', value: 'guangzhou' },
        { text: '深圳', value: 'shenzhen' }
      ],
      typeOptions: [
        { text: '类型一', value: 'type1' },
        { text: '类型二', value: 'type2' },
        { text: '类型三', value: 'type3' }
      ],
      longTextOptions: [
        { text: '这是一个很长很长的选项文本用来测试组件的显示效果', value: 'long1' },
        { text: '另一个超级长的选项文本内容测试', value: 'long2' },
        { text: '短文本', value: 'short' }
      ]
    }
  },
  methods: {
    handleAreaChange(value, option) {
      this.addLog(`地区改变: ${value} - ${option.text}`)
    },
    handleCityChange(value, option) {
      this.addLog(`城市改变: ${value} - ${option.text}`)
    },
    handleLongTextChange(value, option) {
      this.addLog(`长文本改变: ${value} - ${option.text}`)
    },
    handleConfirm(value, option) {
      this.addLog(`确认选择: ${value} - ${option.text}`)
    },
    handleCancel() {
      this.addLog('取消选择')
    },
    setCityValue() {
      this.selectedCity = 'shanghai'
    },
    clearCityValue() {
      this.selectedCity = ''
    },
    getSelectedText(value, options) {
      const option = options.find(opt => opt.value === value)
      return option ? option.text : '未选择'
    },
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.eventLogs.unshift(`[${timestamp}] ${message}`)
      if (this.eventLogs.length > 10) {
        this.eventLogs = this.eventLogs.slice(0, 10)
      }
    },
    clearLogs() {
      this.eventLogs = []
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  // padding: 0.4rem;
  background: #f7f8fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 0.6rem;
  
  h2 {
    font-size: 0.4rem;
    color: #323233;
  }
}

.test-section {
  margin-bottom: 0.6rem;
  
  h3 {
    font-size: 0.36rem;
    color: #323233;
    margin-bottom: 0.3rem;
  }
}

.result-box {
  margin: 0.3rem 0;
  padding: 0.3rem;
  background: #fff;
  border-radius: 0.12rem;
  
  p {
    margin: 0.1rem 0;
    font-size: 0.28rem;
    color: #646566;
  }
}

.log-box {
  background: #fff;
  border-radius: 0.12rem;
  padding: 0.3rem;
  max-height: 4rem;
  overflow-y: auto;
  
  .log-item {
    font-size: 0.26rem;
    color: #646566;
    padding: 0.1rem 0;
    border-bottom: 1px solid #f7f8fa;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

::v-deep .van-button {
  margin: 0.1rem 0.1rem 0 0;
}
</style>
