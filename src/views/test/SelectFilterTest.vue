<template>
  <div class="test-page">
    <div class="header">
      <h2>SelectFilter 组件测试</h2>
    </div>

    <van-cell-group title="基础用法">
      <select-filter
        v-model="selectedArea"
        :options="areaOptions"
        label="地区"
        placeholder="请选择所在地区"
        title="选择地区"
        @change="handleAreaChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
      <div class="result-box">
        <p>选中值: {{ selectedArea }}</p>
        <p>选中文字: {{ getSelectedText(selectedArea, areaOptions) }}</p>
      </div>
    </van-cell-group>

    <van-cell-group title="禁用状态">
      <select-filter
        v-model="selectedType"
        :options="typeOptions"
        label="类型"
        placeholder="请选择类型"
        title="选择类型"
        :disabled="true"
      />
    </van-cell-group>

    <van-cell-group title="级联选择器案例">
      <div class="cascader-trigger" @click="showCascader = true">
        <div class="trigger-content">
          <span class="trigger-label">省市选择</span>
          <span class="trigger-value" :class="{ placeholder: cascaderValue.length === 0 }">
            {{ cascaderText }}
          </span>
        </div>
        <van-icon name="arrow" class="trigger-arrow" />
      </div>
      <van-popup v-model="showCascader" round position="bottom">
        <van-cascader
          v-model="cascaderValue"
          title="请选择所在地区"
          :options="cascaderOptions"
          @close="showCascader = false"
          @finish="onCascaderFinish"
        />
      </van-popup>
      <div class="result-box">
        <p>选中值: {{ cascaderValue }}</p>
        <p>选中文字: {{ cascaderText }}</p>
      </div>
    </van-cell-group>

    <van-cell-group title="级联 + SelectFilter 组合使用">
      <select-filter
        v-model="selectedProvince"
        :options="provinceOptions"
        label="省份"
        placeholder="请选择省份"
        title="选择省份"
        @change="handleProvinceChange"
      />
      <select-filter
        v-model="selectedCity"
        :options="cityOptionsForProvince"
        label="城市"
        placeholder="请选择城市"
        title="选择城市"
        :disabled="!selectedProvince"
        @change="handleCityChange"
      />
      <div class="result-box">
        <p>选中省份: {{ selectedProvince }} - {{ getSelectedText(selectedProvince, provinceOptions) }}</p>
        <p>选中城市: {{ selectedCity }} - {{ getSelectedText(selectedCity, cityOptionsForProvince) }}</p>
      </div>
    </van-cell-group>

    <!-- 事件日志 -->
    <van-cell-group title="事件日志">
      <div class="log-box">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          {{ log }}
        </div>
        <van-button size="small" @click="clearLogs" v-if="eventLogs.length > 0">清空日志</van-button>
      </div>
    </van-cell-group>
  </div>
</template>

<script>
import SelectFilter from '@/components/SelectFilter.vue'

export default {
  name: 'SelectFilterTest',
  components: {
    SelectFilter
  },
  data() {
    return {
      // 级联选择器相关
      showCascader: false,
      cascaderValue: [],
      cascaderOptions: [
        {
          text: '浙江省',
          value: '330000',
          children: [
            { text: '杭州市', value: '330100' },
            { text: '宁波市', value: '330200' },
            { text: '温州市', value: '330300' },
            { text: '嘉兴市', value: '330400' }
          ],
        },
        {
          text: '江苏省',
          value: '320000',
          children: [
            { text: '南京市', value: '320100' },
            { text: '苏州市', value: '320500' },
            { text: '无锡市', value: '320200' }
          ],
        },
        {
          text: '广东省',
          value: '440000',
          children: [
            { text: '广州市', value: '440100' },
            { text: '深圳市', value: '440300' },
            { text: '珠海市', value: '440400' }
          ],
        },
      ],

      // 级联组合使用相关
      selectedProvince: '',
      selectedCity: '',
      provinceOptions: [
        { text: '浙江省', value: 'zhejiang' },
        { text: '江苏省', value: 'jiangsu' },
        { text: '广东省', value: 'guangdong' }
      ],
      provinceCityMap: {
        'zhejiang': [
          { text: '杭州市', value: 'hangzhou' },
          { text: '宁波市', value: 'ningbo' },
          { text: '温州市', value: 'wenzhou' },
          { text: '嘉兴市', value: 'jiaxing' }
        ],
        'jiangsu': [
          { text: '南京市', value: 'nanjing' },
          { text: '苏州市', value: 'suzhou' },
          { text: '无锡市', value: 'wuxi' }
        ],
        'guangdong': [
          { text: '广州市', value: 'guangzhou' },
          { text: '深圳市', value: 'shenzhen' },
          { text: '珠海市', value: 'zhuhai' }
        ]
      },

      // 基础数据
      selectedArea: 'hangzhou',
      selectedType: '',
      eventLogs: [],
      areaOptions: [
        { text: '杭州', value: 'hangzhou' },
        { text: '宁波', value: 'ningbo' },
        { text: '温州', value: 'wenzhou' },
        { text: '嘉兴', value: 'jiaxing' },
        { text: '湖州', value: 'huzhou' },
        { text: '绍兴', value: 'shaoxing' },
        { text: '金华', value: 'jinhua' },
        { text: '衢州', value: 'quzhou' },
        { text: '舟山', value: 'zhoushan' },
        { text: '台州', value: 'taizhou' },
        { text: '丽水', value: 'lishui' }
      ],
      cityOptions: [
        { text: '北京', value: 'beijing' },
        { text: '上海', value: 'shanghai' },
        { text: '广州', value: 'guangzhou' },
        { text: '深圳', value: 'shenzhen' }
      ],
      typeOptions: [
        { text: '类型一', value: 'type1' },
        { text: '类型二', value: 'type2' },
        { text: '类型三', value: 'type3' }
      ],
      longTextOptions: [
        { text: '这是一个很长很长的选项文本用来测试组件的显示效果', value: 'long1' },
        { text: '另一个超级长的选项文本内容测试', value: 'long2' },
        { text: '短文本', value: 'short' }
      ]
    }
  },
  computed: {
    // 级联选择器显示文字
    cascaderText() {
      if (this.cascaderValue.length === 0) return '请选择所在地区'
      const province = this.cascaderOptions.find(p => p.value === this.cascaderValue[0])
      if (!province) return '请选择所在地区'
      if (this.cascaderValue.length === 1) return province.text
      const city = province.children.find(c => c.value === this.cascaderValue[1])
      return city ? city.text : province.text
    },
    // 根据选中省份获取城市选项
    cityOptionsForProvince() {
      if (!this.selectedProvince) return []
      return this.provinceCityMap[this.selectedProvince] || []
    }
  },
  methods: {
    handleAreaChange(value, option) {
      this.addLog(`地区改变: ${value} - ${option.text}`)
    },
    handleCityChange(value, option) {
      this.addLog(`城市改变: ${value} - ${option.text}`)
    },
    // 级联选择器完成选择
    onCascaderFinish({ selectedOptions, value }) {
      this.showCascader = false
      this.addLog(`级联选择完成: ${value.join(' -> ')} (${selectedOptions.map(o => o.text).join(' / ')})`)
    },
    // 省份改变时重置城市选择
    handleProvinceChange(value, option) {
      this.selectedCity = '' // 重置城市选择
      this.addLog(`省份改变: ${value} - ${option.text}`)
    },
    handleConfirm(value, option) {
      this.addLog(`确认选择: ${value} - ${option.text}`)
    },
    handleCancel() {
      this.addLog('取消选择')
    },
    setCityValue() {
      this.selectedCity = 'shanghai'
    },
    clearCityValue() {
      this.selectedCity = ''
    },
    getSelectedText(value, options) {
      const option = options.find(opt => opt.value === value)
      return option ? option.text : '未选择'
    },
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.eventLogs.unshift(`[${timestamp}] ${message}`)
      if (this.eventLogs.length > 10) {
        this.eventLogs = this.eventLogs.slice(0, 10)
      }
    },
    clearLogs() {
      this.eventLogs = []
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  // padding: 0.4rem;
  background: #f7f8fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 0.6rem;
  
  h2 {
    font-size: 0.4rem;
    color: #323233;
  }
}

.test-section {
  margin-bottom: 0.6rem;
  
  h3 {
    font-size: 0.36rem;
    color: #323233;
    margin-bottom: 0.3rem;
  }
}

.result-box {
  margin: 0.3rem 0;
  padding: 0.3rem;
  background: #fff;
  border-radius: 0.12rem;
  
  p {
    margin: 0.1rem 0;
    font-size: 0.28rem;
    color: #646566;
  }
}

.log-box {
  background: #fff;
  border-radius: 0.12rem;
  padding: 0.3rem;
  max-height: 4rem;
  overflow-y: auto;
  
  .log-item {
    font-size: 0.26rem;
    color: #646566;
    padding: 0.1rem 0;
    border-bottom: 1px solid #f7f8fa;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

// 级联选择器触发器样式，与 SelectFilter 保持一致
.cascader-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0.4rem;
  background: #fff;
  border-radius: 0.12rem;
  border: 1px solid #ebedf0;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0.24rem 0.32rem;

  &:active {
    background: #f7f8fa;
  }
}

.trigger-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.trigger-label {
  font-size: 0.32rem;
  color: #323233;
  margin-right: 0.24rem;
  white-space: nowrap;
}

.trigger-value {
  font-size: 0.32rem;
  color: #323233;
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.placeholder {
    color: #c8c9cc;
  }
}

.trigger-arrow {
  font-size: 0.32rem;
  color: #c8c9cc;
  margin-left: 0.16rem;
  transition: transform 0.3s;
  flex-shrink: 0;
}

// 级联选择器蓝色主题样式
::v-deep .van-popup {
  .van-cascader {
    .van-cascader__header {
      .van-cascader__title {
        color: #1989fa;
      }
    }

    .van-cascader__tabs {
      .van-tab {
        color: #646566;

        &.van-tab--active {
          color: #1989fa;
        }
      }

      .van-tabs__line {
        background-color: #1989fa;
      }
    }

    .van-cascader__options {
      .van-cascader__option {
        &.van-cascader__option--selected {
          color: #1989fa;

          .van-icon {
            color: #1989fa;
          }
        }
      }
    }
  }
}

::v-deep .van-button {
  margin: 0.1rem 0.1rem 0 0;
}
</style>
