<template>
  <div class="groupAll">
    <div class="content">
      <div class="itemBoxs" v-for="(item,index) in iconList" :key="index" @click="onPreviewPDF(item.pdfText)">
        <div class="itemBox" v-show="item.isShow">
          <div>
            <img v-if="index==0" src="@/assets/img/icon-产业运营情况.png" />
            <img v-if="index==1" src="@/assets/img/icon-财务运行报告.png" />
            <img v-if="index==2" src="@/assets/img/icon-财务简报.png" />
            <p>{{ item.text }}</p>
          </div>
        </div>
      </div>
      <div v-show="!iconList[0].isShow && !iconList[1].isShow && !iconList[2].isShow" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
        <img src="@/assets/img/noData.png" :style="{width: '100%'}">
        <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getReportStatus } from "@/http/api";
export default {
  name: 'secondUnit',
  props: {
    dt: {
      type: String,
      default: ""
    },
    isShowReportData: Object
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.isShowReport()
      }
    },
    isShowReportData: {
      deep: true,
      handler(newVal, oldVal) {
        console.log(newVal, oldVal, '哈哈');
        if (newVal) {
          this.iconList[0].isShow = newVal.chanye
          this.iconList[1].isShow = newVal.caiwu
          this.iconList[2].isShow = newVal.jingji
        } else {
          this.iconList[0].isShow = oldVal.chanye
          this.iconList[1].isShow = oldVal.caiwu
          this.iconList[2].isShow = oldVal.jingji
        }
      }
    }
  },
  data() {
    return {
      iconList: [{
        text: '产业运营概况',
        pdfText: '产业运营概况',
        isShow: false
      }, {
        text: '财务简报',
        pdfText: '财务简报',
        isShow: false
      }, {
        text: '经济运行报告',
        pdfText: '集团公司经济运行报告',
        isShow: false
      }],
    }
  },
  computed: {

  },
  created() {
    // console.log('111');
    
    if (sessionStorage.getItem("pdfFileName")) {
      setTimeout(() => {
        sessionStorage.removeItem('pdfFileName')
      }, 0)
    }
  },
  mounted() {
    // console.log('222');
    let isShowReportObj = JSON.parse(sessionStorage.getItem("isShowReportData"))
    if (isShowReportObj) {
      debugger
      this.iconList[0].isShow = isShowReportObj.chanye
      this.iconList[1].isShow = isShowReportObj.caiwu
      this.iconList[2].isShow = isShowReportObj.jingji
    }
  },
  methods: {
    isShowReport() {
      let month = this.dt && this.dt.replace(/-/g, "")
      getReportStatus(month).then((res) => {
        if (res && res.code == 200) {
          // console.log(res, '嗯哼');
          this.iconList[0].isShow = res.data.chanye
          this.iconList[1].isShow = res.data.caiwu
          this.iconList[2].isShow = res.data.jingji
          console.log(this.iconList[0], this.iconList[1], this.iconList[2]);
          // this.$forceUpdate()
        }
      }).catch((err) => {
        console.log(err);
      });

    },
    onPreviewPDF(item) {
      let dts = this.dt.split("-");
      let dt = item == '财务简报' ? `${dts[0]}年${Number(dts[1])}月` : `（${dts[0]}年${Number(dts[1])}月）`
      let pdfFileName = `${item}${dt}.pdf`;
      this.$router.push({ path: "/pdfPreview" });
      sessionStorage.setItem('pdfPreview', this.dt)
      sessionStorage.setItem('pdfFileName', pdfFileName)
    },
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  height: 100%;
  .content {
    padding: 0.3rem;
    box-sizing: border-box;
    min-height: 100%;
    // background: #fff;
    .itemBoxs {
      float: left;
      width: 30%;
      margin-right: 5%;
    }
    .itemBoxs:nth-child(3) {
      margin-right: 0 !important;
    }
    .itemBox {
      width: 100%;
      background: #fff;
      text-align: center;
      background: linear-gradient(
        141deg,
        #e2f5fc 0%,
        #ffffff 52%,
        #e7fafe 100%
      );
      box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
      height: 3rem;

      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    p {
      font-size: 0.3rem;
      font-weight: 600;
    }
    img {
      width: 55%;
      margin-bottom: 0.5rem;
    }

    .report_item:not(.report_item:nth-of-type(1)) {
      margin-left: 0.3rem;
    }
  }
}
</style>
