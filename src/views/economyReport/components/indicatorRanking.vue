<template>
  <div class="groupAll">
    <div class="content">
      <div class="ranke_content">
        <public-title :title="'营业收入'" unit="亿元"></public-title>
        <rank-chart :data="revenueData" :barItemColor="['#4481F6']"/>
      </div>
      <div class="ranke_content">
        <public-title :title="'利润总额'" unit="亿元"></public-title>
        <rank-chart :data="totalData" :barItemColor="['#20C3BE']"/>
      </div>
    </div>
  </div>
</template>

<script>
import { getJJYXData } from "@/http/api";
import publicTitle from '@/components/publicTitle.vue';
import rankChart from "./rankChart";
export default {
  name: 'indicatorRanking',
  components: {
    publicTitle,
    rankChart
  },
  props: {
    dt: {
      type: String,
      default: ""
    }
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      }
    }
  },
  computed: {

  },
  data() {
    return {
      indicatorTypeoOption: [
        { text: '营业收入', value: 'A01', unit: '万元' },
        { text: '利润总额', value: 'A02', unit: '万元' },
        { text: '净利润', value: 'A03' , unit: '万元'},
        { text: '归母净利润', value: 'A04', unit: '万元' },
        { text: '净资产收益率', value: 'A05', unit: '%' },
        { text: '营业现金比率', value: 'A06' , unit: '%'},
        { text: '全员劳动生产率', value: 'A07', unit: '万元/人' },
        { text: '资产负债率', value: 'A08', unit: '%' },
        { text: '研发投入强度', value: 'A09', unit: '%' }
      ],
      revenueData: [], //营业收入
      totalData: [] //利润总额
    }
  },
  created() {
    this.initData()
  },
  mounted() {

  },
  methods: {
    // 初始化页面
    initData() {
      if(!this.dt) return false;
      let params = {
        dt: this.dt
      }
      getJJYXData(params).then(res => {
        if(res.code == 200) {
          this.revenueData = res.data.revenueData;
          /**  临时代码-资产管理有数据删除即可-start  **/
            var sourceIndexes = [];
            res.data.totalData.forEach((item, index) => {
              if (item['biz_sctr'] == "资产管理") {
                sourceIndexes.push(index);
              }
            });
            // 遍历索引数组，移动元素
            sourceIndexes.forEach((sourceIndex) => {
              res.data.totalData[sourceIndex] = res.data.totalData.splice(res.data.totalData.length, 1, res.data.totalData[sourceIndex])[0];
            });
            // 过滤数组，去除 undefined 项
            res.data.totalData = res.data.totalData.filter((item) => {
              return item !== undefined || !!item;
            });
          /**  临时代码-资产管理有数据删除即可-end  **/
          this.totalData = res.data.totalData
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  .content {
    padding: 0.3rem 0.2rem;
    box-sizing: border-box;
    .ranke_content {
      & div:nth-last-child(1) {
        background: #fff;
      }
    }
    .ranke_content{
      ::v-deep .titleBox {
        margin-top: unset;
        padding-top: unset;
      }
    }
    .ranke_content:not(.ranke_content:nth-of-type(1)) {
      margin-top: 0.3rem;
      margin-bottom: 0.1rem;
    }
  }
}
</style>
