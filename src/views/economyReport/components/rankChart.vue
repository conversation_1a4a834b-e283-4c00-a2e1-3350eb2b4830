<template>
  <div :style="{position: 'relative',width: '100%',padding: '0 0.35rem',minHeight:'300px'}">
    <p v-show="data.length && unit" :style="{textAlign: 'right',color: '#666666',marginTop: '10px'}">单位：{{unit}}</p>
    <div ref="rankChartRef" :style="{width: '100%',height: 'calc(100vh - 200px)'}"></div>
    <div v-show="!data.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>

<script>
import { findLargest, formatAmount } from '@/utils/chart';
export default {
  name: 'RankChart',
  props: {
    type: {
      type: String,
      default: ""
    },
    unit: {
      type: String,
      default: ""
    },
    barItemColor: {
      type: Array,
      default: () => {
        return ['#4481F6']
      }
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        this.chartData = []
        this.categories = []
        if (data) {
          this.showAxisLine = false;
          for (var i = 0; i < data.length; i++) {
            let val = data[i]['tgt_val'];
            if(val < 0) this.showAxisLine = true;
            this.categories.push(data[i]['biz_sctr'])
            this.chartData.push({
              value: val || 0,
              label: {position: val >=0 ? 'right' : 'right'},
              itemStyle: {
                color: this.barItemColor[0],
                borderRadius: val >=0 ? [0, 3, 3, 0] : [3, 0, 0, 3],
              }
            })
          }
          if (this.categories) this.labelLargest = findLargest(this.categories);
          this.initEcharts();
        }
      }
    },
  },
  data() {
    return {
      categories: [],
      chartData: [],
      labelLargest: 0,
      showAxisLine: false,
      myChart: null
    }
  },
  methods: {
    initEcharts() {
      // 清除 图表实例
      if(this.myChart != null && this.myChart !== '' && this.myChart !== undefined) {
        this.myChart.dispose(this.$refs.rankChartRef)
      }
      // 在组件中直接使用this.$echarts来进行操作
      this.myChart = this.$echarts.init(this.$refs.rankChartRef);
      let maxNumber = 0;
      if(this.chartData.length) {
        maxNumber = this.chartData.reduce((a,b)=>Number(a)+Number(b.value),0);  //累加所有值
        let maxValue = Math.max(...this.chartData.map(item =>{return item.value}));
        if((maxValue / maxNumber) > 0.6 ) maxNumber = maxNumber * 3;
        if((maxValue / maxNumber) < 0.6 ) maxNumber = maxValue * 1.5;
      }
      // 指定图表的配置项和数据
      var option = {
        title: {
          show: false,
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          extraCssText:'z-index:2',
          axisPointer: {
            type: 'shadow'
          }
        },
        tooltip: {
          trigger: 'axis',
          extraCssText:'z-index:2',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            console.log(params,'sss')
              let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`
              if (params) {
                return `
                <div>${circle}${params[0]['color']}"></span> ${params[0]['name']} ${formatAmount(params[0]['value'],1)}</div>
              `
              }
          }
        },
        legend: {},
        grid: {
          top: '5%',
          left: '1%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          data: this.categories,
          inverse: true, //如果数据数组倒置排序，加上此代码
          axisLine: {
            show: this.showAxisLine,
            lineStyle: {
              color: '#999999'
            },
          },
          axisTick: {
            show: false
          },
          splitLine: { //修改背景线条样式
            show: false
          },
          offset: 1,
          axisLabel: {
            color: "#222222",
            fontWeight: '400',
            fontSize: '18px',
            // align: 'left',
            // margin: 140,
            formatter: (params, index) => {
              let label = "";
              if (this.labelLargest > 6) {
                if (params.length > 6) {
                  label = `${params.substr(0, 6)}...`
                } else {
                  label = `${params.padEnd(6, '\u3000')}`
                  label = `${label.padEnd(9, '\u0020')}`
                }
              } else if (this.labelLargest <= 6) {
                label = params.length < this.labelLargest ? params.padEnd(this.labelLargest, '\u3000') : params;
              }
              return [`{a|${index + 1}}  ${label}`].join('\n')
            },
            rich: {
              a: {
                color: "#373F6D",
                fontSize: '18px',
                align: 'left',
                width: 12
              }
            }
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: "14px",
            showBackground: true,
            data: this.chartData,
            backgroundStyle: {
              color: '#F2F6FD'
            },
            label: {
              show: true,        // 开启显示
              distance: 5,     // 条柱之间的距离
              // position: 'right',    // 右侧right显示
              // textStyle: {        // 数值样式
              color: '#373F6D',
              fontSize: '18px',
              // },
              formatter: (params, index) => {
                const reg = /(\d)(?=(?:\d{3})+$)/g;
                if(this.unit == '%') {
                  return params.value.toString().replace(reg, '$1,') + '%'; //占比
                } else {
                  return formatAmount(params.data.value,1)
                }     
              }
            },
          }
        ]
      };
      if(maxNumber > 0) {
        option['xAxis']['max'] = maxNumber;
      } else {
        option['xAxis']['max'] = Math.abs(maxNumber) * 3;
        option['xAxis']['min'] = maxNumber * 2;
      }
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option, true);
      // 自适应高度
      let autoHeight = this.chartData.length * 35 + 50;
      this.myChart.getDom().style.height = autoHeight + "px";
      this.myChart.resize(); 
    }
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
</style>
