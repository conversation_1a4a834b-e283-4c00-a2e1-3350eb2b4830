<template>
  <div class="zbBoard">
    <div class="tabsBox" >
      <!-- tab区域 -->
      <van-tabs v-model="active" swipeable>
        <van-tab v-for="(item,index) in tabList" :title="item" :key="index" />
      </van-tabs>
      <!-- 内容区域 -->
      <div class="tabContent">
        <div v-if="active=='0'">
          <group-all @toPage="toPage" :dt="dt"/>
        </div>
        <div v-if="active=='1'">
          <indicator-ranking @toPage="toPage" :dt="dt"/>
        </div>
          <div v-if="active=='2'">  
          <second-unit :dt="dt"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import groupAll from '@/views/zbBoard/components/groupAll'
import indicatorRanking from '@/views/zbBoard/components/indicatorRanking'
import secondUnit from '@/views/zbBoard/components/secondUnit'
export default {
  name: 'economyReport',
   components: {
    groupAll,
    indicatorRanking,
    secondUnit
  },
  props: {
    dt: {
      type: String,
      default: ""
    }
  },
  watch: {
    active: {
      deep: true,
      handler(newVal, oldVal) {
        let el = document.querySelector('.tabsBox .tabContent');
        el.scrollTo(0,0)
      }
    }
  },
  data() {
    return {
      active: 0,
      columns: [
      ],
      customFieldName: {
        text: 'text',
        children: 'children',
      },
      tabList: [
        "集团整体",
        "指标排名",
        "二级企业"
      ],
    }
  },
  created() {
    
  },
  mounted() {
    this.$nextTick(() => {
      let tabHeight = document.querySelector('.tabsBox .van-tabs__wrap').offsetHeight;
      // let navHeight = document.querySelector(".van-tabbar").offsetHeight;
      let el = document.querySelector('.tabsBox .tabContent');
      el.style.height = `calc(100% - ${tabHeight}px)`;
      // el.style.paddingBottom = `${navHeight}px`;
      // el.style.paddingBottom = `0.35rem`;
      el.style.overflow = 'scroll';
    })
  },
  methods: {
    toPage(val) {
      this.active = val;
    }
  }
}
</script>

<style lang="scss" scoped>
.zbBoard {
  height: 100%;
  //日期组件
  .dateBox {
    background: #fff;
    padding: 0.2rem 0 0.3rem;
    ::v-deep .van-cell {
      width: 2.7rem;
      height: 0.6rem;
      line-height: 0.58rem;
      padding: 0px 10px;
      font-size: 0.28rem;
      border-radius: 0.08rem;
      border: 0.01rem solid #3e7bfa;
      background-color: #F1F6FF;
      color: rgba(62, 123, 250, 1);
      margin: 0 auto;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      position: relative;
      ::after {
        content: "";
        position: absolute;
        color: red;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("../../../assets/img/rlIcon.png") no-repeat;
        background-size: cover;
        display: block;
        width: 0.28rem;
        height: 0.28rem;
      }
    }
    ::v-deep [class*="van-hairline"]::after {
      border: none;
    }
    ::v-deep .van-field__control:read-only {
      color: rgba(62, 123, 250, 1);
    }
    ::v-deep .van-cell-group {
      background-color: #fff;
      // position: inherit;
    }
    ::v-deep .van-field__control::placeholder {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
    ::v-deep li.van-picker-column__item {
      color: rgba(34, 34, 34, 1);
      font-size: 0.3rem;
    }
    ::v-deep li.van-picker-column__item.van-picker-column__item--selected {
      color: rgba(62, 123, 250, 1);
      font-size: 0.4rem;
    }

    ::v-deep
      li.van-picker-column__item.van-picker-column__item--selected
      .van-ellipsis {
      background-color: #f1f6ff;
      width: 2.8rem;
      text-align: center;
      padding: 0.1rem 0;
    }
    ::v-deep .van-picker__confirm {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
  }
  .tabsBox {
    height: 100%;
    .van-tabs {
      z-index: 99;
    }
    //tab滑动切换
    ::v-deep .van-tab {
      color: rgba(17, 17, 17, 1);
      font-size: 0.36rem;
    }
    ::v-deep .van-tab--active {
      color: #111;
      font-size: 0.36rem;
    }
    ::v-deep .van-tabs__line {
      background-color: #3e7bfa;
      width: 1.8rem;
      height: 0.05rem;
      border-radius: 0;
    }
    ::v-deep .van-tabs--line .van-tabs__wrap {
      box-shadow: 0px 0.03rem 0.07rem 0 #e6e6e6;
    }
    .tabContent {
      
    }
  }
}
</style>
