<template>
	<div class="wrap">
		<div id="iframeContain" v-show="!errLoading"></div>
		<div v-show="errLoading" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
			<img src="@/assets/img/noData.png" :style="{width: '100%'}">
			<p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
		</div>
	</div>
</template>

<script>
export default {
  name: "pdfPreview",
  data() {
    return {
		pdfh5: null,
		pdfFileName: "",
		errLoading: false,
    };
  },
  mounted() {
		if(sessionStorage.getItem("pdfFileName")) {
			this.pdfFileName = sessionStorage.getItem("pdfFileName");
		}
		this.initPage()
	},
	created(){
		this.$loading.show()
	},
	beforeDestroy(){
    this.$loading.hide()
  },
	methods: {
		initPage() {
			//console.log(window.location.origin,'哦这是url');
			
			this.pdfh5 = new Pdfh5("#iframeContain", {
				pdfurl: this.$route.query.pdfurl || `./static/file/${this.pdfFileName}`,
				cMapUrl: `${window.location.origin}/hr_mobile`,
				loading: false // 如果有这个选项的话
			});
			  //监听完成事件
			this.pdfh5.on("error", (status, msg, time) => {
				this.errLoading = true;
			})
			this.pdfh5.on('ready‌',(status, msg, time)=>{
				this.$loading.hide()
			})
			this.pdfh5.on('complete',(status, msg, time)=>{
				this.$loading.hide()
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap {
	width: 100%;
	height: 100vh;
	background: #FFF;
	#iframeContain {
		width: 100%;
		height: 100%;
	}
}


</style>