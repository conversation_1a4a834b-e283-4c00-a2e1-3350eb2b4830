<template>
  <div class="zbBoard">
    <div class="dateBox_ix">
      <van-cell-group>
        <van-field style="text-align:center" readonly clickable placeholder="请选择日期" :value="dateValue" @click="showPicker = true" />
        <van-popup v-model="showPicker" round position="bottom" :lazy-render='false' >
          <van-picker ref="dtPickerRef" title="请选择日期" show-toolbar :columns="columns" :columns-field-names="customFieldName" @confirm="yearConfirm" @cancel="cancel" />
        </van-popup>
      </van-cell-group>
    </div>
    <div class="tabsBox_ix" >
      <!-- tab区域 -->
      <!-- <van-tabs v-model="active" swipeable>
        <van-tab v-for="(item,index) in tabList" :title="item" :key="index" />
      </van-tabs> -->
      <!-- 内容区域 -->
      <div class="tabContent_ix">
        <group-all @toPage="toPage" :dt="dt" v-if="active=='0'"/>
        <indicator-ranking @toPage="toPage" :dt="dt" v-if="active=='1'"/>
        <second-unit :dt="dt" :isShowReportData="isShowReportData" v-if="active=='2'"/>
      </div>
      <van-tabbar v-model="active">
        <van-tabbar-item v-for="(item,index) in tabList" :key="index" @click="handleClick(index)">
          <template #icon="props">
            <img v-if="index == '0'" :src="props.active ? navLeftActive : navLeft"/>
            <img v-else-if="index == '1'" :src="props.active ? navCenterActive : navCenter"/>
            <img v-else :src="props.active ? navRightActive : navRight"/>
          </template>
          <span class="navBigText">{{ item }}</span>
        </van-tabbar-item>
      </van-tabbar>
    </div>
  </div>
</template>

<script>
import navLeft from "@/assets/img/economyReportIcon/navLeft.png";
import navLeftActive from "@/assets/img/economyReportIcon/navLeftActive.png";
import navCenter from "@/assets/img/economyReportIcon/navCenter.png";
import navCenterActive from "@/assets/img/economyReportIcon/navCenterActive.png";
import navRight from "@/assets/img/economyReportIcon/navRight.png";
import navRightActive from "@/assets/img/economyReportIcon/navRightActive.png";
import { getJjMonth,getReportStatus } from "@/http/api";
import groupAll from './components/groupAll'
import indicatorRanking from './components/indicatorRanking'
import secondUnit from './components/secondUnit'
import axios from 'axios';
export default {
  name: 'zbBoard',
  components: {
    groupAll,
    indicatorRanking,
    secondUnit
  },
  watch: {
    active: {
      deep: true,
      handler(newVal, oldVal) {
        let el = document.querySelector('.tabsBox_ix .tabContent_ix');
        el.scrollTo(0,0)
         this.isShowReport()
      }
    }
  },
  data() {
    return {
      navLeft,
      navLeftActive,
      navCenter,
      navCenterActive,
      navRight,
      navRightActive,
      showImg:"主要指标",
      dateValue: "",
      dt: "",
      isShowReportData:{
        caiwu:0,
        chanye:0,
        jingji:0,
      },
      showPicker: false,
      active: 0,
      columns: [
        // // 第一列
        // {
        //   values: ['2021', '2022', '2023', '2024', '2025'],
        //   defaultIndex: 2,
        // },
        // // 第二列
        // {
        //   values: ['1月', '1-2月', '1-3月', '1-4月', '1-5月', '1-6月', '1-7月'],
        //   defaultIndex: 1,
        // },
      ],
      customFieldName: {
        text: 'text',
        children: 'children',
      },
      tabList: [
        "主要指标",
        "重点业务板块",
        "查看报告"
      ],
      monthList: [
        {text: '1月'},{text: '1-2月'},{text: '1-3月'},{text: '1-4月'},{text: '1-5月'},{text: '1-6月'},
        {text: '1-7月'},{text: '1-8月'},{text: '1-9月'},{text: '1-10月'},{text: '1-11月'},{text: '1-12月'}
      ]
    }
  },
  created() {
    this.createDateOptions();  
  },
  mounted() {
    this.$nextTick(() => {
      let dateBoxHeight = document.querySelector('.dateBox_ix').offsetHeight;
      let vanTabHeight = document.querySelector('.tabsBox_ix .van-tabbar').offsetHeight;
      let el = document.querySelector('.tabsBox_ix .tabContent_ix');
      el.style.height = `calc(100vh - ${dateBoxHeight}px`;
      el.style.paddingBottom = `${vanTabHeight}px`;
      el.style.overflow = 'scroll';
    })
  },
  methods: {
    isShowReport(){
      let month=this.dt && this.dt.replace(/-/g,"")
       getReportStatus(month).then((res) => {
        if (res && res.code == 200) {
          console.log(res,'嘿哼嘿');
          this.isShowReportData.caiwu=res.data.caiwu
          this.isShowReportData.chanye=res.data.chanye
          this.isShowReportData.jingji=res.data.jingji
          sessionStorage.setItem('isShowReportData',  JSON.stringify(this.isShowReportData))
            // this.$forceUpdate()
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    //年选择器
    yearConfirm(value) {
      let month = this.monthList.findIndex(item => {
        return item.text == value[1] 
      }) + 1;
      if(month < 10) month = `0${month}`
      this.dt = `${value[0]}-${month}`
      this.dateValue = `${value[0]}年 ${value[1]}`
      this.showPicker = false;
    },
    //点击取消按钮时触发的事件
    cancel() {
      this.showPicker = false
    },
    toPage(val) {
      this.active = val;
     
    },
    async createDateOptions() {
      let columns = [];
      await this.handleGetJjMonth().then(res => {
        columns = res;
      }).catch(err => {
        columns = this.createDtOption()
      })
      this.columns = columns;
      if(sessionStorage.getItem('pdfPreview')) { //查看报告页面返回
        let dtData = sessionStorage.getItem('pdfPreview').split("-");
        let curMotn = 0;
        let curYear = 0;
        for(let i = 0; i < columns.length; i++) {
          if(Number(columns[i]['text']) == Number(dtData[0])) {
            curYear = i;
            for(let j = 0; j < columns[i]['children'].length; j++) {
              if(columns[i]['children'][j]['text'] == this.monthList[Number(dtData[1] - 1)]['text']) {
                curMotn = j;
              }
            }
          }
        }
        this.dt = sessionStorage.getItem('pdfPreview');
        this.dateValue = `${dtData[0]}年 ${columns[curYear]['children'][curMotn]['text']}`;
        this.$nextTick(() => {
          this.$refs.dtPickerRef.setColumnIndex(0,Number(curYear))
          this.$refs.dtPickerRef.setColumnIndex(1,Number(curMotn))
        })
        this.active = 2;
        setTimeout(() => {
          sessionStorage.removeItem('pdfPreview')
        },0)
      } else {
        let maxMonthText = columns[0]['children'][columns[0]['children'].length - 1]['text'];
        let maxYear = columns[0]['text'];
        let maxMonth = this.monthList.findIndex(item => {
          return item.text == maxMonthText
        }) + 1;
        this.dateValue = `${maxYear}年 ${maxMonthText}`;
        this.dt = `${maxYear}-${maxMonth < 10 ? '0' + maxMonth : maxMonth}`;
        this.$nextTick(() => {
          this.$refs.dtPickerRef.setColumnIndex(1,maxMonth - 1)
        })
      }
    },
    createDtOption() {
      let columns = [];
      let maxYear = new Date().getFullYear();
      let maxMonth = new Date().getMonth() + 1;
      let curMonth = this.monthList.slice(0,maxMonth)
      let minYear = '2000';
      for(let i = maxYear; i >= minYear; i--) {
        let obj = {
          text: i,
          children: i == maxYear ? curMonth : this.monthList
        }
        columns.push(obj)
      }
      return columns
    },
    handleGetJjMonth() {
      return new Promise((resolve,reject) => {
        getJjMonth().then(res => {
          if(res.code == 200) {
            let years = [];
            let columns = [];
            res.data.shift()//此操作是为了删除掉202306的数据
          //  res.data.pop()//此操作是为了删除掉202312的数据,,,之后如果需要12月的数据需要显示出来，此句删除
          //  console.log(res.data,'昂昂郇');
            for(let i = 0;i < res.data.length;i++) {
              let cjdt = res.data[i]['label'].split("年")[0];
              if(!years.includes(cjdt)) years.push(cjdt)
            }
            years = years.sort((a,b) => {return b - a}); //降序排列年份
            for(let i = 0;i < years.length;i++){
              let monthList = [];
              for(let j = 0;j < res.data.length;j++){
                if(res.data[j]['label'].indexOf(years[i])!=-1){
                  let dtValue =  `${res.data[j]['value'].slice(0,4)}-${res.data[j]['value'].slice(4,6)}-${res.data[j]['value'].slice(6,8)}`
                  monthList.push({
                    text: this.monthList[new Date(dtValue).getMonth()]['text']
                  })
                  
                }
              }
              // monthList = monthList.shift()
              
              let obj = {
                text: years[i],
                children: monthList
              }
              columns.push(obj)
            }
            resolve(columns)
          }else{
            reject([])
          }
        }).catch(err => {
          reject([])
        })
      })
    },
    handleClick(index) {
      if(index==0){
        this.showImg='主要指标'
      }else{
        this.showImg='重点业务板块'
      }
      //点击切换标签页，回到顶部
       window.scrollTo(0,0)
    },
  }
}
</script>

<style lang="scss" scoped>
.zbBoard {
  background: #f6f7f9;
  height: 100vh;
  font-family: "微软雅黑";
  //日期组件
  .dateBox_ix {
    background: #fff;
    padding: 0.2rem 0 0.3rem;
    ::v-deep .van-cell {
      width: 2.7rem;
      height: 0.6rem;
      line-height: 0.58rem;
      padding: 0px 10px;
      font-size: 0.28rem;
      border-radius: 0.08rem;
      border: 0.01rem solid #3e7bfa;
      background-color: #F1F6FF;
      color: rgba(62, 123, 250, 1);
      margin: 0 auto;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      position: relative;
      ::after {
        content: "";
        position: absolute;
        color: red;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("../../assets/img/rlIcon.png") no-repeat;
        background-size: cover;
        display: block;
        width: 0.28rem;
        height: 0.28rem;
      }
    }
    ::v-deep [class*="van-hairline"]::after {
      border: none;
    }
    ::v-deep .van-field__control:read-only {
      color: rgba(62, 123, 250, 1);
    }
    ::v-deep .van-cell-group {
      background-color: #fff;
      // position: inherit;
    }
    ::v-deep .van-field__control::placeholder {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
    ::v-deep li.van-picker-column__item {
      color: rgba(34, 34, 34, 1);
      font-size: 0.3rem;
    }
    ::v-deep li.van-picker-column__item.van-picker-column__item--selected {
      color: rgba(62, 123, 250, 1);
      font-size: 0.4rem;
    }

    ::v-deep
      li.van-picker-column__item.van-picker-column__item--selected
      .van-ellipsis {
      background-color: #f1f6ff;
      width: 2.8rem;
      text-align: center;
      padding: 0.1rem 0;
    }
    ::v-deep .van-picker__confirm {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
  }
  .tabsBox_ix {
    .van-tabs {
      z-index: 99;
    }
    //tab滑动切换
    ::v-deep .van-tab {
      color: rgba(17, 17, 17, 1);
      font-size: 0.36rem;
    }
    ::v-deep .van-tab--active {
      color: #111;
      font-size: 0.36rem;
    }
    ::v-deep .van-tabs__line {
      background-color: #3e7bfa;
      width: 1.8rem;
      height: 0.05rem;
      border-radius: 0;
    }
    ::v-deep .van-tabs--line .van-tabs__wrap {
      box-shadow: 0px 0.03rem 0.07rem 0 #e6e6e6;
    }

    ::v-deep .van-tabbar {
      height: 1.3rem;
      img {
        height: 0.6rem;
      }
      .navBigText {
        font-size: 0.27rem;
      }
    }
  }
}
</style>
