<template>
  <div class="container" :class="rankTab == 'progress' ? 'progress-container' : 'retrogress-container'">
    <div class="tab">
      <div class="tab-inner">
        <span :class="rankTab == 'progress' ? 'actived-tab' : ''" @click="handleTabChange('progress')">
          红榜
        </span>
        <span :class="rankTab == 'retrogress' ? 'actived-tab' : ''" @click="handleTabChange('retrogress')">
          黑榜
        </span>
      </div>
    </div>
    <div class="content">
      <div class="index-list">
        <div v-for="item in indexList" class="index-item" :class="item.value == tgt_no ? 'index-item-actived' : ''"
          :key="item.value" @click="handleIndexChange(item.value)">
          {{ item.label }}
        </div>
      </div>
      <p class="desc" @click="tableVisible = true">
        {{
          tgt_no == "A01" ? (
            "26户二级企业中，中电金投和中电蓝海不参与排名"
          ) : (
            rankTab == "progress" ? "26户二级企业参与排名，亏损企业不进入红榜" : "26户二级企业参与排名"
          )
        }}
        <img src="../../../../assets/img/ranking/icon_enter.png" />
      </p>

      <template v-if="rankTab == 'progress'">
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="value" title="完成值" :data="progress.value.slice(0, 8)" />
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="ratio" title="达成率" :data="progress.index.slice(0, 8)" />
      </template>

      <template v-if="rankTab == 'retrogress'">
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="value" title="完成值" :data="retrogress.value.slice(0, 8)" />
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="ratio" title="达成率" :data="retrogress.index.slice(0, 8)" />
      </template>

    </div>
    <van-popup closeable v-model="tableVisible" position="center"
      :style="{ width: '88%', padding: '0.5rem', borderRadius: '5px', maxHeight: '80vh', overflow: 'auto' }"
      @close="tableVisible = false">
      <h2 :style="{ color: '#222222', fontSize: '0.32rem', fontWeight: '600', marginBottom: '0.2rem' }">参与排名的二级企业名单</h2>
      <div class="company-list-table"
        :class="rankTab == 'progress' ? 'company-list-table-progress' : 'company-list-table-retrogress'">
        <div class="company-list-table-header">
          <div class="company-list-table-row">
            <div class="company-list-table-col1">序号</div>
            <div class="company-list-table-col2">二级企业名称</div>
          </div>
        </div>
        <div class="company-list-table-body" :key="tgt_no + rankTab">
          <div v-for="item in sortList" :key="item.index" class="company-list-table-row">
            <div class="company-list-table-col1">{{ item.index }}</div>
            <div class="company-list-table-col2">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import mixin from '../mixin'

export default {
  mixins: [mixin],
  data() {
    return {
      dsp_typ_cd_1: "2",
      dsp_typ_cd_2: "3"
    }
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
