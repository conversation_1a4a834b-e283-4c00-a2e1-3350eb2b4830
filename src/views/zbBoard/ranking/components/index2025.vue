<template>
  <div class="container" :class="rankTab == 'progress' ? 'progress-container' : 'retrogress-container'">
    <div class="tab">
      <div class="tab-inner">
        <span :class="rankTab == 'progress' ? 'actived-tab' : ''" @click="handleTabChange('progress')">
          红榜
        </span>
        <span :class="rankTab == 'retrogress' ? 'actived-tab' : ''" @click="handleTabChange('retrogress')">
          黑榜
        </span>
      </div>
    </div>
    <div class="content">
      <div class="index-list">
        <div v-for="item in indexList" class="index-item" :class="item.value == tgt_no ? 'index-item-actived' : ''"
          :key="item.value" @click="handleIndexChange(item.value)">
          {{ item.label }}
        </div>
      </div>
      <p class="desc" @click="tableVisible = true">
        {{
          tgt_no == "A01" ? (
            "26户二级企业中，中电金投不参与排名"
          ) : (
            rankTab == "progress" ? "26户二级企业参与排名，亏损企业不进入红榜" : "26户二级企业参与排名"
          )
        }}
        <img src="../../../../assets/img/ranking/icon_enter.png" />
      </p>

      <template v-if="rankTab == 'progress'">
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="value" title="完成值" :data="progress.value.slice(0, 8)" />
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="ratio" title="同比变化" secondTitle="去年同期" :hasTip="false" :unit="otherUnit"
          :data="progress.index.slice(0, 8)" />
      </template>

      <template v-if="rankTab == 'retrogress'">
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="value" title="完成值" :data="retrogress.value.slice(0, 8)" />
        <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab" type="ratio" title="同比变化" secondTitle="去年同期" :hasTip="false" :unit="otherUnit"
          :data="retrogress.index.slice(0, 8)" />
      </template>

    </div>
    <van-popup closeable v-model="tableVisible" position="center"
      :style="{ width: '88%', padding: '0.5rem', borderRadius: '5px', maxHeight: '80vh', overflow: 'auto' }"
      @close="tableVisible = false">
      <h2 :style="{ color: '#222222', fontSize: '0.32rem', fontWeight: '600', marginBottom: '0.2rem' }">参与排名的二级企业名单</h2>
      <div class="company-list-table"
        :class="rankTab == 'progress' ? 'company-list-table-progress' : 'company-list-table-retrogress'">
        <div class="company-list-table-header">
          <div class="company-list-table-row">
            <div class="company-list-table-col1">序号</div>
            <div class="company-list-table-col2">二级企业名称</div>
          </div>
        </div>
        <div class="company-list-table-body" :key="tgt_no + rankTab">
          <div v-for="item in sortList" :key="item.index" class="company-list-table-row">
            <div class="company-list-table-col1">{{ item.index }}</div>
            <div class="company-list-table-col2">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import mixin from '../mixin'
import { Decimal } from 'decimal.js'

import { getKbData } from "@/http/api";
import { formatNum } from "@/utils"
export default {
  mixins: [mixin],
  data() {
    return {
      dsp_typ_cd_1: '6',
      dsp_typ_cd_2: "4",
      otherUnit: ""
    }
  },
  created() {
    const list = { A01: '6', A02: '5' }
    const unitList = { A01: '', A02: '亿元' }
    this.dsp_typ_cd_1 = list[this.tgt_no]
    this.otherUnit = unitList[this.tgt_no]
  },
  watch: {
    tgt_no(newVal) {
      const list = { A01: '6', A02: '5' }
      const unitList = { A01: '', A02: '亿元' }
      this.otherUnit = unitList[newVal]
      this.dsp_typ_cd_1 = list[newVal]
    },
    immediate: true
  },
  methods: {
    updateData(newParams = {}) {
      const params = {
        dt: this.dt,
        mapperInterface: ["rbczbpm"],
        moreChooseType: "1",
        tgt_no: this.tgt_no,
        ...newParams
      }

      // 完成值数据
      getKbData({
        ...params,
        dsp_typ_cd: "1"
      }).then(res => {
        if (res.code == 200 && res.data.rbczbpm) {
          const data = res.data.rbczbpm.map(item => {
            const value = formatNum(item.dsp_val)

            return {
              ...item,
              name: item.entp_nm,
              value: value
            }
          });
          this.retrogress.value = data
          this.progress.value = [...data].reverse()
        }
        // 同比变化数据
        getKbData({
          ...params,
          dsp_typ_cd: this.dsp_typ_cd_1
        }).then(res => {
          if (res.code == 200 && res.data.rbczbpm) {
            const rbczbpm = res.data.rbczbpm.filter(item => {
              return item.dsp_val
            })
            const ratioData = rbczbpm.map(item => {
              let dsp_val;
              if (this.tgt_no == 'A01') {
                dsp_val = formatNum(new Decimal(item.dsp_val).mul(new Decimal(100)), 1)
                if (Number(dsp_val) > 0) {
                  dsp_val = `+${dsp_val}`
                }
              } else if (this.tgt_no == 'A02') {
                dsp_val = formatNum(item.dsp_val)
                if (dsp_val.endsWith('0')) {
                  dsp_val = dsp_val.slice(0, -1);
                }
                if (dsp_val.endsWith('.')) {
                  dsp_val = dsp_val + "0"
                }

                if (Number(dsp_val) > 0) {
                  dsp_val = `+${dsp_val}`
                }
              }
              return {
                ...item,
                dsp_val: dsp_val,
              }

            })

            // 达成目标
            getKbData({
              ...params,
              dsp_typ_cd: this.dsp_typ_cd_2
            }).then(res => {
              if (res.code == 200 && res.data.rbczbpm) {
                const data = ratioData.map(ratioItem => {
                  const value = ratioItem.dsp_val
                  const target = res.data.rbczbpm.find(item => item.entp_nm == ratioItem.entp_nm)

                  return {
                    ...ratioItem,
                    name: ratioItem.entp_nm,
                    value: value,
                    addValue: target ? formatNum(target.dsp_val) : '-'
                  }
                });
                this.retrogress.index = data
                this.progress.index = [...data].reverse()
              }
            })
          }
        })

      })
    },
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
