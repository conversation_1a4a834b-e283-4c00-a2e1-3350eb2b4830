<template>
  <div
    class="rank-list-container"
    :class="rankTab == 'progress' ? 'rank-list-container-progress' : 'rank-list-container-retrogress'"
    :style="{marginBottom: type == 'ratio' ? '0.1rem' : ''}"
  >
    <div v-if="data && data.length > 0" @click="handleShowModal" class="title">
      <span class="dot" />
      <span class="title-text">
        {{ title }}
        <img
          v-if="type == 'ratio'"
          src="../../../assets/img/ranking/icon_remark.png"
          alt=""
        >
      </span>
      
    </div>
    <div class="top">
      <div
        v-for="(item, index) in data.slice(0, 3)"
        :key="index"
        class="top-card"
        @click="handleCompanyClick(item.entp_no)"
      >
        <template v-if="rankTab == 'progress'">
          <img v-if="index == 0" src="../../../assets/img/ranking/progress_one.png" alt="">
          <img v-else-if="index == 1" src="../../../assets/img/ranking/progress_two.png" alt="">
          <img v-else src="../../../assets/img/ranking/progress_three.png" alt="">
        </template>
        <template v-else>
          <img v-if="index == 0" src="../../../assets/img/ranking/retrogress_one.png" alt="">
          <img v-else-if="index == 1" src="../../../assets/img/ranking/retrogress_two.png" alt="">
          <img v-else src="../../../assets/img/ranking/retrogress_three.png" alt="">
        </template>
        
        <span class="top-card-name" :class="item.name.length >= 5 ? 'name-length-s' : ''">
          <span class="xinan" v-if="item.name.includes('电子六所')">
            <span>中国信安</span>
            <span>（电子六所）</span>
          </span>
          <template v-else>
            {{ item.name }}
          </template>
        </span>
        <span class="top-card-title">{{ title }}</span>
        <span class="top-card-value">{{ judgeViewEnabled(item, item.value) }}</span>
        <span class="top-card-unit">{{ type == 'value' ? '亿元' : '%' }}</span>
        <span v-if="type == 'ratio'" class="top-card-add">预算目标 <span>{{ judgeViewEnabled(item, item.addValue) }}</span> 亿元</span>
      </div>
    </div>
    <div
      class="list"
      :style="{marginTop: type == 'ratio' ? '0.5rem' : '' }"
      :class="type == 'ratio' ? 'hide-bottom-border' : ''"
    >
      <div
        v-for="(item, index) in data.slice(3)"
        :key="index"
        class="list-item"
        @click="handleCompanyClick(item.entp_no)"
      >
        <span>
          <span class="list-item-index">{{ index + 1 + 3 }}</span>
          <span class="list-item-name">
            <span>{{ item.name }}</span>
            <span v-if="type == 'ratio'" class="list-item-name-bottom">预算目标 {{ judgeViewEnabled(item, item.addValue) }} 亿元</span>
          </span>
        </span>
        <span class="list-item-bottom">
          <span>{{ title }}</span>
          <span>
            <span class="list-item-value">{{ judgeViewEnabled(item, item.value) }}</span>
            <span class="list-item-unit">{{ type == 'value' ? '亿元' : '%'}}</span>
          </span>
        </span>
      </div>
    </div>
    <item-tip
      :type="'card'"
      :show="remarkVisible"
      @handleClose="remarkVisible=false"
      :title="'达成率计算公式'"
      :content="content"
      :showCircle="false"
      closeable
    />
  </div>
</template>

<script>
import itemTip from '@/components/itemTip.vue';

export default {
  components: {
    itemTip
  },
  data() {
    return {
      remarkVisible: false,
      content: [
        '预算目标>0，达成率=完成值/预算目标',
        '预算目标<0，达成率=2-完成值/预算目标',
        '预算目标=0，完成值≥0，达成率=100.0%',
        '预算目标=0，完成值<0，达成率=0.0%'
      ]
    }
  },
  props: {
    title: String,
    data: {
      type: Array,
      default: []
    },
    rankTab: String,
    type: String,
    judgeViewEnabled: Function,
    judgeDetailsEnabled: Function
  },
  methods: {
    handleShowModal() {
      if (this.type != 'ratio') return
      this.remarkVisible = true
    },
    handleCompanyClick(entp_no) {
      if (!this.judgeDetailsEnabled(entp_no)) return
      this.$router.back()
      this.$nextTick(() => {
          location.href = `/hr_mobile/zbBoard?active=2&currentCompany=${entp_no}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rank-list-container {
  margin: 0.2rem 0;
  margin-bottom: 0.6rem;

  &.rank-list-container-progress {
    .title .dot {
      background: #f83a3e;
    }

    .list-item-index {
      color: #C94A4A;
      background: url(../../../assets/img/ranking/progress_index_border.png);
    }

    .top-card {
      &:nth-child(1) {
        background-image: url(../../../assets/img/ranking/progress_one_bg.png);
      }
      &:nth-child(2) {
        background-image: url(../../../assets/img/ranking/progress_two_bg.png);
      }
      &:nth-child(3) {
        background-image: url(../../../assets/img/ranking/progress_three_bg.png);
      }
    }
  }

  &.rank-list-container-retrogress {
    .title .dot {
      background: #EFB653;
    }

    .list-item-index {
      color: #C94A4A;
      background: url(../../../assets/img/ranking/retrogress_index_border.png);
    }

    .top-card {
      &:nth-child(1) {
        background-image: url(../../../assets/img/ranking/retrogress_one_bg.png);
      }
      &:nth-child(2) {
        background-image: url(../../../assets/img/ranking/retrogress_two_bg.png);
      }
      &:nth-child(3) {
        background-image: url(../../../assets/img/ranking/retrogress_three_bg.png);
      }
    }
  }

  .title {
    display: flex;
    align-items: center;
    font-size: 0.3rem;
    margin-bottom: 0.5rem;

    .dot {
      width: 0.14rem;
      height: 0.14rem;
      border-radius: 100%;
      margin-right: 0.15rem;
    }

    .title-text {
      position: relative;
      img {
        position: absolute;
        top: -0.3rem;
        right: -0.5rem;
        width: 0.4rem;
        height: 0.4rem;
      }
    }
  }

  .top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 160px;

    .top-card {
      // width: 1.8rem;
      width: 34%;
      // min-height: 2.9rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      border: 2px solid #e9e9e9;
      border-top: 0;
      padding-top: 0.23rem;
      background-color: #fff;
      border-radius: 8px;
      background-size: auto 90%;
      background-position: center -10px;
      background-repeat: no-repeat;
      padding-bottom: 0.1rem;

      &>img {
        width: 0.60rem;
        height: auto;
        margin-bottom: 0.15rem;
      }

      &:nth-child(1) {
        width: 36%;
        padding-bottom: 0.2rem;
        position: absolute;
        left: 32%;
        top: -0.4rem;
        z-index: 2;
        border-width: 0;
        box-shadow: 0px 2px 15px #ccc;

        .top-card-name {
          font-size: 0.31rem;
          margin-bottom: 0.1rem;
          text-align: center;
        }

        .top-card-title {
          font-size: 0.21rem;
        }

        .top-card-value {
          font-size: 0.39rem;
        }
      }

      &:nth-child(2) {
        position: absolute;
        left: 0;
        top: 0;
        padding-top: 0;
        padding-bottom: 0.05rem;
      }

      &:nth-child(3) {
        position: absolute;
        right: 0;
        top: 0;
        padding-top: 0;
        padding-bottom: 0.05rem;
      }

      .top-card-name {
        font-size: 0.27rem;
        padding: 0 .2rem;
        text-align: center;
        // margin-bottom: 0.1rem;

        .xinan {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        &.name-length-s {
          font-size: 0.18rem;
        }
      }

      .top-card-title {
        color: #666;
        font-size: 0.19rem;
      }

      .top-card-value {
        font-weight: 700;
        font-size: 0.37rem;
      }

      .top-card-unit {
        font-size: 0.17rem;
        color: #222;
      }

      .top-card-add {
        font-size: 0.11rem;
        color: #666;

        span {
          font-size: 0.2rem;
        }
      }
    }
  }

  .list {
    // margin-top: -0.6rem;

    &.hide-bottom-border {
      .list-item:last-child {
        border-color: transparent;
      }
    }

    .list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;
      padding: 0.2rem 0.15rem;
      
      &>span:first-child {
        display: flex;
        align-items: center;
      }

      .list-item-index {
        width: 0.50rem;
        height: 0.54rem;
        font-size: 0.27rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.33rem;
        color: #d78082;
        background-size: cover;
        background-repeat: no-repeat;
      }

      .list-item-name {
        font-size: 0.33rem;
        width: 2.65rem;
        display: flex;
        flex-direction: column;

        .list-item-name-bottom {
          width: 100%;
          font-size: 0.21rem;
          color: #666;
        }
      }

      .list-item-bottom {
        min-width: 2.3rem;
      }

      &>span:last-child {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        color: #222;

        &>span:first-child {
          font-size: 0.23rem;
          color: #666;
        }

        .list-item-value {
          font-weight: bold;
          font-size: 0.35rem;
        }

        .list-item-unit {
          font-size: 0.32rem;
        }
      }
    }
  }
}
</style>