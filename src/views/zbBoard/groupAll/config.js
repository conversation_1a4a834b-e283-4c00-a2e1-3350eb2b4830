export const grouList2024 = [
    {
        name:'营业收入',
        key: 'yysr',
        val: 'A01',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'利润总额',
        key: 'lrze',
        val: 'A02',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'净利润',
        key: 'jinglr',
        val: 'A03',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'归母净利润',
        key: 'gmjlr',
        val: 'A04',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'营业现金比率',
        key: 'yyxjbl',
        val: 'A06',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'资产负债率',
        key: 'zcfzl',
        val: 'A08',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'净资产收益率 （年化）',
        key: 'jzcsyl',
        val: 'A05',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'研发经费投入强度',
        key: 'yftrqd',
        val: 'A09',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'全员劳动生产率（年化）',
        key: 'qyldscl',
        val: 'A07',
        unit: '万元/人',
        unit2: '万元/人',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    }
]

export const grouList2025 = [
    {
        name:'营业收入',
        key: 'yysr',
        val: 'A01',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'利润总额',
        key: 'lrze',
        val: 'A02',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'净利润',
        key: 'jinglr',
        val: 'A03',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'归母净利润',
        key: 'gmjlr',
        val: 'A04',
        unit: '亿元',
        unit2: '%',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    //2025新增营业收现率
    {
        name:'营业收现率',
        key: 'yysxl',
        val: 'A10',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'资产负债率',
        key: 'zcfzl',
        val: 'A08',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'净资产收益率 （年化）',
        key: 'jzcsyl',
        val: 'A05',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'研发经费投入强度',
        key: 'yftrqd',
        val: 'A09',
        unit: '%',
        unit2: '百分点',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    },
    {
        name:'全员劳动生产率（年化）',
        key: 'qyldscl',
        val: 'A07',
        unit: '万元/人',
        unit2: '万元/人',
        dcl: '-',
        dq: '-',
        rw: '-',
        tb: '-',
        tq: '-'
    }
]
