<template>
  <div>
    <van-popup v-model="showPopup" :style="{ height: '60%' ,width:'100%'}" :closeable="true">
    <popupList :dt="dt" :showPopup="showPopup" :userId="userId" :keyValue="keyValue"> </popupList>
    </van-popup>
    <component :is="componentName" v-bind="$props" v-on="$listeners" @getCompanyList="getCompanyList" />
  </div>
</template>

<script>
import index2024 from './components/index2024.vue'
import index2025 from './components/index2025.vue'
import popupList from './components/popupList.vue'
import mixin from './mixin'
export default {
  components: {
    index2024,
    index2025,
    popupList
  },
  mixins: [mixin],
  data() {
    return {
      componentName: '',
      showPopup: false,
      keyValue:'yysr'
    }

  },
  watch: {
    dt: {
      immediate: true,
      handler(val) {
        if (val) {
          let  year = val.slice(0, 4)
          if ( Number(year) < 2025) {
            year = '2024'
          }
          this.componentName = `index${year}`
        }
      }
    }
  },
  methods: {
    getCompanyList(item) {
      this.keyValue=item.key
      //获取点击内容详细的企业信息并弹框显示
      this.showPopup = true
    }
  }
}
</script>

<style src="./index.scss" lang="scss" scoped />
