
import { Toast } from "vant";
import { getKbData } from "@/http/api";
import { formatNum } from "@/utils";

const redReg = "#E60012";
const greenReg = "#32CD91";
const blueReg = "#008AE6";

export default {
  name: 'groupAll',
  props: {
    dt: {
      type: String,
      default: ""
    },
    currentMonth: Number,
    companyInfoList: Array,
    userId: String
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      }
    }
  },
  data() {
    return {
      blueReg: blueReg,
      reportList: [],
      groupList: [],
    }
  },
  computed: {
    getUnit() {

    },
    reportViewEnabled() {
      let self = {}

      this.companyInfoList.forEach(item => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self = item
        }
      })

      // 只有全集团运行查看月报
      if (self.cpyTag == '01') return true

      return false
    }
  },
  created() {
    this.initData()
  },
  methods: {
    formatNum,
    viewReport() {
      // 只有全集团可以点击查看月报
      if (!this.reportViewEnabled) {
        Toast('无访问权限')
        return
      }
      this.$router.push({ path: '/viewReport' })
    },
    // 初始化页面
    initData() {
      if(!this.dt) return false;
      let tempParam='yyxjbl';
      if(Number(this.dt.slice(0,4))>2024){
        tempParam='yysxl'
      }
      let params = {
        dt: this.dt,
        mapperInterface: ["yysr","jinglr","lrze","gmjlr",tempParam,"zcfzl","jzcsyl","yftrqd","qyldscl"],
        moreChooseType: '0'
      }
      getKbData(params).then(res => {
        if(res.code == 200) {
          this.groupList.forEach(item => {
            if(res.data[item['key']]) {
              item = Object.assign(item,res.data[item['key']])
            }
          })
        }
      })
    },
    // 页面跳转
    jumpPage(val) {
      sessionStorage.setItem('secondPageVal',val)
      this.$emit('toPage',1)
    },
    getTextColor(item){
      if(!item.dq || item.dq == '-') return blueReg
      if((item.tb*1) == 0) {
        return blueReg
      } else if((item.tb*1)>0 && item.val != 'A08' || ((item.tb*1)<0 && item.val == 'A08')) {
        return redReg
      } else {
        return greenReg
      }
    },
    getTextWidth(text, font) {
      var canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
      var context = canvas.getContext("2d"); 
      context.font = font;
      var metrics = context.measureText(text);
      return metrics.width;
    },
    //点击营业收入和利润总额传递item信息
    getCompanyList(key,item){
        if(item.key=='yysr'||item.key=='lrze'){
          this.companyInfoList.forEach(element => {
             if(element.userIds.includes(this.userId)&&element.entpNo=='99'){
              this.$emit('getCompanyList',item)
             }
          });
       
        }
    },
  
  }
}