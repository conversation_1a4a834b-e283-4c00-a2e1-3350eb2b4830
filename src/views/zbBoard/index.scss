.van-popup {
    .company-list-table {
      border: 1px solid #e1e1e1;
  
      &.company-list-table-progress {
        .company-list-table-header {
          background-color: #ffebec;
        }
      }
  
      &.company-list-table-retrogress {
        .company-list-table-header {
          background-color: #fce6c6;
        }
      }
  
      .company-list-table-header {
  
        .company-list-table-row {
          color: #666;
          font-size: 0.25rem;
          border-color: transparent;
        }
      }
  
      .company-list-table-body {
        height: 6rem;
        overflow: auto;
  
        .company-list-table-row:last-child {
          border-color: transparent;
        }
      }
  
      .company-list-table-row {
        width: 100%;
        min-height: 0.7rem;
        border-bottom: 1px solid #e1e1e1;
        display: flex;
        align-items: center;
        font-size: 0.31rem;
  
        .company-list-table-col1 {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 1.2rem;
        }
        .company-list-table-col2 {
          display: flex;
          justify-content: center;
          align-items: center;
          // min-width: 3rem;
          flex: 1;
        }
      }
    }
  }