<template>
  <div class="groupAll">
    <div class="content">
      <div class="header">
        <div class="header-top">
          <div class="header-top-left">
            <van-dropdown-menu>
              <van-dropdown-item :title="currentName" ref="dropDwonRef">
                <ul>
                  <li
                    v-for="item in companyList"
                    :key="item.entp_no"
                    :label="item.entp_nm"
                    :value="item.entp_no"
                    :class="{
                      selected: item.entp_no == entp_no,
                      disabled:
                        selfInfo.every(item => !item.isAll) && selfInfo.every(selfItem => selfItem.entpNo !== item.entp_no),
                    }"
                    @click="toggleSelect(item.entp_no)"
                  >
                    <span
                      :class="
                        item.entp_nm && item.entp_nm.length > 5
                          ? item.entp_nm.length > 7
                            ? 'size-7'
                            : 'size-5'
                          : ''
                      "
                    >{{ item.entp_nm }}</span>
                  </li>
                </ul>
              </van-dropdown-item>
            </van-dropdown-menu>
            <p class="title-remark" :style="{opacity: isShowRemark.show ? 1 : 0}">{{ isShowRemark.text }}</p>
            <div v-if="!isMetaCenter">
              <div class="title">营业收入</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yysr.dq || '-') }}</span>
                <span class="value-unit">{{ yysr.dq ? '亿元' : ''}}</span>
              </div>
            </div>
            <div v-else>
              <div class="title">交易规模</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yqjjyzx.trd_scl || '-') }}</span>
                <span class="value-unit">{{ yqjjyzx.trd_scl ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">集采业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.ctr_pur || '-')
                }}</span>
                <span class="other-value-unit">{{ yqjjyzx.ctr_pur ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">自助撮合业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.sef_mat_busi || '-')
                }}</span>
                <span class="other-value-unit">{{ yqjjyzx.sef_mat_busi ? '亿元' : '' }}</span>
              </div>
            </div>
          </div>

          <img
            src="../../../../assets/img/ranking/second_top_icon.png"
            class="header-top-right"
            :class="isMetaCenter ? 'header-top-right-meta' : ''"
          />
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span
              :class="
              yysr.tb ? (
                formatNum(yysr.tb) > 0
                  ? 'other-value-up'
                  : 'other-value-down'
              ) : 'other-value'
              "
              >{{ formatNum(yysr.tb || '-', undefined, "ratio") }}</span
            >
            <span class="other-value-unit">{{ yysr.tb ? '%' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(yysr.rw || '-') }}</span>
            <span class="other-value-unit">{{ yysr.rw ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="bottom-title">
          <div class="title">利润总额</div>
          <div class="value">
            <span class="value-text">{{ formatNum(lrze.dq || '-') }}</span>
            <span class="value-unit">{{ lrze.dq ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span
              :class="
              lrze.tb ? (
                formatNum(lrze.tb) > 0
                  ? 'other-value-up'
                  : 'other-value-down'
              ) : 'other-value'
              "
              >{{ lrze.dq !== undefined ? formatNum(lrze.dq - lrze.tq, undefined, "ratio") : '-' }}</span
            >
            <span class="other-value-unit">{{ lrze.dq !== undefined ? '亿元' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(lrze.rw || '-') }}</span>
            <span class="other-value-unit">{{ lrze.rw ? '亿元' : '' }}</span>
          </div>
        </div>
      </div>

      <div v-if="!isMetaCenter && indexList.length > 0" class="index-card">
        <div class="index-item" v-for="(item, index) in indexList" :key="index">
          <div>
            <!-- <img :src="item.icon" alt="" /> -->
            <span class="index-item-name">{{ item.name }}</span>
          </div>
          <div class="index-item-tail">
            <span class="index-item-value">{{
              item.value == undefined ? '-' : formatNum(item.value, undefined, item.type)
            }}</span>
            <span class="index-item-unit" :style="item.unitStyle">{{ item.value == undefined ? '' : item.unit }}</span>
          </div>
        </div>
      </div>

      <div class="task-card">
        <div class="title">重点工作任务</div>
        <div v-if="taskList.length === 0 && !isMetaCenter" class="task-no-data">
          <img
            src="../../../../assets/img/ranking/second_task_no_data.png"
            alt=""
          />
          <span>集团未下达企业年度重点工作任务</span>
        </div>
        <div
          v-else-if="taskList.length === 0 && isMetaCenter"
          class="task-no-data-meta-center"
        >
          <img
            src="../../../../assets/img/ranking/second_task_no_data.png"
            alt=""
          />
          <span>集团未下达企业年度重点工作任务</span>
        </div>
        <div
          v-else
          class="task-item"
          v-for="(item, index) in taskList"
          :key="index"
        >
          <!-- <div class="index">{{ index + 1 }}</div> -->
          <div class="content">
            <span class="name"><span class="sort">{{ index+1+"." }}</span>{{ item.task_name ? (
              item.task_name.length > 11 ?
                item.task_name.slice(0, 11) + '...' : item.task_name
            ) : '' }}</span>
            <div v-for="(item1, index1) in item.children" :key="index1">
              <div class="secondTitle" v-if="item.children.length>1" :class="index1 == 0 ? 'secondTitle-first' : ''">{{ item1.task_nm_l2 }}</div>
              <div class="bottom">
                <ProgressBar :value="Number(item1.task_pct)"
                  :disabled="progressBarDisabled(item.children.length > 1 ? item1 : item)" />
                <div class="btn" @click="handleViewDetails(item1,item)"></div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '65%' }"
      class="modal"
    >
      <div :class="{ tab1: activeTab == 1, tab2: activeTab == 2 }">
        <div class="tab-left" @click="activeTab = 1" />
        <div class="tab-right" @click="activeTab = 2" />
      </div>

      <!-- <h2>{{ taskDetails.task_name }}</h2> -->
      <div
        v-if="activeTab == 1 ? taskDetails.task_desp : taskDetails.task_detail"
        :key="activeTab"
        class="modal-content"
        v-html="activeTab == 1 ? taskDetails.task_desp : taskDetails.task_detail"
      />
      <div class="modal-no-data" v-else>
        <img src="../../../../assets/img/ranking/second_task_no_data.png" alt="" />
        <span>暂无任务进展情况</span>
      </div>

      <div class="close-btn">
        <div class="close-btn-inner" @click="show = false">关闭</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import mixin from '../mixin.js'

export default {
  mixins: [mixin],
  data() {
    return {
      rateData: {
        key: "csh_rate",
        name: "营业现金比率",
      }

    }
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
