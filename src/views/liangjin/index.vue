<template>
  <div class="liangjin">
    <div class="topsTip">
      <div><img style="width:0.28rem" src="../../assets/img/gt.png" alt="">说明：模块完善中，将于近期上线。</div>
    </div>
      <div class="container">
         <img style="width:100%" src="../../assets/img/liangjin/liangjin.png" alt="">
      </div>
  </div>
</template>

<script>


export default {
  name: 'liangjin',
  props: {},
  data() {
    return {
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  watch: {
   
  },
  methods: {
   
  },
  mounted() {
   
  },
  created() {
    
  },
  beforeDestroy() {
    
  }
}
</script>

<style lang="scss" scoped>
// #bottom-nav {
//   position: relative;
//   z-index: 1;
// }

</style>
