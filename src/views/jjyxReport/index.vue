<template>
  <div class="liangjin">
    <div class="topsTip">
      <div><img style="width:0.28rem" src="../../assets/img/gt.png" alt="">说明：模块完善中，将于近期上线。</div>
    </div>
      <div class="container">
         <img v-if="showImg=='主要指标'" style="width:100%;margin-bottom: 1.2rem;" src="../../assets/img/jjyxReport/zyzb.png" alt="">
         <img v-if="showImg=='重点业务板块'" style="width:100%;margin-bottom: 1.2rem;" src="../../assets/img/jjyxReport/ywbk.png" alt="">
      </div>
      <div id="bottom-nav">
    <van-tabbar v-model="active">
      <van-tabbar-item @click="handleClick(0)">
        <template #icon="props">
          <img :src="props.active ? navLeftActive : navLeft" :style="{height: '0.4rem'}"/>
        </template>
        <!-- <div :style="{visibility: !showPopover1 ? 'hidden' : ''}" class="nav-popover nav-popover1">
          <p v-for="(item,index) in action1" :key="index" @click="handleToGo(item,item.to)">
            <span>{{ item.text }}</span>
          </p>
        </div> -->
        <span class="navBigText">主要指标</span>
      </van-tabbar-item>
      <van-tabbar-item @click="handleClick(1)">
        <template #icon="props">
          <img :src="props.active ? navRightActive : navRight" :style="{height: '0.4rem'}"/>
        </template>
        <!-- <div :style="{visibility: !showPopover2 ? 'hidden' : ''}" class="nav-popover nav-popover2">
          <p v-for="(item,index) in action2" :key="index" @click="handleToGo(item,item.to)">
            <span>{{ item.text }}</span>
          </p>
        </div> -->
        <span class="navBigText">重点业务板块</span>
      </van-tabbar-item>
    </van-tabbar>
  </div>
  </div>
</template>

<script>
import navLeft from "@/assets/img/jjyxReport/navLeft.png";
import navLeftActive from "@/assets/img/jjyxReport/navLeftActive.png";
import navRight from "@/assets/img/jjyxReport/navRight.png";
import navRightActive from "@/assets/img/jjyxReport/navRightActive.png";
import { setDocumentTitle } from '@/utils/document'

export default {
  name: 'jjyxReport',
  props: {},
  data() {
    return {
      navLeft: navLeft,
      navLeftActive: navLeftActive,
      navRight: navRight,
      navRightActive: navRightActive,
      active: 0,
      showPopover1: false,
      showPopover2: false,
      action1: [
        {text: '人才概况', to: '/hrIndex'},
        {text: '教育信息', to: '/eduMsg'},
        {text: '工作信息', to: '/workMsg'},
        { text: '人员查询', to: 'rpt-m.cec.com.cn/smartbi/vision/share.jsp?resid=I8a8b9090018bf538f538a661018c0070e8013177&hiddenParamPanel=true' }
      ],
      action2: [
        {text: '土地', to: '/managerMsg'},
        {text: '房产', to: '/technologyMsg'},
        {text: '生产线', to: '/skillMsg'},
        {text: '在建工程', to: '/skillMsg'},
         {text: '或有资产', to: '/skillMsg'},
      ],
       ownOrgName:"全集团",//是否是二级有企业单位进来
       showImg:"主要指标"
    }
  },
  watch: {
    showPopover1(val) {
      if(val) {
        let el = $(".nav-popover1");
        let elHeight =  el.height() + 5;
        $(".nav-popover1").css("top",`-${elHeight}px`)
      }
    },
    showPopover2(val) {
      if(val) {
        let el = el = $(".nav-popover2");
        let elHeight = el.height() + 5;
         $(".nav-popover2").css("top",`-${elHeight}px`)
      }
    }
  },
  methods: {
    handleClick(index) {
      if(index==0){
        this.showImg='主要指标'
        setDocumentTitle("1-10月主要指标")
      }else{
        this.showImg='重点业务板块'
        setDocumentTitle("1-10月重点业务板块")
      }
      //点击切换标签页，回到顶部
       window.scrollTo(0,0)
      if (!index && this.showPopover1 || index && this.showPopover1) {
        this.showPopover1 = false;
        if (index && !this.showPopover1) this.showPopover2 = true;
      } else if (!index && this.showPopover2 || index && this.showPopover2) {
        this.showPopover2 = false;
        if (!index && !this.showPopover2) this.showPopover1 = true;
      } else if (!index && !this.showPopover1) {
        this.showPopover1 = true;
      } else if (index && !this.showPopover2) {
        this.showPopover2 = true;
      }

    },
    handleToGo(item, path) {
      
      this.showImg=item.text
    },
    bodyClick(e) {
      const el = document.querySelector('#bottom-nav');
      if (el && !el.contains(e.target)) {
        this.showPopover1 = false;
        this.showPopover2 = false;
      }
    }
  },
  mounted() {
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    if( this.ownOrgName!='全集团'){
      this.action1.unshift({text: '集团人才概况', to: '/hrIndexTwo'},)
    }
    if( this.ownOrgName=='全集团'){
      this.action1.pop()
    }

    // 导航栏active显示
    if (this.action1.some(item => {
      return item.to == this.$route.path
    })) {
      this.active = 0;
    } else if (this.action2.some(item => {
      return item.to == this.$route.path
    })) {
      this.active = 1;
    }
  },
  created() {
    document.addEventListener('click', this.bodyClick)
  },
  beforeDestroy() {
    document.addEventListener('click', this.bodyClick)
  }
}
</script>

<style lang="scss" scoped>
// #bottom-nav {
//   position: relative;
//   z-index: 1;
// }
::v-deep .van-tabbar-item {
  font-size: 0.3rem;
  position: relative;

  .van-icon {
    font-size: 0.5rem;
    margin-bottom: 0.1rem;
  }
}

::v-deep .van-tabbar.van-tabbar--fixed {
  height: 1.3rem;
  z-index: 15;
}

.van-tabbar-item__text {
  font-size: 0.28rem;
}

::v-deep .van-tabbar-item__icon {
  margin-bottom: 0.15rem;
}

.van-tabbar-item__text .nav-popover {
  position: absolute;
  // top: -3.4rem;
  transform: translateX(-10px);
  background: #fff;
  padding: 10px 14px;
  box-shadow: 10px 6px 16px 0px rgba(74, 103, 157, 0.1);
  border-radius: 6px 6px 0px 0px;
  text-align: center;
}

.van-tabbar-item__text .nav-popover p {
  display: block;
  padding: 5px;
  border-bottom: 1px solid #cccccc;
  font-size: 0.3rem;
  line-height: 2;
}

.van-tabbar-item__text .nav-popover p > span {
  display: block;
  padding: 5px;
}

.van-tabbar-item__text .nav-popover p:hover span {
  background: #eeeeee;
}

.van-tabbar-item__text .nav-popover p:nth-of-type(1) {
  padding-top: unset;
}

.van-tabbar-item__text .nav-popover p:nth-last-of-type(1) {
  border-bottom: unset;
  padding-bottom: unset;
}

.van-tabbar-item--active {
  color: unset;
}
.van-tabbar-item--active .navBigText{
  color: #3E7BFA;
}
</style>
