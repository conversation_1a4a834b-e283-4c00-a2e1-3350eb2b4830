<template>
  <div class="container">
    <RadioGroup v-model="busType">
      <Radio v-for="item in reportTypeList" :key="item.key" :name="item.key">{{ item.name }}</Radio>
    </RadioGroup>
    <uploader v-model="file" accept="file" max-count="1"><button>文件上传</button></uploader>
    <p>文件命名规则为【文件名称-日期】例如：【经营分析报告-2024年4月】</p>
    <button @click="upload">提交</button>
  </div>
</template>

<script>
import { Toast, Form, Field, Radio, RadioGroup, Uploader } from 'vant'

import { uploadFile } from '@/http/api'
import { reportTypeList } from '@/constants'

export default {
  components: {
    Form,
    Field,
    Radio,
    RadioGroup,
    Uploader,
  },
  data() {
    return {
      file: [],
      busType: '',
      reportTypeList,
    }
  },
  methods: {
    upload() {
      if (!this.busType || !this.file[0]) return
      const formData = new FormData()
      formData.append('file', this.file[0].file)
      formData.append('busType', this.busType)

      uploadFile(formData)
        .then(() => {
          this.file = []
          this.busType = ''
          Toast.success('上传成功');
        })
        .catch(() => {
          Toast.fail('上传失败');
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    padding: 30px;
  }
</style>