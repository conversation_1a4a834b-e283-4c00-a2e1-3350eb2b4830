<template>
  <div class="rcBox">
    <div class="bannerBox">
      <div class="banner"><img src="../../assets/img/bg2.png" alt=""></div>
      <!-- 职工总人数 -->
      <div class="floatTop">
        <div class="worderNum">
          <h4>总人数<img class="tipBtn" @click="show1=true" src="../../assets/img/tip.png" alt=""></h4>
          <h2>{{dataObj.employeeSkill_Skill&& (dataObj.employeeSkill_Skill*1).toLocaleString() || 0}}<span>人</span></h2>
          <h5 :style="{marginTop: '0.2rem'}">占比<span :style="{marginLeft: '0.3rem'}">{{dataObj.employeeSkillRatio_Skill || '0.0%'}}</span></h5>
        </div>
      </div>
    </div>
    <div class="container">
        <!-- 技能等级分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工技能等级分布情况'" :text="['指“技能类”岗位中，按“技能等级”统计职工各技能的分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeGrade_Skill_Content}}
          </div>
          <div class="haveNoPosition" v-if="dataObj.employeejobTitle_Y_Skill || dataObj.employeejobTitle_N_Skill">
            <div class="box">
              <h3>有技能等级人员</h3>
              <p> {{dataObj.employeejobTitle_Y_Skill.value1}}人 ({{dataObj.employeejobTitle_Y_Skill.value2}}%)</p>
            </div>
            <div class="box">
              <h3>无技能等级人员</h3>
              <p> {{dataObj.employeejobTitle_N_Skill.value1}}人 ({{dataObj.employeejobTitle_N_Skill.value2}}%)</p>
            </div>
          </div>
          <div class="port" style="padding-bottom:0.1rem">
            <echarts-pies :pieColorList="employeeGradeColor" :toChild='dataObj.employeeGrade_Skill' />
          </div>
        </div>
      </div>
      <!-- 企业分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工企业分布情况'" :text="['指“技能类”岗位中，按企业统计职工的分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="lookMore" v-show="dataObj.employeeEnterprise_Skill && dataObj.employeeEnterprise_Skill.length > 10" @click="lookMoreHandler('skillData','employeeEnterprise_Skill','技能岗位','职工企业分布情况',dataObj.employeeEnterprise_Skill_Content)"><span>查看更多</span></div>
          <div class="discribBox">
            {{dataObj.employeeEnterprise_Skill_Content}}
          </div>
          <div class="port" style="padding-bottom:0.03rem;padding-top:0.12rem">
            <echart-bar-y :id="'barY1'" :barItemColor="'#4481F6'" :data="dataObj.employeeEnterprise_Skill" />
          </div>
        </div>
      </div>
      <!-- 职工职称分布情况 -->
      <!-- <public-title :title="'职工职称分布情况'" :text="['本指标的注释文字：XXXXXXXX']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          XXXXX
        </div>
        <div class="dataDetails">
          <h4>工程类</h4>
          <dl v-if="dataObj.employeeJobTitle">
            <dd>正高:{{dataObj.employeeJobTitle[0] && (dataObj.employeeJobTitle[0].pur_mth_amt*1).toLocaleString()}}</dd>
            <dd>副高:{{dataObj.employeeJobTitle[1] && (dataObj.employeeJobTitle[1].pur_mth_amt*1).toLocaleString()}}</dd>
            <dd>中级:{{dataObj.employeeJobTitle[2] && (dataObj.employeeJobTitle[2].pur_mth_amt*1).toLocaleString()}}</dd>
            <dd>初级:{{dataObj.employeeJobTitle[3] && (dataObj.employeeJobTitle[3].pur_mth_amt*1).toLocaleString()}}</dd>
          </dl>
        </div>
        <div class="port">
          <duiji-bar :toChild="dataObj.employeeJobTitle" />
        </div>
      </div> -->
      <!-- 学历分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工学历分布情况'" :text="['指“技能类”岗位中，按“全日制\\含非全日制最高学历”，统计对比职工学历分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeDegree_Skill_Content}}
          </div>
          <div class="port" style="padding-top:0.08rem;padding-bottom:0.08rem">
            <echart-bar-dx :id="'barDx1'" :barItemColor="['#53B5FF','#FFC683']" :data="dataObj.employeeDegree_Skill" />
          </div>
        </div>
      </div>
      <!-- 司龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工司龄分布情况'" :text="employeeSeniority_Skill_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeSeniority_Skill_Content}}
          </div>
          <div class="port">
            <pictorial-bar :colorBg="'#4481F6'" label="平均司龄" :toChild='dataObj.employeeSeniority_Skill' :value="dataObj.employeeSeniorityAverage_Skill" />
          </div>
        </div>
      </div>
      <!-- 集团工作年限分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工集团工作年限分布情况'" :text="employeeWorkAge_Skill_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeWorkAge_Skill_Content}}
          </div>
          <div class="port">
            <pictorial-bar label="平均集团工作年限" :colorBg="'#20C3BE'" :toChild='dataObj.employeeWorkAge_Skill' :value="dataObj.employeeWorkAgeAverage_Skill" />
          </div>
        </div>
      </div>
      <!-- 年龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工年龄分布情况'" :text="employeeAge_Skill_Desc" unit="人"></public-title>
        <div class="doduleBox pictorialBarBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeAge_Skill_Content}}
          </div>
          <div class="port">
            <pictorial-bar label="平均年龄" :colorBg="'#70D9FF'" :toChild='dataObj.employeeAge_Skill' :value="dataObj.employeeAgeAverage_Skill" />
          </div>
        </div>
      </div>
      <!-- 工龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工工龄分布情况'" :text="employeeWorkSeniority_Skill_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeWorkSeniority_Skill_Content}}
          </div>
          <div class="port">
            <pictorial-bar :colorBg="'#FFC683'" label="平均工龄" :toChild='dataObj.employeeWorkSeniority_Skill' :value="dataObj.employeeWorkSeniorityAverage_Skill" />
          </div>
        </div>
      </div>
      <!-- 获奖情况 -->
      <div class="titleContainer">
        <public-title :title="'职工获奖情况'" :text="employeePrize_Skill_Desc"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeePrize_Skill_Content}}
          </div>
          <div class="port" style="padding-top:0.1rem">
            <echart-bar-x :id="'barX2'"  :fontSize="'15px'"  :width="'12px'" :barItemColor="'#F5876A'" :data="dataObj.employeePrize_Skill" />
          </div>
        </div>
      </div>
    </div>
    <global-filter @submit="submitFilter" @reset="resetFilter"></global-filter>
    <item-tip :show="show1" @handleClose="show1=false" :content="talentOverview" />
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue';
import echartBarDx from "@/components/echartBarDX";
import echartBarX from "@/components/echartBarX";
import echartBarY from "@/components/echartBarY";
import echartsPies from '@/components/echartsPies.vue';
import echartBarSup from "@/components/echartBarSup";
import pictorialBar from '@/components/pictorialBar.vue';
import globalFilter from '@/components/globalFilter.vue';
import echartsWater from '@/components/echartsWater.vue';
import itemTip from '@/components/itemTip.vue';
import duijiBar from '@/components/duijiBar.vue'
import { getHrData } from "@/http/api";
import { getEmployeeGrade_SkillColors } from "@/utils/data";
export default {
  name: 'skillMsg',
  components: {
    publicTitle,
    echartBarX,
    echartBarY,
    echartsPies,
    echartBarSup,
    globalFilter,
    pictorialBar,
    itemTip,
    echartBarDx,
    echartsWater,
    duijiBar
  },
  data() {
    return {
      show1: false, //人才概况职工总人数
      talentOverview: [
        '岗位为“技能类”的人员，一般指掌握一定的专业技能、从事某种技术性较强工种工作的人员。以及“技能类”人员占“职工总人数”的百分比。'
      ], 
      employeeSeniority_Skill_Desc: [
        '指“技能类”岗位中，按“本单位入职时间”统计职工各司龄的分布情况；其中司龄划分如下：',
        '• 10年以上（指＞10年）',
        '• 6-10年（指＞6年 且 ≤10年）',
        '• 3-6年（指＞3年 且 ≤6年）',
        '• 1-3年（指＞1年 且 ≤3年）',
        '• 1年及以下（指≤1年）',
      ],
      employeeWorkAge_Skill_Desc: [
        '指“技能类”岗位中，按“中国电子系统内工作年限”统计职工各年限的分布情况；其中年限划分如下：',
        '• 10年以上（指＞10年）',
        '• 6-10年（指＞6年 且 ≤10年）',
        '• 3-6年（指＞3年 且 ≤6年）',
        '• 1-3年（指＞1年 且 ≤3年）',
        '• 1年及以下（指≤1年）',
      ],
      employeeAge_Skill_Desc: [
        '指“技能类”岗位中，按“出生日期”统计职工各年龄的分布情况：其中年龄划分如下：',
        '• 60岁及以上（指≥60岁）',
        '• 55-59岁（指≥55岁 且 ＜60岁）',
        '• 51-54岁（指≥51岁 且 ＜55岁）',
        '• 46-50岁（指≥46岁 且 ＜51岁）',
        '• 41-45岁（指≥41岁 且 ＜46岁）',
        '• 36-40岁（指≥36岁 且 ＜41岁）',
        '• 35岁及以下（指＜36岁）'
      ],
      employeeWorkSeniority_Skill_Desc: [
        '指“技能类”岗位中，按“参加工作时间”统计职工各工龄的分布情况；其中工龄划分如下：',
        '• 20年以上（指＞20年）',
        '• 10-20年（指＞10年 且 ≤20年）',
        '• 5-10年（指＞5年 且 ≤10年）',
        '• 1-5年（指＞1年 且 ≤5年）',
        '• 1年及以下（指≤1年）',
      ],
      employeePrize_Skill_Desc: [
        '指“技能类”岗位中，按“获奖级别”，统计职工获奖的分布情况。获奖级别说明如下：',
        '1.国家级：由国家政府机构或组织授予的奖励；“一级学会”获奖等同于“国家级”获奖。',
        '2.省部级：由中华人民共和国各省、自治区、直辖市党委或人民政府直接授予的奖励；\n“二级学会”获奖等同于“省部级”获奖；\n“副省级城市”获奖，等同于“省部级”获奖；\n“中国电子集团”颁发的奖励，等同于“省部级”获奖；\n“军队”颁发的奖项，如为中央军委颁发，则为国家级；如为中央军委科技委等部门颁发，则为省部级。',
        '3.省部级以下：未达到以上级别的获奖，如地级市获奖、其他组织或团体办法的奖励。',
      ],
      employeeGradeColor: ['#FFD452', '#C0193D', '#9996FF', '#1F88FF', '#74D8F4', '#10CBD9', '#B4CCFD', '#D4D4D4', '#ba67f8'],
      propsObject: {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      },
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  computed: {
    dataObj() {
      return this.$store.state.skillData
    }
  },
  mounted() {
    this.initData();
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
    if(this.ownOrgName!='全集团'){
      $(".discribBox").hide()
    }
  },
  methods: {
    //初始化数据
    initData() {
      let params = {
        mapperInterface: ["employeeSkill_Skill", "employeeAge_Skill", "employeeAgeAverage_Skill", "employeeSkillRatio_Skill", "employeeGrade_Skill", "employeeEnterprise_Skill",
         "employeeDegree_Skill", "employeeSeniorityAverage_Skill", "employeeSeniority_Skill", "employeeWorkAgeAverage_Skill", 
         "employeeWorkAge_Skill", "employeeWorkSeniorityAverage_Skill", "employeeWorkSeniority_Skill", "employeePrize_Skill",
         "employeejobTitle_Y_Skill","employeejobTitle_N_Skill"],
         
        page: "Skill"
      }
      params = { ...params, ...this.propsObject }
      getHrData(params).then((res) => {
        if (res && res.code == 200) {
          if(res.data.employeeGrade_Skill.length) {
            this.employeeGradeColor = getEmployeeGrade_SkillColors(res.data.employeeGrade_Skill);
          }
          this.$store.dispatch("commitChangeSkillData", res.data);
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    lookMoreHandler(type, params, title,moduleTitle,desc) {
      let scrollX = window.scrollX;
      let scrollY = window.scrollY;
      sessionStorage.setItem('savedPosition', JSON.stringify({ x: scrollX, y: scrollY }));
      sessionStorage.setItem('desc', JSON.stringify(desc));
      this.$router.push({ path: '/moreData', query: { type, params, title,moduleTitle,moreList:this.propsObject.moreList,consldEntpFlg:this.propsObject.consldEntpFlg} })
    },
    // 重置查询
    resetFilter() {
      this.propsObject = {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      }
      this.initData();
      //从sessionStorage中获取是二级单位还是全集团
      this.ownOrgName = sessionStorage.getItem("ownOrgName")
      //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
      if (this.ownOrgName != '全集团') {
        $(".discribBox").hide()
      }
    },
    //从筛选子组件传过来的值，判断是否显示京津冀
    submitFilter(val) {
      // 获取数据
      this.propsObject = val;
      this.initData();
      window.scrollTo(0,0);
    },
  },
}
</script>

<style lang="scss" scoped>
.rcBox {
  background: #f7f8f9;
  font-family: "微软雅黑";
  padding-bottom: 1.8rem;
  .tipBtn {
    width: 0.4rem;
    height: 0.4rem;
  }
  .bannerBox {
    position: relative;
    padding: 0.5rem 0.3rem 0;
    //职工总人数
    .banner {
      img {
        width: 100%;
        height: 3.4rem;
        border-radius: 6px;
        // height: 6.37rem;
      }
    }
    .worderNum {
      position: absolute;
      left: 0.86rem;
      top: 0.96rem;
      color: #fff;

      h4 {
        font-size: 0.4rem;
        position: relative;
        display: inline-block;
        img {
          position: absolute;
          right: -25px;
          top: -5px;
        }
      }
      h5 {
        font-size: 0.33rem;
      }
      h2 {
        font-size: 0.8rem;
        margin-top: 0.25rem;
        font-weight: 600;

        span {
          font-size: 0.32rem;
        }
      }
    }
  }
  //三大岗位板块：管理/科技/技能
  .container {
    padding: 0 0.3rem;
    .station {
      display: flex;
      margin-top: 0.3rem;
      .box {
        flex: 1;
        font-size: 0.32rem;
        text-align: center;
        background: #fff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 0.1rem;
        padding: 0.3rem 0;
        margin-left: 0.1rem;
        &:first-child {
          margin-left: 0;
        }
        div {
          margin-top: 0.23rem;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .checkBoxs {
      float: right;
      margin-top: 0.3rem;
      .text {
        font-size: 0.3rem;
        color: #456bc4;
      }
    }
    .dataDetails {
      margin-top: 0.32rem;
      h4 {
        font-size: 0.32rem;
        color: #222;
        margin-bottom: 0.2rem;
      }
      dl {
        color: #333;
        font-size: 0.32rem;
        display: flex;
        dd {
          margin-right: 0.1rem;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}
::v-deep i.van-icon.van-icon-success {
  border: 2px solid #456bc4 !important;
  border-radius: 4px;
}

.water-container {
  display: flex;
  justify-content: space-around;
  .water-item {
    padding: 0 0.1rem;
    text-align: center;
    border-right: 1px dashed #cccccc;
    h1 {
      font-size: 0.3rem;
      font-weight: 600;
      color: "#333333";
      line-height: 0.62rem;
    }
    h2 {
      font-size: 0.3rem;
      font-weight: 400;
      color: "#333333";
      line-height: 0.62rem;
    }
    h3 {
      font-size: 0.2rem;
      font-weight: 400;
      color: "#71737A";
      line-height: 0.58rem;
    }
  }
  .water-item:nth-last-of-type(1) {
    border-right: unset;
  }
}
</style>
