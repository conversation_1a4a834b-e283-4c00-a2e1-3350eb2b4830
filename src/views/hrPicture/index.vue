<template>
  <div class="rcBox">
    <div class="bannerBox">
      <div class="banner"><img :style="{'height': propsObject.moreList=='' && propsObject.district==''  && propsObject.consldEntpFlg=='' ? '7rem':'3.5rem'}" src="../../assets/img/bg.png" alt=""></div>
      <!-- 职工总人数 -->
      <div class="floatTop">
        <div class="worderNum worderNum1">
          <h4>
            <strong v-if="propsObject.consldEntpFlg=='1'">控股企业职工总数</strong>
            <strong v-if="propsObject.consldEntpFlg=='0'">参股企业职工总数</strong>
            <strong v-if="propsObject.consldEntpFlg==''">全级次控股企业和一级参股企业职工总数</strong>
            <img v-if="ownOrgName=='全集团'" class="tipBtn" @click="show1=true" src="../../assets/img/tip.png" alt="">
          </h4>
          <h2>{{dataObj.top1_Home && (dataObj.top1_Home*1).toLocaleString() || 0}}<span>人</span></h2>
        </div>
        <div class="worderNum worderNum2" v-if="propsObject.moreList=='' && propsObject.district=='' && propsObject.consldEntpFlg==''">
          <h4>全级次控股企业职工总数</h4>
          <h2>{{ dataObj.top2_Home && (dataObj.top2_Home*1).toLocaleString() || 0 }}<span>人</span></h2>
        </div>
        <div class="worderNum worderNum3" v-if="propsObject.moreList=='' && propsObject.district=='' && propsObject.consldEntpFlg==''">
          <h4>全级次一级参股企业职工总数</h4>
          <h2>{{ dataObj.top3_Home && (dataObj.top3_Home*1).toLocaleString() || 0 }}<span>人</span></h2>
        </div>
        <div class="worderNum worderNum4" v-if="propsObject.moreList=='' && propsObject.district=='' && propsObject.consldEntpFlg==''">
          <h4>本次分析包含的职工人数<img class="tipBtn" @click="show1_4=true" src="../../assets/img/tip.png" alt=""></h4>
          <h2>{{ dataObj.top4_Home && (dataObj.top4_Home*1).toLocaleString() || 0 }}<span>人</span></h2>
        </div>

      </div>
      <!-- 五大六类 -->
      <div class="fiveSixPerson">
        <div class="box">
          <div class="fiveNum">
            <div>五大主业职工总数</div>
            <div>{{dataObj.employeeFiveSum_Home || 0}}人</div>
            <div v-if="Number(dataObj.employeeFiveRatio_Home) && Number(dataObj.employeeFiveRatio_Home)>0.1">占比 {{dataObj.employeeFiveRatio_Home}}%</div>
            <div v-else-if="Number(dataObj.employeeFiveRatio_Home) && Number(dataObj.employeeFiveRatio_Home)<0.1">小于 0.1%</div>
            <div v-else>占比 0.0%</div>
          </div>
          <div class="sixNum">
            <div>六类支撑职工总数</div>
            <div>{{dataObj.employeeSixSum_Home || 0}}人</div>
            <div v-if="Number(dataObj.employeeSixRatio_Home) && Number(dataObj.employeeSixRatio_Home)>0.1">占比 {{dataObj.employeeSixRatio_Home}}%</div>
            <div v-else-if="Number(dataObj.employeeSixRatio_Home) && Number(dataObj.employeeSixRatio_Home)<0.1">小于 0.1%</div>
            <div v-else>占比 0.0%</div>
          </div>
          <img class="tipBtn" @click="show2=true" src="../../assets/img/tip1.png" alt="">
        </div>
      </div>
    </div>

    <div class="container">
      <!-- 三大岗位板块：管理/科技/技能 -->
      <div class="station">
        <div class="box">
          <div class="weight600">管理岗位</div>
          <div>{{dataObj.employeeManageSum_Home ? Number(dataObj.employeeManageSum_Home).toLocaleString() : 0}}人</div>
          <div v-if="Number(dataObj.employeeManageRatio_Home) && Number(dataObj.employeeManageRatio_Home)>0.1">占比 {{dataObj.employeeManageRatio_Home}}%</div>
          <div v-else-if="Number(dataObj.employeeManageRatio_Home) && Number(dataObj.employeeManageRatio_Home)<0.1">小于 0.1%</div>
          <div v-else>占比 0.0%</div>
        </div>
        <div class="box">
          <div class="weight600">科技岗位</div>
          <div>{{dataObj.employeeTechnologySum_Home ? Number(dataObj.employeeTechnologySum_Home).toLocaleString() : 0}}人</div>
          <div v-if="Number(dataObj.employeeTechnologyRatio_Home) && Number(dataObj.employeeTechnologyRatio_Home)>0.1">占比 {{dataObj.employeeTechnologyRatio_Home}}%</div>
          <div v-else-if="Number(dataObj.employeeTechnologyRatio_Home) && Number(dataObj.employeeTechnologyRatio_Home)<0.1">小于 0.1%</div>
          <div v-else>占比 0.0%</div>
        </div>
        <div class="box">
          <div class="weight600">技能岗位</div>
          <div>{{dataObj.employeeSkillSum_Home ? Number(dataObj.employeeSkillSum_Home).toLocaleString() : 0}}人</div>
          <div v-if="Number(dataObj.employeeSkillRatio_Home) && Number(dataObj.employeeSkillRatio_Home)>0.1">占比 {{dataObj.employeeSkillRatio_Home}}%</div>
          <div v-else-if="Number(dataObj.employeeSkillRatio_Home) && Number(dataObj.employeeSkillRatio_Home)<0.1">小于 0.1%</div>
          <div v-else>占比 0.0%</div>
        </div>
      </div>
      <!-- 职工企业分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工企业分布情况'" :text="['指按企业，统计职工的分布情况。']"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="lookMore" v-show="dataObj.employeeEnterprise_Home && dataObj.employeeEnterprise_Home.length > 10" @click="lookMoreHandler('data','employeeEnterprise_Home','人才概况','职工企业分布情况',Boolean(propsObject.district) ? '' : dataObj.employeeEnterprise_Home_Content)"><span>查看更多</span></div>
          <div class="discribBox">
            {{dataObj.employeeEnterprise_Home_Content}}
          </div>
          <div class="port" style="padding-top:0.1rem">
            <echart-bar-y :id="'barY1'" :barItemColor="'#4481F6'" :data="dataObj.employeeEnterprise_Home" />
          </div>
        </div>
      </div>
      <!-- 职工业务板块分布情况 -->
      <div class="titleContainer" v-if="propsObject.moreChooseType==0 || (propsObject.moreChooseType==1 && propsObject.moreList && propsObject.moreList.split(',').length>1) || (propsObject.moreChooseType==2)">
        <public-title :title="'职工业务板块分布情况'" :text="['指按“当前从事业务所属主要\\其他板块”与“工作经历涉及业务板块（2年以上）”业务板块，统计职工的分布情况。']"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeBusiness_Home_Content}}
          </div>
          <div class="port">
            <echart-bar-x :id="'barX1'" :isMinFont="true" :width="'12px'" :barItemColor="'#FFC683'"  :data="dataObj.employeeBusiness_Home" />
          </div>
        </div>
      </div>
      <!-- 职工全国各省分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工全国各省分布情况'" :text="['指按中国现行的34个一级行政区，包括23个省、5个自治区、4个直辖市、2个特别行政区，统计职工“工作所在地”分布情况。']"></public-title>
        <div class="doduleBox classjjj">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeProvince_Home_Content}}
          </div>
          <div class="lookMore" v-show="dataObj.employeeProvince_Home && dataObj.employeeProvince_Home.length > 10" @click="lookMoreHandler('data','employeeProvince_Home','人才概况','职工全国各省分布情况', Boolean(propsObject.district) ? '' : dataObj.employeeProvince_Home_Content)"><span>查看更多</span></div>
          <div class="station mt40" v-show="isShowJJJ">
            <div class="box">
              <div class="weight600">京津冀<br />地区</div>
              <div>{{dataObj.employeeBeijingSum_Home || 0}}人</div>
            </div>
            <div class="box">
              <div class="weight600">长三角<br />地区</div>
              <div>{{dataObj.employeeCSJSum_Home || 0}}人</div>
            </div>
            <div class="box">
              <div class="weight600">粤港澳<br />大湾区</div>
              <div>{{dataObj.employeeYGASum_Home || 0}}人</div>
            </div>
          </div>
          <div class="port" style="padding-top: 0.22rem;">
            <echart-bar-y :id="'barY2'" :barItemColor="'#4481F6'" :data="dataObj.employeeProvince_Home" />
          </div>
        </div>
      </div>
      <!-- 职工学历分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工学历分布情况'" :text="['指按“全日制\\含非全日制最高学历”，统计职工学历分布情况。默认展示“全日制最高学历”分布，勾选“含非全日制”时，展示包含非全日制学历的分布情况。']"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeDegree_Home_Content}}
          </div>
          <div v-show="dataObj.doctorTitle_AppInfo_Home && dataObj.doctorTitle_AppInfo_Home.length>0" style="height:2.4rem" class="lookMore"  @click="lookMoreWorderDeatils('data','doctorTitle_AppInfo_Home','人才概况','博士职工名单',dataObj.employeeProvince_Home_Content)"><span>博士职工名单</span></div>
          <div class="checkBoxs" v-if="dataObj.employeeDegree_Home && dataObj.employeeDegree_Home.length>0">
            <van-checkbox v-model="checked" shape="square" @change="changeHandeler" checked-color="#456bc4"><span class="text">含非全日制</span></van-checkbox>
          </div>
          <div class="port" style="padding-bottom: 0.11rem;">
            <echarts-pie :title="propsObject.stdyForm" :pieColorList="ygXueliListColor" :toChild="dataObj.employeeDegree_Home" />
          </div>
        </div>
      </div>
      <!-- 职工岗位分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工岗位分布情况'" :text="employeePost_Home_Desc"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox" style="margin-bottom:0">
            {{dataObj.employeePost_Home_Content}}
          </div>
          <div class="port" style="padding-bottom: 0.11rem;">
            <echarts-pie :pieColorList="employeePostColor" :toChild='dataObj.employeePost_Home' />
          </div>
        </div>
      </div>
      <!-- 职工专业背景情况 -->
      <div class="titleContainer">
        <public-title :title="'职工专业背景情况'" :text="['指职工“最高学历”毕业院校所学专业的分布情况。']"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeSpecialized_Home_Content}}
          </div>
          <div class="port" style="padding-top: 0.1rem;">
            <echart-bar-y :id="'barY3'" :barItemColor="'#4481F6'" :data="dataObj.employeeSpecialized_Home" />
          </div>
        </div>
      </div>
      <!-- 职工职称分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工职称分布情况'" :text="['指按“职称系列”统计职工各系列的分布情况；点击系列，可查看每个系列中“职称级别”（即：正高、副高、中级、初级）的职工分布情况。']"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeejobTitle_Home_Content}}
          </div>
          <div class="lookMore" v-show="dataObj.seniorTitle_Home && dataObj.seniorTitle_Home.length>0" style="height:2.4rem"  @click="lookMoreWorderDeatils('data','seniorTitle_Home','人才概况','正高职工名单',dataObj.employeeProvince_Home_Content)"><span>正高职工名单</span></div>
          <div class="haveNoPosition" v-if="dataObj.employeejobTitle_Y_HomeApp || dataObj.employeejobTitle_N_HomeApp">
            <div class="box">
              <h3>有职称人员</h3>
              <p> {{dataObj.employeejobTitle_Y_HomeApp.value1}}人 <br/>( {{dataObj.employeejobTitle_Y_HomeApp.value2}}%)</p>
            </div>
            <div class="box">
              <h3>无职称人员</h3>
              <p> {{dataObj.employeejobTitle_N_HomeApp.value1}}人 <br/>( {{dataObj.employeejobTitle_N_HomeApp.value2}}%)</p>
            </div>
          </div>
          <div class="dataDetails" v-if="dataObj.work_Home && dataObj.work_Home.length>0">
            <h4>工程类</h4>
            <dl v-if="dataObj.work_Home">
              <dd v-for="(item,index) in dataObj.work_Home">
                {{ item.pur_yr }}<br />{{(item.pur_mth_amt && (item.pur_mth_amt*1).toLocaleString())}}
              </dd>
            </dl>
          </div>
          <div class="port" style="padding-bottom: 0.04rem;">
            <!-- <echart-bar-sup :id="'barSup1'" :barItemColor="['#3C7FFE','#75A1FF','#B9CFFF','#D1DDEB','#D4D4D4']" :data="dataObj.employeejobTitle_Home" /> -->
            <duiji-bar :toChild="dataObj.employeejobTitle_Home" />
          </div>
        </div>
      </div>
      <!-- 职工获奖情况 -->
      <div class="titleContainer">
        <public-title :title="'职工获奖情况'" :text="employeePrize_Home_Desc"></public-title>
        <div class="doduleBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeePrize_Home_Content}}
          </div>
          <div class="port" style="padding-top: 0.15rem;">
            <echart-bar-x :id="'barX2'" :fontSize="'15px'" :width="'12px'" :barItemColor="'#F5876A'" :data="dataObj.employeePrize_Home" />
          </div>
        </div>
      </div>
      <!--  -->
    </div>
    <global-filter ref="globalFilterRef" @submit="submitFilter" @reset="resetFilter"></global-filter>
    <item-tip :show="show1" @handleClose="show1=false" :content="talentOverview" />
    <item-tip :show="show1_4" @handleClose="show1_4=false" :content="talentOverview4" />
    <item-tip :type="'card'" :show="show2" @handleClose="show2=false" :title="'业务板块说明'" :content="textFive" />
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue'
import echartBarX from "@/components/echartBarX";
import echartBarXs from "@/components/echartBarXs";
import echartBarY from "@/components/echartBarY";
import echartsPie from '@/components/echartsPie.vue'
import echartBarSup from "@/components/echartBarSup";
import globalFilter from '@/components/globalFilter.vue'
import itemTip from '@/components/itemTip.vue'
import duijiBar from '@/components/duijiBar.vue'
import { getHrData } from "@/http/api";
import { getEmployeePostHomeColors, getEmployeeDegreeHomeColors } from "@/utils/data";
export default {
  name: 'home',
  components: {
    publicTitle,
    echartBarX,
    echartBarXs,
    echartBarY,
    echartsPie,
    echartBarSup,
    globalFilter,
    itemTip,
    duijiBar
  },
  data() {
    return {
      show1: false, //人才概况职工总人数
      show1_4: false, //本次分析包含的职工人数
      show2: false,//是否显示五大六类说明弹框
      talentOverview: [
        '1. 统计时间：截止2025年03月31日（时点数）。',
        '2. 统计范围：集团全级次控股企业和一级参股企业970家（其中控股企业680家），目前已采集468家企业人员信息（其中控股企业416家），其余504家因注销、在境外、未实际管控等原因暂未填报（其中控股企业264家）。',
        '3. 人员类别：职工。办理任用、聘用等手续，人事关系在统计单位的在册在岗人员，以及派往境内外参股（出资）单位的人员；不含借调、返聘以及控股单位中参股方派出的人员。',
        '4. 职工政治面貌概况：集团现有中共党员25,572人、共青团员21,402人、民主党派148人（包含九三学社社员、民革会员、民建会员、民进会员、民盟盟员、农工党党员、台盟盟员、致公党党员）、无党派民主人士222人、群众111,482人。'
      ],
      talentOverview4: [
        '有1,979人的数据因工作地（境外）法律对个人隐私数据采集的限制，未纳入本次分析范围。'
      ],
      textFive: [
        '1. 五大主业是指计算产业、集成电路、数据应用、高新电子、网络安全五大主业板块。',
        '2. 六类支撑是指供应链服务、资产管理、产业金融、产业园区、电子制造、显示产业六类支撑板块。',
      ],
      employeePost_Home_Desc: [
        '指按“岗位类型”，统计职工各岗位分布情况。岗位类型说明如下：',
        '1.管理类：集团公司处级副职及以上，企业中层副职及以上的定义为管理类。',
        '2.科技类：直接从事科技活动以及专门从事科技活动管理和为科技活动提供直接服务的人员。包括：直接从事研发活动的人员（研发人员）；从事科技管理工作的人员；为科技活动提出直接服务的人员。',
        '3.技能类：一般指掌握一定的专业技能、从事某种技术性较强工种工作的人员。例如技师、技工等人员。',
        '4.党群类：面向党的基层组织的党务工作者等职业，组织工作、宣传工作、纪检工作、党群工作等。',
        '5.市场类：面向党的基层组织的党务工作者等职业，组织工作、宣传工作、纪检工作、党群工作等。',
        '6.职能类：包含行政、人事、财务、会计、文秘等，职能类是指公司中具有计划、组织、指挥能力的岗位。',
        '7.其他：无法归属在以上分类的其他岗位人员。',
      ],
      employeePrize_Home_Desc: [
        '指按“获奖级别”，统计职工获奖的分布情况。获奖级别说明如下：',
        '1.国家级：由国家政府机构或组织授予的奖励；“一级学会”获奖等同于“国家级”获奖。',
        '2.省部级：由中华人民共和国各省、自治区、直辖市党委或人民政府直接授予的奖励；\n“二级学会”获奖等同于“省部级”获奖；\n“副省级城市”获奖，等同于“省部级”获奖；\n“中国电子集团”颁发的奖励，等同于“省部级”获奖；\n“军队”颁发的奖项，如为中央军委颁发，则为国家级；如为中央军委科技委等部门颁发，则为省部级。',
        '3.省部级以下：未达到以上级别的获奖，如地级市获奖、其他组织或团体办法的奖励。',
      ],
      ygXueliListColor: ['#5673EF', '#37C5CB', '#5398F7', '#6DC6FA'],
      employeePostColor: ['#FFDE95', '#A1A8FF', '#61BFF5', '#E95D56', '#ACFAD8', '#4E80F7', '#D4D4D4'],
      checked: false,
      isShowJJJ: true,
      propsObject: {
        "stdyForm": sessionStorage.getItem("stdyForm") ? sessionStorage.getItem("stdyForm") : "全日制", //全日制
        "district": "",  // 区域名称（目前为省份名称）
        "districtType": "G",	// 区域类型（G：全球；P：省）
        "moreChooseType": sessionStorage.getItem("ownOrgName") != "全集团" ? "2" : "0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName") != "全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg": "",//并表，默认传空即传全集团
      },
      show: false,
      ownOrgName: "全集团",//是否是二级有企业单位进来
    }
  },
  computed: {
    dataObj() {
      return this.$store.state.data
    },
  },
  watch:{
    '$route': {
      // deep: true,
      // immediate: true,
      handler(to,from) {
        if(to.path.indexOf('/home')!=-1 || from.path.indexOf('/home')!=-1) { //返回首页再回来 重置数据
          this.$refs.globalFilterRef.reset();
        }
      }
    }
  },
  mounted() {
    this.initData();
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
    if (this.ownOrgName != '全集团') {
      $(".discribBox").hide()
    }
  },
  methods: {
    // 重置查询
    resetFilter() {
      // console.log('测试一下啊');
      this.propsObject = {
        "stdyForm": "全日制", //全日制
        "district": "",  // 区域名称（目前为省份名称）
        "districtType": "G",	// 区域类型（G：全球；P：省）
        "moreChooseType": sessionStorage.getItem("ownOrgName") != "全集团" ? "2" : "0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName") != "全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg": "",//并表，默认传空即传全集团
      }

      this.checked = false
      this.initData();
      //从sessionStorage中获取是二级单位还是全集团
      this.ownOrgName = sessionStorage.getItem("ownOrgName")
      //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
      if (this.ownOrgName != '全集团') {
        $(".discribBox").hide()
      }
    },
    //从筛选子组件传过来的值，判断是否显示京津冀
    submitFilter(val) {
      if (val.district) {
        this.isShowJJJ = false
      } else {
        this.isShowJJJ = true
      }
      // 获取数据
      this.propsObject = val;
      if (!this.propsObject.stdyForm) {
        this.propsObject.stdyForm = sessionStorage.getItem("stdyForm") ? sessionStorage.getItem("stdyForm") : "全日制"
      }
      this.initData();
      window.scrollTo(0, 0);
    },
    //初始化数据
    initData() {
      let params = {
        mapperInterface: [
          "employeeSum_Home", "employeeFiveSum_Home", "employeeFiveRatio_Home",
          "employeeSixSum_Home", "employeeSixRatio_Home", "employeeManageSum_Home",
          "employeeManageRatio_Home", "employeeTechnologySum_Home", "employeeTechnologyRatio_Home",
          "employeeSkillSum_Home", "employeeSkillRatio_Home", "employeeEnterprise_Home",
          "employeeBusiness_Home", "employeeBeijingSum_Home", "employeeCSJSum_Home",
          "employeeYGASum_Home", "employeeProvince_Home", "employeeDegree_Home",
          "employeePost_Home", "employeeSpecialized_Home", "employeejobTitle_Home",
          "employeePrize_Home", "top1_Home", "top2_Home", "top3_Home", "top4_Home", "work_Home", 
          "employeejobTitle_Y_HomeApp", "employeejobTitle_N_HomeApp","seniorTitle_Home","doctorTitle_AppInfo_Home"],
        page: "Home"
      }
      params = { ...params, ...this.propsObject }
      sessionStorage.setItem("dctDetailParams",JSON.stringify(params))
      getHrData(params).then((res) => {
        if (res && res.code == 200) {
          if (res.data.employeePost_Home.length) {
            this.employeePostColor = getEmployeePostHomeColors(res.data.employeePost_Home);
          }
          if (res.data.employeeDegree_Home.length) {
            this.ygXueliListColor = getEmployeeDegreeHomeColors(res.data.employeeDegree_Home);
          }
          this.$store.dispatch("commitChangeData", res.data);
        }
      }).catch((err) => {
        console.log(err);
      });
    },

    lookMoreHandler(type, params, title, moduleTitle, desc) {
      let scrollX = window.scrollX;
      let scrollY = document.documentElement.scrollTop || document.body.scrollTop;
      sessionStorage.setItem('savedPosition', JSON.stringify({ x: scrollX, y: scrollY }));
      sessionStorage.setItem('desc', JSON.stringify(desc));
      this.$router.push({ path: '/moreData', query: { type, params, title, moduleTitle, moreList: this.propsObject.moreList, consldEntpFlg: this.propsObject.consldEntpFlg } })
    },
    lookMoreWorderDeatils(type, params, title, moduleTitle, desc) {
      let scrollX = window.scrollX;
      let scrollY = document.documentElement.scrollTop || document.body.scrollTop;
      sessionStorage.setItem('savedPosition', JSON.stringify({ x: scrollX, y: scrollY }));
      sessionStorage.setItem('desc', JSON.stringify(desc));
      this.$router.push({ path: '/workersDetails', query: { type, params, title, moduleTitle, moreList: this.propsObject.moreList, consldEntpFlg: this.propsObject.consldEntpFlg } })
    },
    //职工学历分布情况，全日制和含非全日制切换
    changeHandeler(val) {
      this.propsObject.stdyForm = val ? '含非全日制' : '全日制';
      sessionStorage.setItem("stdyForm", this.propsObject.stdyForm);
      this.initData();
    }
  },
}
</script>

<style lang="scss" scoped>
.rcBox {
  background-color: #f7f8f9;
  font-family: "微软雅黑";
  padding-bottom: 1.8rem;
  overflow-x: hidden;
  position: relative;
  height: 100%;
  overflow-y: auto;
  // z-index: 1000;
  .classjjj {
    .box {
      margin-left: 0.25rem !important;
      &:first-child {
        margin-left: 0 !important;
      }
    }
  }
  .tipBtn {
    width: 0.4rem;
    height: 0.4rem;
  }
  .bannerBox {
    position: relative;
    //职工总人数
    .banner {
      img {
        width: 100%;
        height: 7rem;
      }
    }
    .worderNum {
      position: absolute;
      left: 0.86rem;
      top: 0.4rem;
      color: #fff;

      h4 {
        font-size: 0.27rem;
        position: relative;
        display: inline-block;
        strong {
          font-weight: 400;
        }
        img {
          position: absolute;
          right: -25px;
          top: -7px;
        }
      }
      h2 {
        font-size: 0.5rem;
        margin-top: -0.05rem;
        font-weight: 600;

        span {
          font-size: 0.32rem;
        }
      }
    }
    .worderNum1 {
    }
    .worderNum2 {
      top: 1.55rem;
      h2 {
        font-size: 0.8rem;
        color: #ffe898;
      }
    }
    .worderNum3 {
      top: 3rem;
    }
    .worderNum4 {
      top: 4.2rem;
    }
    //五大六类
    .fiveSixPerson {
      padding: 0.3rem 0.3rem 0.3rem;
      margin-top: -2rem;
      .box {
        padding: 0.3rem;
        background: #ffffff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 0.1rem;
        margin: 0 auto 0;
        // z-index: 99;
        display: flex;
        position: relative;
        img {
          position: absolute;
          right: 0;
          top: 0;
        }
        div {
          flex: 1;
          text-align: center;
          font-size: 0.35rem;
          div {
            margin-bottom: 0.3rem;
            &:last-child {
              margin-bottom: 0 !important;
            }
          }
        }
        .fiveNum {
          position: relative;
          padding-right: 0.2rem;
          ::after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 0.01rem;
            height: 100%;
            border-right: 1px dashed #cccccc;
          }
        }
        .sixNum {
          padding-left: 0.2rem;
        }
      }
    }
  }
  //三大岗位板块：管理/科技/技能
  .container {
    padding: 0 0.3rem;
    .station {
      display: flex;
      margin-top: 0.02rem;
      .box {
        flex: 1;
        font-size: 0.32rem;
        text-align: center;
        background: #fff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 0.1rem;
        padding: 0.3rem 0;
        margin-left: 0.1rem;
        &:first-child {
          margin-left: 0;
        }
        div {
          margin-top: 0.23rem;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .checkBoxs {
      float: right;
      margin-top: 0.26rem;
      .text {
        font-size: 0.3rem;
        color: #456bc4;
      }
    }
    .dataDetails {
      margin-top: 0.32rem;
      margin-bottom: 0.08rem;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.1rem;
      padding: 0.3rem;
      h4 {
        font-size: 0.32rem;
        color: #000;
        margin-bottom: 0.2rem;
        font-weight: 600;
      }
      dl {
        color: #333;
        font-size: 0.32rem;
        font-weight: 400;
        display: flex;
        dd {
          margin-right: 0.05rem;
          flex: 1;
          text-align: center;
          font-size: 0.32rem;
          line-height: 0.6rem;
          position: relative;
          &:last-child {
            margin-right: 0;
            &::after {
              display: none;
            }
          }
          &::after {
            content: "";
            height: 0.8rem;
            border-left: 1px dashed #cccccc;
            position: absolute;
            right: 0;
            top: 0.25rem;
          }
        }
      }
    }
  }
}
::v-deep i.van-icon.van-icon-success {
  border: 2px solid #456bc4 !important;
  border-radius: 4px;
}
</style>
