<template>
  <div class="rcBox">
    <div class="container">
      <!-- 职工岗位分布情况 -->
      <public-title :title="'职工岗位分布情况'" :text="['指按“岗位类型”，统计职工各岗位分布情况。岗位类型说明如下：','1.管理类：集团公司处级副职及以上，企业中层副职及以上的定义为管理类。','2.科技类：直接从事科技活动以及专门从事科技活动管理和为科技活动提供直接服务的人员。包括：直接从事研发活动的人员（研发人员）；从事科技管理工作的人员；为科技活动提出直接服务的人员。','3.技能类：一般指掌握一定的专业技能、从事某种技术性较强工种工作的人员。例如技师、技工等人员。','4.党群类：面向党的基层组织的党务工作者等职业，组织工作、宣传工作、纪检工作、党群工作等。','5.市场类：指负责制定市场营销策略和方向，为销售的前端引导和中期服务监督的市场营销、销售、销售客服类人员。','6.职能类：包含行政、人事、财务、会计、文秘等，职能类是指公司中具有计划、组织、指挥能力的岗位。','7.其他：无法归属在以上分类的其他岗位人员。']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeePost_Work_Content}}
        </div>
        <div class="port" style="padding-bottom:0.1rem">
          <echarts-pie :pieColorList="employeePostColor" :toChild='dataObj.employeePost_Work' />
        </div>
      </div>
      <!-- 职工职称分布情况 -->
       <div class="titleContainer">
      <public-title :title="'职工职称分布情况'" :text="['指按“职称系列”统计职工各系列的分布情况；点击系列，可查看每个系列中“职称级别”（即：正高、副高、中级、初级）的职工分布情况。']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeJobTitle_Work_Content}}
        </div>
        <div class="haveNoPosition"  v-if="dataObj.employeejobTitle_Y_Work || dataObj.employeejobTitle_N_Work">
            <div class="box">
              <h3>有职称人员</h3>
              <p> {{dataObj.employeejobTitle_Y_Work.value1}}人 <br/>({{dataObj.employeejobTitle_Y_Work.value2}}%)</p>
            </div>
            <div class="box">
              <h3>无职称人员</h3>
              <p> {{dataObj.employeejobTitle_N_Work.value1}}人 <br/>({{dataObj.employeejobTitle_N_Work.value2}}%)</p>
            </div>
          </div>
        <div class="dataDetails" style="margin-bottom:0!important" v-if="dataObj.work_Home && dataObj.work_Home.length>0">
          <h4>工程类</h4>
          <dl v-if="dataObj.work_Home">
              <dd v-for="(item,index) in dataObj.work_Home" :key="index">
               {{ item.pur_yr }}<br/>{{item.pur_mth_amt && (item.pur_mth_amt*1).toLocaleString()}}
              </dd>
            </dl>
        </div>
        <div class="port" style="padding-bottom:0.1rem">
          <duiji-bar :toChild="dataObj.employeeJobTitle_Work" />
        </div>
      </div>
       </div>
      <!-- 职工司龄分布情况 -->
       <div class="titleContainer">
      <public-title :title="'职工司龄分布情况'" :text="['指按“本单位入职时间”统计职工各司龄的分布情况；其中司龄划分如下：','• 10年以上（指＞10年）','• 6-10年（指＞6年 且 ≤10年）','• 3-6年（指＞3年 且 ≤6年）','• 1-3年（指＞1年 且 ≤3年）','• 1年及以下（指≤1年）']"></public-title>
      <div class="doduleBox pictorialBarBox">
        <div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeSeniority_Work_Content}}
        </div>
        <div class="port">
          <pictorial-bar :colorBg="'#4481F6'" label="平均司龄" :toChild='dataObj.employeeSeniority_Work' :value="dataObj.employeeSeniorityAverage_Work" />
        </div>
      </div>
       </div>
      <!-- 职工集团工作年限分布情况 -->
       <div class="titleContainer">
      <public-title :title="'职工集团工作年限分布情况'" :text="['指按“中国电子系统内工作年限”统计职工各年限的分布情况；其中年限划分如下：','• 10年以上（指＞10年）','• 6-10年（指＞6年 且 ≤10年）','• 3-6年（指＞3年 且 ≤6年）','• 1-3年（指＞1年 且 ≤3年）','• 1年及以下（指≤1年）']"></public-title>
      <div class="doduleBox pictorialBarBox">
         <div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeWorkAge_Work_Content}}
        </div>
        <div class="port">
          <pictorial-bar label="平均集团工作年限" :colorBg="'#20C3BE'" :toChild='dataObj.employeeWorkAge_Work' :value="dataObj.employeeWorkAgeAverage_Work" />
        </div>
      </div>
       </div>
      <!-- 职工工龄分布情况 -->
       <div class="titleContainer">
      <public-title :title="'职工工龄分布情况'" :text="['指按“参加工作时间”统计职工各工龄的分布情况；其中工龄划分如下：','• 20年以上（指＞20年）','• 10-20年（指＞10年 且 ≤20年）','• 5-10年（指＞5年 且 ≤10年）','• 1-5年（指＞1年 且 ≤5年）','• 1年及以下（指≤1年）']"></public-title>
      <div class="doduleBox pictorialBarBox">
         <div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeWorkSeniority_Work_Content}}
        </div>
        <div class="port">
          <pictorial-bar :colorBg="'#FFC683'" :toChild='dataObj.employeeWorkSeniority_Work' :value="dataObj.employeeWorkSeniorityAverage_Work" />
        </div>
      </div>
       </div>
    </div>
    <global-filter @submit="submitFilter" @reset="resetFilter"></global-filter>
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue';
import globalFilter from '@/components/globalFilter.vue';
import itemTip from '@/components/itemTip.vue';
import echartsPie from '@/components/echartsPie.vue';
import echartBarSup from "@/components/echartBarSup";
import pictorialBar from '@/components/pictorialBar.vue';
import duijiBar from '@/components/duijiBar.vue'
import { getHrData } from "@/http/api";
import { getEmployeePostHomeColors } from "@/utils/data";
export default {
  name: 'workMsg',
  components: {
    publicTitle,
    itemTip,
    globalFilter,
    echartsPie,
    echartBarSup,
    pictorialBar,
    duijiBar
  },
  data() {
    return {
      textPublic: ['本指标的注释文字：XXXXXXXX'],
      ygXueliListColor: ['#5673EF', '#37C5CB', '#5398F7', '#6DC6FA'],
      employeePostColor: ['#FFDE95', '#A1A8FF', '#61BFF5', '#E95D56', '#ACFAD8', '#4E80F7', '#D4D4D4'],
      checked: false,
      propsObject: {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      },
      show: false,
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  computed: {
    dataObj() {
      return this.$store.state.workData
    }
  },
  mounted() {
    this.initData();
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
    if(this.ownOrgName!='全集团'){
      $(".discribBox").hide()
    }
  },
  methods: {
    // 重置查询
    resetFilter() {
      this.propsObject = {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      }
      this.initData();
      //从sessionStorage中获取是二级单位还是全集团
      this.ownOrgName = sessionStorage.getItem("ownOrgName")
      //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
      if (this.ownOrgName != '全集团') {
        $(".discribBox").hide()
      }
    },
    //从筛选子组件传过来的值，判断是否显示京津冀
    submitFilter(val) {
      // 获取数据
      this.propsObject = val;
      this.initData();
      window.scrollTo(0,0);
    },
    //初始化数据
    initData() {
      
      let params = {
        mapperInterface: [
          "employeePost_Work", "employeeJobTitle_Work", "employeeSeniorityAverage_Work",
          "employeeSeniority_Work", "employeeWorkAgeAverage_Work", "employeeWorkAge_Work",
          "employeeWorkSeniorityAverage_Work", "employeeWorkSeniority_Work","work_Home",
          "employeejobTitle_Y_Work","employeejobTitle_N_Work"
        ],
        page: "Work"
      }
      params = { ...params, ...this.propsObject }
      getHrData(params).then((res) => {
        if (res && res.code == 200) {
          if(res.data.employeePost_Work.length) {
            this.employeePostColor = getEmployeePostHomeColors(res.data.employeePost_Work);
          }
          this.$store.dispatch("commitChangeWorkData", res.data);
        }
      }).catch((err) => {
        console.log(err);
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.rcBox {
  padding-bottom: 1.8rem;
  font-family: "微软雅黑";
  .tipBtn {
    width: 0.4rem;
    height: 0.4rem;
  }
  .bannerBox {
    position: relative;
    //职工总人数
    .banner {
      img {
        width: 100%;
        // height: 6.37rem;
      }
    }
    .worderNum {
      position: absolute;
      left: 0.86rem;
      top: 1.96rem;
      color: #fff;

      h4 {
        font-size: 0.4rem;
        position: relative;
        display: inline-block;
        img {
          position: absolute;
          right: -25px;
          top: -5px;
        }
      }
      h2 {
        font-size: 0.8rem;
        margin-top: 0.25rem;
        font-weight: 600;

        span {
          font-size: 0.32rem;
        }
      }
    }
  }
  //三大岗位板块：管理/科技/技能
  .container {
    padding: 0 0.3rem;
    .station {
      display: flex;
      .box {
        flex: 1;
        font-size: 0.32rem;
        text-align: center;
        background: #fff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 10px;
        padding: 0.3rem 0;
        margin-left: 0.1rem;
        &:first-child {
          margin-left: 0;
        }
        div {
          margin-top: 0.23rem;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .checkBoxs {
      float: right;
      margin-top: 0.3rem;
      .text {
        font-size: 0.3rem;
        color: #456bc4;
      }
    }
    .dataDetails {
      margin-top: 0.32rem;
      margin-bottom: .2rem;
       box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.1rem;
      padding: .3rem;
      h4 {
        font-size: 0.32rem;
        color: #000;
        margin-bottom: 0.2rem;
        font-weight: 600;
      }
      dl {
        color: #333;
        font-size: 0.32rem;
        font-weight: 400;
        display: flex;
        dd {
          margin-right: 0.05rem;
          flex: 1;
          text-align: center;
          font-size: 0.32rem;
          line-height: .6rem;
          position:relative;
          &:last-child {
            margin-right: 0;
            &::after{
              display: none;
            }
          }
          &::after{
            content:'';
            height:.8rem;
            border-left: 1px dashed #CCCCCC;
            position: absolute;
            right: 0;
            top: 0.25rem;
          }
        }
      }
    }
  }
}
::v-deep i.van-icon.van-icon-success {
  border: 2px solid #456bc4 !important;
  border-radius: 4px;
}
</style>
