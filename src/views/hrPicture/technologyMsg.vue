<template>
  <div class="rcBox">
    <global-filter @submit="submitFilter" @reset="resetFilter"></global-filter>
    <div class="bannerBox">
      <div class="banner"><img src="../../assets/img/bg3.png" alt=""></div>
      <!-- 职工总人数 -->
      <div class="floatTop">
        <div class="worderNum">
          <h4>总人数<img class="tipBtn" @click="show1=true" src="../../assets/img/tip.png" alt=""></h4>
          <h2 v-if="dataObj.employeeTechnology_Technology">{{( dataObj.employeeTechnology_Technology*1).toLocaleString()|| 0}}<span>人</span></h2>
          <h2 v-if="!dataObj.employeeTechnology_Technology">0<span>人</span></h2>
          <h5 :style="{marginTop: '0.2rem'}">占比<span :style="{marginLeft: '0.3rem'}">{{dataObj.employeeTechnologyRatio_Technology || '0.0%' }}</span></h5>
        </div>
      </div>
    </div>
    <div class="container">
    <!-- 企业分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工企业分布情况'" :text="['指“科技类”岗位中，按企业统计职工的分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="lookMore" v-show="dataObj.employeeEnterprise_Technology && dataObj.employeeEnterprise_Technology.length > 10" @click="lookMoreHandler('technologyData','employeeEnterprise_Technology','科技岗位','职工企业分布情况',dataObj.employeeEnterprise_Technology_Content)"><span>查看更多</span></div>
          <div class="discribBox">
            {{dataObj.employeeEnterprise_Technology_Content}}
          </div>
          <div class="port" style="padding-top:0.1rem">
            <echart-bar-y :id="'barY1'" :barItemColor="'#4481F6'" :data="dataObj.employeeEnterprise_Technology" />
          </div>
        </div>
      </div>
      <!-- 职称分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工职称分布情况'" :text="['指“科技类”岗位中，按“职称系列”统计职工各系列的分布情况；点击系列，可查看每个系列中“职称级别”（即：正高、副高、中级、初级）的职工分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeJobTitle_Technology_Content}}
          </div>
          <div class="haveNoPosition"  v-if="dataObj.employeejobTitle_Y_Technology || dataObj.employeejobTitle_N_Technology">
            <div class="box">
              <h3>有职称人员</h3>
              <p> {{dataObj.employeejobTitle_Y_Technology.value1}}人 <br/>({{dataObj.employeejobTitle_Y_Technology.value2}}%)</p>
            </div>
            <div class="box">
              <h3>无职称人员</h3>
              <p> {{dataObj.employeejobTitle_N_Technology.value1}}人 <br/>({{dataObj.employeejobTitle_N_Technology.value2}}%)</p>
            </div>
          </div>
          <div class="dataDetails" style="margin-bottom:0!important" v-if="dataObj.work_Home && dataObj.work_Home.length>0">
            <h4>工程类</h4>
            <dl v-if="dataObj.work_Home">
              <dd v-for="(item,index) in dataObj.work_Home" :key="index">
               {{ item.pur_yr }}<br/>{{item.pur_mth_amt && (item.pur_mth_amt*1).toLocaleString()}}
              </dd>
            </dl>
          </div>
          <div class="port" style="padding-bottom:0.1rem">
            <!-- <echart-bar-sup :id="'barSup1'" :barItemColor="['#3C7FFE','#75A1FF','#B9CFFF','#D1DDEB','#D4D4D4']" :data="dataObj.employeeJobTitle_Technology" /> -->
            <duiji-bar :toChild="dataObj.employeeJobTitle_Technology" />
          </div>
        </div>
      </div>
      <!-- 学历分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工学历分布情况'" :text="['指“科技类”岗位中，按“全日制\\含非全日制最高学历”，统计对比职工学历分布情况']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeDegree_Technology_Content}}
          </div>
          <div class="port" style="padding-top:0.03rem;padding-bottom:0.1rem">
            <echart-bar-dx :id="'barDx1'" :barItemColor="['#53B5FF','#FFC683']" :data="dataObj.employeeDegree_Technology" />
          </div>
        </div>
      </div>
      <!-- 司龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工司龄分布情况'" :text="employeeSeniority_Technology_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeSeniority_Technology_Content}}
          </div>
          <div class="port">
            <pictorial-bar :id="'pictorilaBar1'" label="平均司龄" :colorBg="'#4481F6'" :toChild='dataObj.employeeSeniority_Technology' :value="dataObj.employeeSeniorityAverage_Technology" />
          </div>
        </div>
      </div>
      <!-- 集团工作年限分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工集团工作年限分布情况'" :text="employeeWorkAge_Technology_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeWorkAge_Technology_Content}}
          </div>
          <div class="port">
            <pictorial-bar :id="'pictorilaBar2'" label="平均集团工作年限" :colorBg="'#20C3BE'" :toChild='dataObj.employeeWorkAge_Technology' :value="dataObj.employeeWorkAgeAverage_Technology" />
          </div>
        </div>
      </div>
      <!-- 年龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工年龄分布情况'" :text="employeeAge_Technology_Desc" unit="人"></public-title>
        <div class="doduleBox pictorialBarBox">
          <div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeAge_Technology_Content}}
          </div>
          <div class="port">
            <pictorial-bar label="平均年龄" :colorBg="'#70D9FF'" :toChild='dataObj.employeeAge_Technology' :value="dataObj.employeeAgeAverage_Technology" />
          </div>
        </div>
      </div>
      <!-- 工龄分布情况 -->
      <div class="titleContainer">
        <public-title :title="'职工工龄分布情况'" :text="employeeWorkSeniority_Technology_Desc"></public-title>
        <div class="doduleBox pictorialBarBox">
          	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeWorkSeniority_Technology_Content}}
          </div>
          <div class="port">
            <pictorial-bar :id="'pictorilaBar4'" :colorBg="'#FFC683'" :toChild='dataObj.employeeWorkSeniority_Technology' :value="dataObj.employeeWorkSeniorityAverage_Technology" />
          </div>
        </div>
      </div>
      <!-- 获奖情况 -->
      <div class="titleContainer">
        <public-title :title="'职工获奖情况'" :text="employeePrize_Technology_Desc"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeePrize_Technology_Content}}
          </div>
          <div class="port" style="padding-top:0.15">
            <echart-bar-x :id="'barX2'"  :fontSize="'15px'"  :width="'12px'" :barItemColor="'#F5876A'" :data="dataObj.employeePrize_Technology" />
          </div>
        </div>
      </div>
    </div>
    <item-tip :show="show1" @handleClose="show1=false" :content="talentOverview" />
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue';
import echartBarDx from "@/components/echartBarDX";
import echartBarX from "@/components/echartBarX";
import echartBarY from "@/components/echartBarY";
import echartsPie from '@/components/echartsPie.vue';
import echartBarSup from "@/components/echartBarSup";
import pictorialBar from '@/components/pictorialBar.vue';
import globalFilter from '@/components/globalFilter.vue';
import echartsWater from '@/components/echartsWater.vue';
import itemTip from '@/components/itemTip.vue';
import duijiBar from '@/components/duijiBar.vue'
import { getHrData } from "@/http/api";
export default {
  name: 'technologyMsg',
  components: {
    publicTitle,
    echartBarX,
    echartBarY,
    echartsPie,
    echartBarSup,
    globalFilter,
    pictorialBar,
    itemTip,
    echartBarDx,
    echartsWater,
    duijiBar
  },
  data() {
    return {
      show1: false, //人才概况职工总人数
      talentOverview: [
        '岗位为“科技类”的人员，即直接从事科技活动以及专门从事科技活动管理和为科技活动提供直接服务的人员。以及“科技类”人员占“职工总人数”的百分比。',
      ],
      employeeSeniority_Technology_Desc: [
        '指“科技类”岗位中，按“本单位入职时间”统计职工各司龄的分布情况；其中司龄划分如下：',
        '• 10年以上（指＞10年）',
        '• 6-10年（指＞6年 且 ≤10年）',
        '• 3-6年（指＞3年 且 ≤6年）',
        '• 1-3年（指＞1年 且 ≤3年）',
        '• 1年及以下（指≤1年）',
      ],
      employeeWorkAge_Technology_Desc: [
        '指“科技类”岗位中，按“中国电子系统内工作年限”统计职工各年限的分布情况；其中年限划分如下：',
        '• 10年以上（指＞10年）',
        '• 6-10年（指＞6年 且 ≤10年）',
        '• 3-6年（指＞3年 且 ≤6年）',
        '• 1-3年（指＞1年 且 ≤3年）',
        '• 1年及以下（指≤1年）',
      ],
      employeeAge_Technology_Desc: [
      '指“科技类”岗位中，按“出生日期”统计职工各年龄的分布情况：其中年龄划分如下：',
        '• 60岁及以上（指≥60岁）',
        '• 55-59岁（指≥55岁 且 ＜60岁）',
        '• 51-54岁（指≥51岁 且 ＜55岁）',
        '• 46-50岁（指≥46岁 且 ＜51岁）',
        '• 41-45岁（指≥41岁 且 ＜46岁）',
        '• 36-40岁（指≥36岁 且 ＜41岁）',
        '• 35岁及以下（指＜36岁）'
      ],
      employeeWorkSeniority_Technology_Desc: [
        '指“科技类”岗位中，按“参加工作时间”统计职工各工龄的分布情况；其中工龄划分如下：',
        '• 20年以上（指＞20年）',
        '• 10-20年（指＞10年 且 ≤20年）',
        '• 5-10年（指＞5年 且 ≤10年）',
        '• 1-5年（指＞1年 且 ≤5年）',
        '• 1年及以下（指≤1年）',
      ],
      employeePrize_Technology_Desc: [
        '指“科技类”岗位中，按“获奖级别”，统计职工获奖的分布情况。获奖级别说明如下：',
        '1.国家级：由国家政府机构或组织授予的奖励；“一级学会”获奖等同于“国家级”获奖。',
        '2.省部级：由中华人民共和国各省、自治区、直辖市党委或人民政府直接授予的奖励；\n“二级学会”获奖等同于“省部级”获奖；\n“副省级城市”获奖，等同于“省部级”获奖；\n“中国电子集团”颁发的奖励，等同于“省部级”获奖；\n“军队”颁发的奖项，如为中央军委颁发，则为国家级；如为中央军委科技委等部门颁发，则为省部级。',
        '3.省部级以下：未达到以上级别的获奖，如地级市获奖、其他组织或团体办法的奖励。',
      ],
      textPublic: ['本指标的注释文字：XXXXXXXX'],
      employeePostColor: ['#FFDE95', '#A1A8FF', '#61BFF5', '#E95D56', '#ACFAD8', '#4E80F7', '#D4D4D4'],
      propsObject: {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      },
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  computed: {
    dataObj() {
      return this.$store.state.technologyData
    }
  },
  mounted() {
    this.initData();
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
    if(this.ownOrgName!='全集团'){
      $(".discribBox").hide()
    }
  },
  methods: {
    // 重置查询
    resetFilter() {
      this.propsObject = {
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      }
      this.initData();
      //从sessionStorage中获取是二级单位还是全集团
      this.ownOrgName = sessionStorage.getItem("ownOrgName")
      //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
      if (this.ownOrgName != '全集团') {
        $(".discribBox").hide()
      }
    },
    //从筛选子组件传过来的值，判断是否显示京津冀
    submitFilter(val) {
      // 获取数据
      this.propsObject = val;
      this.initData();
      window.scrollTo(0,0);
    },
    //初始化数据
    initData() {
      let params = {
        mapperInterface: [
          "employeeTechnology_Technology", "employeeTechnologyRatio_Technology", "employeeEnterprise_Technology",
          "employeeJobTitle_Technology", "employeeDegree_Technology", "employeeSeniorityAverage_Technology",
          "employeeAge_Technology", "employeeAgeAverage_Technology",
          "employeeSeniority_Technology", "employeeWorkAgeAverage_Technology", "employeeWorkAge_Technology",
          "employeeWorkSeniorityAverage_Technology", "employeeWorkSeniority_Technology", "employeePrize_Technology","work_Home",
          "employeejobTitle_Y_Technology","employeejobTitle_N_Technology"
        ],
        page: "Technology"
      }
      params = { ...params, ...this.propsObject }
      getHrData(params).then((res) => {
        if (res && res.code == 200) {
          this.$store.dispatch("commitChangeTechnologyData", res.data);
          
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    lookMoreHandler(type, params, title,moduleTitle,desc) {
      let scrollX = window.scrollX;
      let scrollY = window.scrollY;
      sessionStorage.setItem('savedPosition', JSON.stringify({ x: scrollX, y: scrollY }));
      sessionStorage.setItem('desc', JSON.stringify(desc));
      this.$router.push({ path: '/moreData', query: { type, params, title,moduleTitle,moreList:this.propsObject.moreList,consldEntpFlg:this.propsObject.consldEntpFlg  } })
    },
  },
}
</script>

<style lang="scss" scoped>
.rcBox {
  background: #f7f8f9;
  font-family: "微软雅黑";
  padding-bottom: 1.8rem;
  .tipBtn {
    width: 0.4rem;
    height: 0.4rem;
  }
  .bannerBox {
    position: relative;
    padding: 0.5rem 0.3rem 0;
    //职工总人数
    .banner {
      img {
        width: 100%;
        height: 3.4rem;
        border-radius: 6px;
        // height: 6.37rem;
      }
    }
    .worderNum {
      position: absolute;
      left: 0.86rem;
      top: 0.96rem;
      color: #fff;

      h4 {
        font-size: 0.4rem;
        position: relative;
        display: inline-block;
        img {
          position: absolute;
          right: -25px;
          top: -5px;
        }
      }
      h5 {
        font-size: 0.33rem;
      }
      h2 {
        font-size: 0.8rem;
        margin-top: 0.25rem;
        font-weight: 600;

        span {
          font-size: 0.32rem;
        }
      }
    }
  }
  //三大岗位板块：管理/科技/技能
  .container {
    padding: 0 0.3rem;
    .station {
      display: flex;
      margin-top: 0.3rem;
      .box {
        flex: 1;
        font-size: 0.32rem;
        text-align: center;
        background: #fff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 0.1rem;
        padding: 0.3rem 0;
        margin-left: 0.1rem;
        &:first-child {
          margin-left: 0;
        }
        div {
          margin-top: 0.23rem;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .checkBoxs {
      float: right;
      margin-top: 0.3rem;
      .text {
        font-size: 0.3rem;
        color: #456bc4;
      }
    }
    .dataDetails {
      margin-top: 0.32rem;
      margin-bottom: .2rem;
       box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.1rem;
      padding: .3rem;
      h4 {
        font-size: 0.32rem;
        color: #000;
        margin-bottom: 0.2rem;
        font-weight: 600;
      }
      dl {
        color: #333;
        font-size: 0.32rem;
        font-weight: 400;
        display: flex;
        dd {
          margin-right: 0.05rem;
          flex: 1;
          text-align: center;
          font-size: 0.32rem;
          line-height: .6rem;
          position:relative;
          &:last-child {
            margin-right: 0;
            &::after{
              display: none;
            }
          }
          &::after{
            content:'';
            height:.8rem;
            border-left: 1px dashed #CCCCCC;
            position: absolute;
            right: 0;
            top: 0.25rem;
          }
        }
      }
    }
  }
}
::v-deep i.van-icon.van-icon-success {
  border: 2px solid #456bc4 !important;
  border-radius: 4px;
}

.water-container {
  display: flex;
  justify-content: space-around;
  .water-item {
    padding: 0 0.1rem;
    text-align: center;
    border-right: 1px dashed #cccccc;
    h1 {
      font-size: 0.3rem;
      font-weight: 600;
      color: "#333333";
      line-height: 0.62rem;
    }
    h2 {
      font-size: 0.3rem;
      font-weight: 400;
      color: "#333333";
      line-height: 0.62rem;
    }
    h3 {
      font-size: 0.2rem;
      font-weight: 400;
      color: "#71737A";
      line-height: 0.58rem;
    }
  }
  .water-item:nth-last-of-type(1) {
    border-right: unset;
  }
}
</style>
