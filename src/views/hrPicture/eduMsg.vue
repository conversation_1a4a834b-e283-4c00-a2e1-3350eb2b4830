<template>
  <div class="eduMsgBox">
    <div class="container" style="padding-bottom:1.8rem">
      <!-- 职工学历分布情况 -->
      <div class="titleContainer">
      <public-title :title="'职工学历分布情况'" :text="['指按“全日制\\含非全日制最高学历”，统计对比职工学历分布情况。']"></public-title>
      <div class="doduleBox">
	    <div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeDegree_Educate_Content}}
        </div>
        <div class="port" style="padding-bottom:0.1rem;padding-top:0.05rem">
          <echart-bar-dx :id="'barXEdu'" :barItemColor="['#54b5ff','#ffc683']" :data="dataObj.employeeDegree_Educate" />
        </div>
      </div>
      </div>
      <!-- 全日制和含非全日制切换 -->
      <div class="changeBox">
        <van-sticky :offset-top="0" class="sh-tab1">
          <van-tabs title-inactive-color="#222" type="card" @click="onClick1">
            <van-tab title="全日制" :name="'全日制'"></van-tab>
            <van-tab title="含非全日制" :name="'含非全日制'"></van-tab>
          </van-tabs>
        </van-sticky>
      </div>
      <!-- 学历与岗位交叉分析 -->
      <div class="titleContainer">
      <public-title :title="'学历与岗位交叉分析'" :text="['指按“岗位类型”统计职工各岗位的分布情况；点击岗位，可查看每个岗位中“最高学历”（即：博士研究生、硕士研究生、大学本科、本科以下）的职工分布情况。']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeDegreePost_Educate_Content}}
        </div>
        <div class="port" style="padding-bottom:0.1rem">
          <!-- <echart-bar-sup :id="'eduSub1'" :barItemColor="['#5673EF','#37C5CB','#5398F7','#6DC6FA','#D4D4D4']" :data="dataObj.postEducate" /> -->
          <duiji-baredu :toChild="dataObj.employeeDegreePost_Educate" />
        </div>
      </div>
      </div>
      <!-- 学历与司龄交叉分析 -->
      <div class="titleContainer">
      <public-title :title="'学历与司龄交叉分析'" :text="['指按“本单位入职时间”统计职工各司龄的分布情况；点击司龄，可查看每个司龄中“最高学历”（即：博士研究生、硕士研究生、大学本科、本科以下）的职工分布情况。其中司龄划分如下：','•	10年以上（指＞10年）','•	6-10年（指＞6年 且 ≤10年）','•	3-6年（指＞3年 且 ≤6年）','•	1-3年（指＞1年 且 ≤3年）','•	1年及以下（指≤1年）']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeDegreeSeniority_Educate_Content}}
        </div>
        <div class="port" style="padding-bottom:0.1rem">
          <!-- <echart-bar-sup :id="'eduSub2'" :barItemColor="['#5673EF','#37C5CB','#5398F7','#6DC6FA','#D4D4D4']" :data="dataObj.seniorityEducate" /> -->
          <duiji-baredu :toChild="dataObj.employeeDegreeSeniority_Educate" />
        </div>
      </div>
      </div>
      <!-- 高学历职工企业分布情况 -->
      <div class="titleContainer">
        <public-title :title="'高学历职工企业分布情况'" :text="['指按“最高学历”，统计企业职工“硕士研究生”、“博士研究生”的分布情况。','其中[人数]表示：','1.博士研究生：指最高学历为“博士研究生”的职工数。','2.硕士研究生及以上：指最高学历为“硕士研究生”、“博士研究生”的职工数。','其中[占比]表示：','1.博士研究生：指最高学历为“博士研究生”的职工数占对应企业职工总人数的百分比。','2.硕士研究生及以上：指最高学历为“硕士研究生”、“博士研究生”的职工数占对应企业职工总人数的百分比。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeHightDegree_Educate_Content}}
          </div>
          <div class="xueLiSwitch">
            <van-tabs class="sh-tab2" type="card" @click="onClick2">
              <van-tab title="博士研究生" :name="'博士研究生'"></van-tab>
              <van-tab title="硕士研究生及以上" :name="'硕士研究生及以上'"></van-tab>
            </van-tabs>
          </div>
          <div class="port mt40 gxl_box">
            <echart-bar-y :id="'eduBarY1'" :barItemColor="'#5573ef'" :data="propsObject.ratio=='人数' ? dataObj.employeeHightDegree_Educate : dataObj.employeeHightDegreeRatio_Educate" />
            <div class="edu_right_fixed_box">
              <div class="xueLiSwitchY">
                <van-tabs class="sh-tab3" type="card" @click="onClick3">
                  <van-tab title="人数" :name="'人数'"></van-tab>
                  <van-tab title="占比" :name="'占比'"></van-tab>
                </van-tabs>
              </div>
              <div class="lookMore" v-if="dataObj.employeeHightDegree_Educate && dataObj.employeeHightDegree_Educate.length>10" :style="{top: '60%'}" @click="lookMoreHandler('eduData','employeeHightDegree_Educate','教育信息','高学历职工企业分布情况',dataObj.employeeHightDegree_Educate_Content)"><span>查看更多</span></div>
            </div>
          </div>
        </div>
      </div>
      <!-- 职工毕业院校情况 -->
      <div class="titleContainer">
        <public-title :title="'职工毕业院校情况'" :text="['指职工“最高学历”毕业院校的分布情况。']"></public-title>
        <div class="doduleBox">
	<div class="discribBox mt30"></div>
          <div class="discribBox">
            {{dataObj.employeeSchool_Educate_Content}}
          </div>
          <div class="port" style="padding-top:0.08rem">
            <echart-bar-y :id="'eduBarY2'" :barItemColor="'#20C3BE'" :data="dataObj.employeeSchool_Educate" />
          </div>
        </div>
      </div>
      <!-- 职工专业背景情况 -->
      <div class="titleContainer">
      <public-title :title="'职工专业背景情况'" :text="['指职工“最高学历”毕业院校所学专业的分布情况。']"></public-title>
      <div class="doduleBox">
	<div class="discribBox mt30"></div>
        <div class="discribBox">
          {{dataObj.employeeSpecialized_Educate_Content}}
        </div>
        <div class="port" style="padding-top:0.06rem">
          <echart-bar-y :id="'eduBarY3'" :barItemColor="'#FFC683'" :data="dataObj.employeeSpecialized_Educate" />
        </div>
      </div>
    </div>
    </div>
    <!-- 筛选 -->
    <global-filter @submit="submitFilter" @reset="resetFilter"></global-filter>
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue'
import echartBarDx from "@/components/echartBarDX";
import echartBarSup from "@/components/echartBarSup";
import echartBarY from "@/components/echartBarY";
import globalFilter from '@/components/globalFilter.vue';
import duijiBaredu from '@/components/duijiBaredu.vue'
import { getHrData } from "@/http/api";
export default {
  name: 'eduMsg',
  components: {
    publicTitle,
    echartBarDx,
    echartBarSup,
    echartBarY,
    globalFilter,
    duijiBaredu
  },
  data() {
    return {
      propsObject: {
        "stdyForm": "全日制", // 0 1是否为全日制
        "ratio": "人数", // 类型（0：人数；1：占比）
        "educate": "博士研究生", // 学历（0：博士研究生；1：硕士及以上）
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      },
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  mounted() {
    this.initData()
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
    if(this.ownOrgName!='全集团'){
      $(".discribBox").hide()
    }
  },
  computed: {
    dataObj() {
      return this.$store.state.eduData
    },
  },
  methods: {
    // 重置查询
    resetFilter() {
      this.propsObject = {
        "stdyForm": "全日制", // 0 1是否为全日制
        "ratio": "人数", // 类型（0：人数；1：占比）
        "educate": "博士研究生", // 学历（0：博士研究生；1：硕士及以上）
        "moreChooseType": sessionStorage.getItem("ownOrgName")!="全集团" ?"2":"0",// 0:全集团；1：按业务板块；2：按二级单位
        "moreList": sessionStorage.getItem("ownOrgName")!="全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        "consldEntpFlg":"",//并表，默认传空即传全集团
      }
      this.initData();
      //从sessionStorage中获取是二级单位还是全集团
      this.ownOrgName = sessionStorage.getItem("ownOrgName")
      //判断，如果是二级单位进入的页面，那么所有的说明都隐藏
      if (this.ownOrgName != '全集团') {
        $(".discribBox").hide()
      }
    },
    //从筛选子组件传过来的值
    submitFilter(val) {
      // 获取数据
      this.propsObject['moreChooseType'] = val['moreChooseType'];
      this.propsObject['moreList'] = val['moreList'];
      this.propsObject['consldEntpFlg'] = val['consldEntpFlg'];
      this.initData();
      window.scrollTo(0,0);
    },
    //初始化数据
    initData() {
      let params = {
        mapperInterface: [
          "employeeDegree_Educate",
          "employeeDegreePost_Educate",
          "employeeDegreeSeniority_Educate",
          "employeeHightDegree_Educate",
          "employeeHightDegreeRatio_Educate",
          "employeeSchool_Educate",
          "employeeSpecialized_Educate"],
        page: "Educate"
      }
      params = { ...params, ...this.propsObject }
      getHrData(params).then((res) => {
        if (res && res.code == 200) {
          //在vuex中存起来获取到的所有的值
          this.$store.dispatch("commitChangeEduData", res.data);
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    lookMoreHandler(type, params, title, moduleTitle,desc) {
      let scrollX = window.scrollX;
      let scrollY = window.scrollY;
      sessionStorage.setItem('savedPosition', JSON.stringify({ x: scrollX, y: scrollY }));
      sessionStorage.setItem('propsObject', JSON.stringify(this.propsObject));
      sessionStorage.setItem('desc', JSON.stringify(desc));
      this.$router.push({ path: '/moreData', query: { type, params, title, moduleTitle,moreList:this.propsObject.moreList,consldEntpFlg:this.propsObject.consldEntpFlg } })
    },
    onClick1(name, title) {
      this.propsObject['stdyForm'] = name;
      this.initData();
    },
    onClick2(name, title) {
      this.propsObject['educate'] = name;
      this.initData();
    },
    onClick3(name, title) {
      this.propsObject.ratio = name
    },
  }
}
</script>

<style lang="scss" scoped>
.eduMsgBox {
  padding: 0 0.3rem 1.8rem;
  background: #f7f8f9;
  height: 100vh;
  // overflow-y: auto;
  font-family: "微软雅黑";
  .changeBox {
    margin: 0.42rem 0;
    .sh-tab1 {
      ::v-deep .van-sticky--fixed {
        z-index: 199;
      }
      ::v-deep .van-tabs__wrap {
        height: 0.72rem;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        width: 4.2rem;
        background-color: rgba(0, 0, 0, 0) !important;
        border-radius: 0.06rem;
        font-weight: 400;
      }
      ::v-deep .van-tabs__nav {
        border: unset;
        height: 0.72rem;
        background-color: rgba(0, 0, 0, 0) !important;
        margin: 0;
        font-weight: 400;
        .van-tab {
          font-size: 0.3rem;
          flex: unset;
          height: 0.72rem;
          width: 2.1rem;
          color: #222;
          background-color: #fff;

          border-radius: 0.06rem 0px 0px 0.06rem;
          // border: 1px solid #999999;
          padding: 8px 10px;
          font-size: 0.32rem;
          box-sizing: border-box;
          font-weight: 400;
        }
        .van-tab:nth-last-of-type(1) {
          margin-left: -1px;
          border-radius: 0px 0.06rem 0.06rem 0px;
        }
        .van-tab--active {
          color: #fff;
          border: 1px solid #3e7bfa;
          background-color: #3e7bfa;
          font-weight: 400;
          position: relative;
          z-index: 9;
        }
      }
    }
  }
  .xueLiSwitch {
    margin-top: 0.4rem;
    .sh-tab2 {
      ::v-deep .van-tabs__wrap {
        height: 0.7rem;
      }
      ::v-deep .van-tabs__nav {
        border: unset;
        height: 0.7rem;
        margin: 0;
        font-weight: 400;
        .van-tab {
          font-size: 0.32rem;
          flex: unset;
          height: 0.7rem;
          color: #999999;
          background: #ffffff;
          box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
          border-radius: 0.1rem 0px 0px 0.1rem;
          border: 1px solid #999999;
          padding: 8px 10px;
          box-sizing: border-box;
        }
        .van-tab:nth-last-of-type(1) {
          margin-left: -1px;
          border-radius: 0px 0.1rem 0.1rem 0px;
        }
        .van-tab--active {
          color: #3e7bfa;
          border: 1px solid #3e7bfa;
          font-weight: 400;
          position: relative;
          z-index: 9;
        }
      }
    }
  }

  .gxl_box {
    position :relative;
    margin-top: 0.28rem!important;
  }

  .edu_right_fixed_box {
    position: absolute;
    right: 0;
    top: 0;
    transform: translateX(50%);
    z-index: 3;
    .lookMore {
      position: unset;
      margin-top: 1rem;
    }
  }
  .xueLiSwitchY {
    position: absolute;
    right: -5px;
    top: 3.9rem;
    z-index: 3;
    .sh-tab3 {
      ::v-deep .van-tabs__wrap {
        height: auto;
        .van-tabs__nav {
          font-weight: 400;
          border: unset;
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          height: 3rem;
          word-wrap: break-word;
          margin: unset;
          .van-tab {
            font-weight: 400;
            font-size: 0.3rem;
            color: #999999;
            background: #ffffff;
            box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
            border-radius: 0.1rem 0.1rem 0px 0px;
            border: 1px solid #999999;
            box-sizing: border-box;
            .van-tab__text {
              display: flex;
              width: 20px;
              height: 100%;
              line-height: 2;
              word-break: break-all;
              justify-content: center;
              align-items: center;
              padding: 0 15px;
              box-sizing: border-box;
              font-weight: 400;
            }
          }
          .van-tab:nth-last-of-type(1) {
            margin-top: -1px;
            border-radius: 0px 0px 0.1rem 0.1rem;
          }
          .van-tab--active {
            color: #3e7bfa;
            border: 1px solid #3e7bfa;
            font-weight: 400;
            position: relative;
            z-index: 9;
          }
        }
      }
    }
  }
  ::v-deep .van-sticky--fixed {
    width: 100% !important;
    z-index: 999;
    .van-tabs__wrap {
      width: 100% !important;
      border-radius: 0 !important;
    }
    .van-tab {
      width: 50% !important;
      border-radius: 0 !important;
    }
    .van-tab:nth-last-of-type(1) {
      border-radius: 0 !important;
    }
  }
}
</style>
