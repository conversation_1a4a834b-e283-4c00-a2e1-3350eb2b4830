<template>
  <div class="homeNav">
    <!-- <div class="banner">
      <img src="../../assets/img/newHome1205/banner1.png" @click="jumpZbBoard" alt="">
    </div> -->
    <div class="banner">
      <div class="bannerTB" v-if="bannerDataHome" @click="jumpZbBoard">
        <div class="leftBox oneLeftBox">
          <p>营业收入 
            <strong v-if="bannerDataHome.yysr_dq && bannerDataHome.yysr_dq.indexOf('.0')!=-1">{{bannerDataHome.yysr_dq && (bannerDataHome.yysr_dq*1).toLocaleString()}}.0</strong>
            <strong v-else>{{bannerDataHome.yysr_dq && (bannerDataHome.yysr_dq*1).toLocaleString()}}</strong>
            <span>亿元</span></p>
          <div class="tbBox">
            <div class="text">
              <p>同比</p>
              <p class="p2">
                <strong><b v-if="(bannerDataHome.yysr_tb)*1>0">+</b>{{bannerDataHome.yysr_tb && bannerDataHome.yysr_tb.replace(/\s/g,'')}}</strong><span>%</span>
              </p>
            </div>
            <div class="port" style="padding-left:.2rem">
              <echarts-pieh :toChild="employeeDegree_Home" :bannerValue="bannerDataHome.yysr_dcl" />
            </div>
          </div>
        </div>
        <div class="leftBox">
          <p>利润总额 
            <strong v-if="bannerDataHome.lr_dq && bannerDataHome.lr_dq.indexOf('.0')!=-1">{{bannerDataHome.lr_dq && (bannerDataHome.lr_dq*1).toLocaleString()}}.0</strong>
            <strong v-else>{{bannerDataHome.lr_dq && (bannerDataHome.lr_dq*1).toLocaleString()}}</strong>
            <span>亿元</span></p>
          <div class="tbBox">
            <div class="text">
              <p>同比</p>
              <p class="p2"><strong><b v-if="(bannerDataHome.lr_tb*1)>0">+</b>{{bannerDataHome.lr_tb && bannerDataHome.lr_tb.replace(/\s/g,'')}}</strong><span>%</span></p>
            </div>
            <div class="port" style="padding-left:0.05rem">
              <echarts-pieh  :toChild="employeeDegree_Home"  :bannerValue="bannerDataHome.lr_dcl"/>
            </div>
          </div>
        </div>
      </div>
      <p class="tip">* 数据截至<span v-if="bannerDataHomeStr">{{bannerDataHomeStr}}</span>月底</p>
    </div>

    <div class="container">
      <div class="modulesBox" v-for="(item ,index) in navList " :key="index" @click="toPage(item.path)">
        <div class="itemBox" :style="{background: `url(${item.tpUrl}) 100% no-repeat`,backgroundSize: 'cover'}">
          <div class="itemBox_tp">
            <img class="itemBox_icon" :src="item.iconUrl" />
            <span>{{item.title}}</span>
          </div>
          <div class="itemBox_bt">{{item.text}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import { getPermission, getUserId, getKbData } from "../../http/api";
import env from "../../http/env";
import echartsPieh from '@/components/echartsPieh.vue'
export default {
  name: 'home',
  components: { echartsPieh },
  data() {
    return {
      navList: [],
      menus: [
        {
          id: 'jj',
          title: '经济运行',
          path: '/economyReport',
          // path: '/jjyxReport',
          text: '展示集团主要指标及重点业务板块情况',
          tpUrl: require('../../assets/img/newHome1205/tp_jj.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_jj.png'),
        },
        {
          id: 'lj',
          title: '两金监测',
          path: '/tgMonitor',
          // path: '/liangjin',
          text: '监测集团应收账款及存货动态',
          tpUrl: require('../../assets/img/newHome1205/tp_lj.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_lj.png')
        },
        {
          id: 'hr',
          title: '人力资源',
          path: '/hrIndex',
          text: '展示集团人力资源全貌及专题分析',
          tpUrl: require('../../assets/img/newHome1205/tp_hr.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_hr.png')
        },
        {
          id: 'zc',
          title: '资产总览',
          path: '/assetsMap',
          text: '展示集团产权、固定资产底数和地理分布',
          tpUrl: require('../../assets/img/newHome1205/tp_zc.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_zc.png'),
        },
        {
          id: 'siku',
          title: '司库管理',
          path: '/siku',
          text: '实时监控企业资金流动情况及风险',
          tpUrl: require('../../assets/img/newHome1205/tp_siku.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_siku.png')
        },
        {
          id: 'jqqd',
          title: '敬请期待',
          path: '',
          text: '更多主题建设中…',
          tpUrl: require('../../assets/img/newHome1205/tp_jqqd.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_jqqd.png'),
        }
      ],
      employeeDegree_Home: [
        {
          "name": "博士研究生",
          "value": "100"
        },
        {
          "name": "本科研究生",
          "value": "50"
        },
        {
          "name": "硕士",
          "value": "50"
        },
      ],
      showIndi: false,
      propsObject: {

        "dt": "2023-09",
        "moreChooseType": "0",	// 0:全集团；1：指标排名；2：二级企业
        "ENTP_NO": "",	// 二级企业名称
        "tgt_no": "",	// 指标码值
        "dsp_typ_cd": ""	// 1完成值，2达成率

      },
      bannerDataHome:"",
      bannerDataHomeStr:""
    }
  },
  created() {
    // let userId = localStorage.getItem("cec_hr_lx_userId");
    // let _this =this;
    // console.log("userId1", userId)
    // if (userId === undefined || userId === "undefined" || userId === null || userId === "null" || userId.includes('object')) {
    //   lx.biz.getAuthCode({
    //     appId: env.appId,
    //     success: function (res) {
    //       console.log("res", res)
    //       console.log("authCode", res.authCode)
    //       getUserId(res.authCode).then(res1 => {
    //         console.log("getUserId", res1)
    //         userId = res1.data
    //         if (userId !== {}) {            
    //           localStorage.setItem("cec_hr_lx_userId", userId)
    //         }
    //         console.log("userId1111", JSON.stringify(userId))
    //         _this.getPermission(userId);
    //       }).catch(err => {
    //         console.log("err", err)
    //       })
    //     },
    //     fail: function (err) {
    //       console.log("failerr", err)
    //     },
    //   });
    // } else {
    //   console.log("userId2222", userId)
    //   this.getPermission(userId);
    // }

    this.getPermission("18910015136")
    // // this.getPermission("18211013898")
    // // // this.getPermission("18510516069")
  },
  mounted() {
    this.initData()
  },
  methods: {
    //获取Banner数据
    //初始化数据
    initData() {
      let params = {
        mapperInterface: [
          "home", "homeStr"]
      }
      params = { ...params, ...this.propsObject }
      getKbData(params).then((res) => {
        if (res && res.code == 200) {
          this.bannerDataHome = res.data.home
          this.bannerDataHomeStr=res.data.homeStr
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    //跳转至对应页面
    toPage(val) {
      if (!val) return false;
      this.$router.push({ path: val })
    },
    //跳转到指标看板
    jumpZbBoard() {
      if (!this.showIndi) return false;
      this.$router.push({ path: "/zbBoard" })
      // this.$router.push({path: "/cecBoard"})
    },
    getPermission(userId) {
      getPermission(userId).then(res1 => {
        console.log("getPermission", res1)
        let data = res1.data;
        let permissions = data.permissions;
        this.showIndi = permissions.includes('indi');
        if ((permissions.length - this.showIndi) % 2 != 0) permissions.push('jqqd')
        let ownOrgName = data.ownOrgName;
        this.menus.forEach(item => {
          if (permissions.includes(item.id)) this.navList.push(item);
        });
        sessionStorage.setItem("ownOrgName", ownOrgName)
        if (data.hasOwnProperty("orgList")) {
          let orgList = data.orgList;
          sessionStorage.setItem("orgList", orgList)
        }
      }).catch(err => {
        console.log("err", err)
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.homeNav {
  font-family: "微软雅黑";
  background: #f6f7f8;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .banner {
    // padding-top: 0.3rem;
    // background: #1b7bea;
    width: 100%;
    padding: 0.4rem 0.3rem 0.1rem;
    box-sizing: border-box;
    position: relative;
    p.tip {
      position: absolute;
      bottom: 0.4rem;
      left: 0.7rem;
      color: #fff;
      font-size: 0.2rem;
      font-weight: 400;
    }
    .bannerTB {
      background: url("../../assets/img/newHome1205/homeBgImg.png") no-repeat;
      background-size: contain;
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #fff;
      width: 6.9rem;
      height: 3.2rem;
      padding: 0.35rem 0.35rem 0 0.35rem;
      box-shadow: 2px 6px 40px 0px rgba(74, 103, 157, 0.45);
      .leftBox.oneLeftBox {
        margin-right: 0.1rem;
      }
      .leftBox {
        font-size: 0.3rem;
        strong {
          font-weight: 600;
          font-size: 0.35rem;
          // padding-left: 0.05rem;
        }
        span {
          font-size: 0.2rem;
          // padding-left: 0.1rem;
        }
        b{
          font-weight: normal;
        }
      }
      .tbBox {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        position: relative;
    top: -0.1rem;
        .text {
          font-size: 0.2rem;
          p {
            strong {
              font-weight: 550;
              font-size: 0.25rem;
              // padding-left: 0.05rem;
            }
          }
          .p2 {
            padding-top: 0.28rem;
          }
        }
      }
    }
    img {
      width: 100%;
      height: 100%;
      box-shadow: 2px 6px 0.45rem 0px rgba(74, 103, 157, 0.45);
    }
  }
  .container {
    flex: 1;
    overflow: auto;
    .modulesBox {
      display: inline-block;
      width: 50%;
      height: 3.2rem;
      .itemBox {
        width: 100%;
        height: 100%;
        padding: 0 0.3rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background: linear-gradient(180deg, #2595e8 0%, #ffffff 100%);
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 8px;
        .itemBox_tp {
          font-size: 0.3rem;
          // height: 1.4rem;
          // line-height: 1.4rem;
          margin-top: 0.5rem;
          .itemBox_icon {
            width: 0.33rem;
            height: 0.33rem;
            vertical-align: middle;
          }
          span {
            margin-left: 0.05rem;
            font-size: 0.3rem;
            font-weight: 700;
            vertical-align: middle;
          }
        }
        .itemBox_bt {
          flex: 1;
          color: #666666;
          font-size: 0.275rem;
          padding: 0.5rem 0.175rem 0;
          font-weight: 400;
          box-sizing: border-box;
        }
      }
    }
    .modulesBox:nth-of-type(odd) {
      padding: 0.3rem 0.15rem 0 0.3rem;
    }
    .modulesBox:nth-of-type(even) {
      padding: 0.3rem 0.3rem 0 0.15rem;
    }
    .modulesBox:nth-last-of-type(1),
    .modulesBox:nth-last-of-type(2) {
      padding-bottom: 0.3rem;
    }
  }
}
</style>
