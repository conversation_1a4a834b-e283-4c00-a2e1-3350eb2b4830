<template>
  <div class="homeNav">
    <div class="banner">
      <img src="../../assets/img/newHome1205/banner.png" @click="jumpZbBoard" alt="">
    </div>
    <div class="container">
      <div class="modulesBox" v-for="(item ,index) in navList " :key="index" @click="toPage(item.path)">
        <div class="itemBox"  :style="{background: `url(${item.tpUrl}) 100% no-repeat`,backgroundSize: 'cover'}">
          <div class="itemBox_tp">
            <img class="itemBox_icon" :src="item.iconUrl" />
            <span>{{item.title}}</span>
          </div>
          <div class="itemBox_bt">{{item.text}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {getPermission, getUserId} from "../../http/api";
import env from "../../http/env";

export default {
  name: 'home',
  data() {
    return {
      navList: [],//
      menus: [
        {
          id: 'jj',
          title: '经济运行',
          path: '/jjyxReport',
          text: '展示集团主要指标及重点业务板块情况',
          tpUrl: require('../../assets/img/newHome1205/tp_jj.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_jj.png'),
        },
        {
          id: 'lj',
          title: '两金监测',
          path: '/liangjin',
          text: '监测集团应收账款及存货动态',
          tpUrl: require('../../assets/img/newHome1205/tp_lj.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_lj.png')
        },
        {
          id: 'hr',
          title: '人力资源',
          path: '/hrIndex',
          text: '展示集团人力资源全貌及专题分析',
          tpUrl: require('../../assets/img/newHome1205/tp_hr.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_hr.png')
        },
        {
          id: 'zc',
          title: '资产总览',
          path: '/assetsMap',
          text: '展示集团产权、固定资产底数和地理分布',
          tpUrl: require('../../assets/img/newHome1205/tp_zc.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_zc.png'),
        },
        {
          id: 'siku',
          title: '司库管理',
          path: '/siku',
          text: '实时监控企业资金流动情况及风险',
          tpUrl: require('../../assets/img/newHome1205/tp_siku.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_siku.png')
        },
        {
          id: 'jqqd',
          title: '敬请期待',
          path: '',
          text: '更多主题建设中…',
          tpUrl: require('../../assets/img/newHome1205/tp_jqqd.png'),
          iconUrl: require('../../assets/img/newHome1205/icon_jqqd.png'),
        }
      ],
      showIndi: false
    }
  },
  created() {
    lx.ui.setNavigationBarBgColor({
      color: "#1b7bea",
    });
    // let userId = localStorage.getItem("cec_hr_lx_userId");
    // if (userId === undefined || userId === "undefined" || userId === null || userId === "null" || userId.includes('object')) {
    //   lx.biz.getAuthCode({
    //     appId: env.appId,
    //     success: function (res) {
    //       console.log("res", res)
    //       // console.log("authCode", res.authCode)
    //       getUserId(res.authCode).then(res1 => {
    //         console.log("getUserId", res1)
    //         userId = res1.data
    //         if (userId !== {}) {
    //           localStorage.setItem("cec_hr_lx_userId", userId)
    //         }
    //         console.log("userId1111", JSON.stringify(userId))
    //         this.getPermission(userId);
    //       }).catch(err => {
    //         console.log("err", err)
    //       })
    //     },
    //     fail: function (err) {
    //       console.log("err", err)
    //     },
    //   });
    // } else {
    //   console.log("userId2222", userId)
    //   this.getPermission(userId);
    // }

    // this.getPermission("18910015136")
    this.getPermission("18211013898")
    // // this.getPermission("18510516069")
  },
  methods: {
    toPage(val) {
      if(!val) return false;
      this.$router.push({path: val})
    },
    //跳转到指标看板
    jumpZbBoard() {
      if(!this.showIndi) return false;
      this.$router.push({path: "/zbBoard"})
    },
    getPermission(userId) {
      getPermission(userId).then(res1 => {
        console.log("getPermission", res1)
        let data = res1.data;
        let permissions = data.permissions;
        this.showIndi = permissions.includes('indi');
        if( (permissions.length - this.showIndi) % 2 != 0)  permissions.push('jqqd')
        let ownOrgName = data.ownOrgName;
        this.menus.forEach(item => {
          if(permissions.includes(item.id)) this.navList.push(item);
        });
        sessionStorage.setItem("ownOrgName", ownOrgName)
        if (data.hasOwnProperty("orgList")) {
          let orgList = data.orgList;
          sessionStorage.setItem("orgList", orgList)
        }
      }).catch(err => {
        console.log("err", err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.homeNav {
  font-family: "微软雅黑";
  background: #fff;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .banner {
    // padding-top: 0.3rem;
    // background: #1b7bea;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .container {
    flex: 1;
    overflow: auto;
    background: #fff;
    .modulesBox {
      display: inline-block;
      width: 50%;
      height: 3.2rem;
      .itemBox {
        width: 100%;
        height: 100%;
        padding: 0 0.3rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background: linear-gradient(180deg, #2595E8 0%, #FFFFFF 100%);
        box-shadow: 5px 6px 20px 0px rgba(74,103,157,0.1);
        border-radius: 8px;
        .itemBox_tp {
          font-size: 0.3rem;
          height: 1.4rem;
          line-height: 1.4rem;
          .itemBox_icon {
            width: 0.33rem;
            height: 0.33rem;
            vertical-align: middle;
          }
          span {
            margin-left: 0.05rem;
            font-size: 0.3rem;
            font-weight: 700;
            vertical-align: middle;
          }
        }
        .itemBox_bt {
          color: #666666;
          font-size: 0.275rem;
          padding: 0 0.175rem;
          font-weight: 400;
          box-sizing: border-box;
        }
      }
    }
    .modulesBox:nth-of-type(odd) {
      padding: 0.3rem 0.15rem 0 0.3rem;
    }
    .modulesBox:nth-of-type(even) {
      padding: 0.3rem 0.3rem 0 0.15rem;
    }
    .modulesBox:nth-last-of-type(1),.modulesBox:nth-last-of-type(2) {
      padding-bottom: 0.3rem;
    }
  }
}
</style>
