<template>
  <div class="homeNav">
    <!-- 顶部下拉选择器 -->
    <div class="top-selector" v-if="pickerColumns.length > 1">
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span class="selected-text">{{ selectedOption || '请选择公司' }}</span>
          <i class="dropdown-icon" :class="{ 'is-active': showDropdown }"></i>
        </div>
        <transition name="dropdown-fade">
          <div v-if="showDropdown" class="dropdown-menu">
            <div class="dropdown-menu-inner">
              <div 
                v-for="item in pickerColumns" 
                :key="item.id" 
                class="dropdown-item"
                :class="{ 'is-active': selectedCompany && selectedCompany.id === item.id }"
                @click="selectCompany(item)"
              >
                {{ item.text }}
              </div>
            </div>
          </div>
        </transition>
      </div>
      <!-- 点击外部区域关闭下拉框 -->
      <div v-if="showDropdown" class="dropdown-backdrop" @click="showDropdown = false"></div>
    </div>

    <div class="banner-container">
      <template v-if="isGroupSelected">
        <van-swipe
          ref="swiper"
          :initial-swipe="swiperIndex"
          @change="swiperChange"
          class="swiper"
          :show-indicators="false"
        >
          <van-swipe-item v-for="item in bannerList" :key="item.id">
            <div
              class="banner"
              :class="item.class"
              @click="item.click(item)"
            >
              <span v-if="item.name == 'rank'">{{ rankDT }}</span>
              <span v-if="item.name == 'monitor'">{{ monitorDT }}</span>
            </div>
          </van-swipe-item>
        </van-swipe>
        <div class="pagination">
          <div v-for="item in bannerList" :key="item.id" class="item" @click="handleChangeSwiper(item)">
            <div v-if="swiperIndex == item.id" class="item-icon" :class="item.pageClass">
              <img :src="item.icon" alt="">
            </div>
            <div class="item-btn" :class="item.btnClass" />
          </div>
        </div>
      </template>
      <template v-else>
        <div class="banner-second-container">
          <img src="@/assets/img/home/<USER>" class="banner-second-rank-img" @click="routeToZbBoardSecond" />
          <div class="banner-second-time">{{ secondRankTime }}</div>
        </div>
      </template>
    </div>

    <div class="container">
      <div v-if="navList.length == 0" class="no-data-box">
        <img src="../../assets/img/home_no_data.png" />
      </div>
      <div
        v-if="navList.length > 0"
        v-for="(item, index) in navList"
        :key="index"
        class="box"
        @click="toPage(item)"
        :style="{ backgroundImage: item.bg }"
      >
        <div class="title">
          <img class="icon" :src="item.iconUrl" />
          <span>{{ item.title }}</span>
        </div>
        <div class="text">{{ item.text }}</div>
      </div>
      
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
import { getUserId, getUserInfo } from "@/utils";
import { getLatestMonth, getIndexLatestMonth, getAllCompanyInfoList, getCompanyInfo, getModuleInfo } from "@/http/api";
import { setState, getState } from "@/utils/cache";
import { getPermission, getKbData, getSecKbData } from "../../http/api";
import env from "../../http/env";
import echartsPieh from "@/components/echartsPieh.vue";

import IconRank from '../../assets/img/home/<USER>'
import IconMonitor from '../../assets/img/home/<USER>'

export default {
  name: "home",
  components: { echartsPieh },
  data() {
    return {
      swiperIndex: 0,
      userId: '',
      navList: [],
      rankDT: "",
      monitorDT: "",
      companyInfoList: getState("compnay_info_list") || null,
      // 下拉选择器相关数据
      selectedOption: "",
      showDropdown: false,
      pickerColumns: [],
      selectedCompany: null,
      secondRankTime: "",
      menus: [
        // {
        //   id: 'jj',
        //   title: '经济运行',
        //   path: '/economyReport',
        //   // path: '/jjyxReport',
        //   text: '展示集团主要指标及重点业务板块情况',
        //   tpUrl: require('../../assets/img/newHome1205/tp_jj.png'),
        //   iconUrl: require('../../assets/img/newHome1205/icon_jj.png'),
        // },
        {
          id: "lj",
          title: "两金监测",
          path: "/tgMonitor",
          // path: '/liangjin',
          text: "监测集团应收账款及存货动态",
          // tpUrl: require('../../assets/img/newHome1205/tp_lj.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_lj.png"),
          bg: "linear-gradient(180deg, #c5ecea 0%, #FFFFFF 50%)",
        },
        {
          id: "hr",
          title: "人力资源",
          path: "/hrIndex",
          text: "展示集团人力资源全貌及专题分析",
          // tpUrl: require('../../assets/img/newHome1205/tp_hr.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_hr.png"),
          bg: "linear-gradient(180deg, #c5e3f8 0%, #FFFFFF 50%)",
        },
        {
          id: "zc",
          title: "资产总览",
          path: "/assetsMap",
          text: "展示集团产权、固资底数和地理分布",
          // tpUrl: require('../../assets/img/newHome1205/tp_zc.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_zc.png"),
          bg: "linear-gradient(180deg, #fdf0e9 0%, #FFFFFF 50%)",
        },
        {
          id: "siku",
          title: "司库管理",
          path: "/siku",
          text: "实时监控企业资金流动情况及风险",
          // tpUrl: require('../../assets/img/newHome1205/tp_siku.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_siku.png"),
          bg: "linear-gradient(180deg, #feefef 0%, #FFFFFF 50%)",
        },
        {
          id: "ckbg",
          title: "查看报告",
          path: "/viewReport",
          text: "查看集团各类业务专题报告",
          // tpUrl: require('../../assets/img/newHome1205/tp_siku.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_report.png"),
          bg: "linear-gradient(180deg, #cae1f5 0%, #FFFFFF 50%)",
        },
        {
          id: "jqqd",
          title: "敬请期待",
          path: "",
          text: "更多主题建设中…",
          // tpUrl: require('../../assets/img/newHome1205/tp_jqqd.png'),
          iconUrl: require("../../assets/img/newHome1205/icon_jqqd.png"),
          bg: "linear-gradient(180deg, #d2d6f0 0%, #FFFFFF 50%)",
        },
      ],
      employeeDegree_Home: [
        {
          name: "博士研究生",
          value: "100",
        },
        {
          name: "本科研究生",
          value: "50",
        },
        {
          name: "硕士",
          value: "50",
        },
      ],
      isShowIndi: false,
      isShowMonitor: false,
      isShowPhb: false,
      propsObject: {
        dt: "2023-09",
        moreChooseType: "0", // 0:全集团；1：指标排名；2：二级企业
        ENTP_NO: "", // 二级企业名称
        tgt_no: "", // 指标码值
        dsp_typ_cd: "", // 1完成值，2达成率
      },
      bannerDataHome: "",
      bannerDataHomeStr: "",
      bannerList: [
        {
          id: 0,
          name: 'rank',
          class: 'banner-rank',
          click: this.routeToRanking,
          disabled: true,
          pageClass: 'item-icon-rank',
          icon: IconRank,
          btnClass: 'item-btn-rank',
          btnClassDisabled: 'item-btn-rank-disabled',
        },
        {
          id: 1,
          name: 'monitor',
          class: 'banner-monitor',
          click: this.routeToMonitor,
          disabled: true,
          pageClass: 'item-icon-monitor',
          icon: IconMonitor,
          btnClass: 'item-btn-monitor',
          btnClassDisabled: 'item-btn-monitor-disabled',
        },
      ],
    };
  },
  computed: {
    isGroupSelected() {
      return this.selectedCompany && this.selectedCompany.id === '99';
    },
    reportViewEnabled() {
      let self = [];
      this.companyInfoList.forEach((item) => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push(item);
        }
      });

      // 只有全集团运行查看月报
      if (self.some(item => item.cpyTag == "01")) return true;

      return false;
    },
  },
  watch: {
    // 监听选中的公司变化
    selectedCompany: {
      handler(newCompany, oldCompany) {
        if (newCompany && newCompany.id) {
          // 如果是初始化（没有oldCompany）或者公司ID发生变化
          if (!oldCompany || newCompany.id !== oldCompany.id) {
            console.log('公司选择发生变化:', (oldCompany && oldCompany.id), '->', newCompany.id);
            this.getPermissionByCompany(this.userId, newCompany.id);
            this.initData();
            this.initDate();
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    // 切换下拉框显示状态
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    
    // 选择公司
    selectCompany(item) {
      this.selectedOption = item.text;
      this.selectedCompany = item;
      this.showDropdown = false;
      
      // 将企业ID保存到缓存
      if (item.id) {
        setState('selectedCompanyId', item.id);
        console.log('已保存企业ID到缓存:', item.id);
      }
      
      // 触发公司变更后的业务逻辑
      if (this.selectedCompany && this.selectedCompany.id) {
        this.getPermissionByCompany(this.userId, this.selectedCompany.id);
        this.initData();
        this.initDate();
      }
    },
    
    // 获取公司选择列表
    async getCompanySelectList() {
      const res = await getCompanyInfo(this.userId);
      
      if (res && res.code === 200 && res.data && res.data.companyInfo) {
        // 直接使用公司信息列表，不添加"全部"选项
        this.pickerColumns = res.data.companyInfo.map(item => ({
          text: item.companyName,
          value: item.companyName,
          id: item.id
        }));
        
        // 默认选择第一个公司
        if (this.pickerColumns.length > 0) {
          this.selectedOption = this.pickerColumns[0].text;
          this.selectedCompany = this.pickerColumns[0];
          setState('selectedCompanyId', this.pickerColumns[0].id);
        }
      }
    },
    
    // 统一的权限处理函数
    async getPermissionByCompany(userId, companyId) {
      // 根据公司ID决定使用哪种权限逻辑
      if (companyId === '99') {
        // CEC集团总部，使用原来的 getPermission 逻辑
        this.getPermission(userId);
      } else {
        // 其他企业，使用模块权限逻辑
        await this.getModulePermission(companyId);
      }
    },
    
    // 获取模块权限信息
    async getModulePermission(companyId) {
      try {
        const res = await getModuleInfo(this.userId, companyId);
        
        if (res && res.code === 200 && res.data && res.data.moduleInfo) {
          const navList = []
          this.$store.dispatch('updateModuleInfo', res.data.moduleInfo);
          
          // 根据模块信息构建权限列表
          const modulePermissions = res.data.moduleInfo.map(module => module.id);
          
          // 设置权限标志
          this.isShowPhb = modulePermissions.includes("phb");
          this.filterBannerList();
          
          // 根据模块权限显示对应的菜单项
          this.menus.forEach((item) => {
            if (modulePermissions.includes(item.id)) {
              navList.push({
                ...item,
                enabled: true
              });
            }
          });
          
          // 如果菜单项数量为奇数，添加"敬请期待"
          if (navList.length % 2 != 0) {
            const comingSoon = this.menus.find(item => item.id == 'jqqd');
            if (comingSoon) {
              navList.push(comingSoon);
            }
          }

          this.navList = navList
        }
      } catch (error) {
        console.error('获取模块权限失败:', error);
      }
    },
    
    async init() {
      getUserInfo();
      const userId = await getUserId()
      this.userId = userId;
      console.log('初始化获取到的userId:', userId);
      this.loadCompanyInfoList();
      this.getCompanySelectList(); // 获取公司选择列表
    },
    // 修改 initDate 方法
    async initDate() {
      if (this.selectedCompany && this.selectedCompany.id === '99') {
        // 99企业逻辑：获取当前最新年月
        getLatestMonth().then((res) => {
          if (res.data.kanbanym) {
            this.rankDT = res.data.kanbanym[0].year_month;
          }
        });

        getIndexLatestMonth().then((res) => {
          if (res.data.busdate) {
            this.monitorDT = res.data.busdate[0].year_month;
          }
        });
      } else {
        // 非99企业逻辑：获取二级企业时间
        try {
          const params = {
            mapperInterface: ["kanbanym"],
            moreChooseType: "0"
          };
          const res = await getSecKbData(params);
          if (res && res.code === 200 && res.data && res.data.kanbanym && res.data.kanbanym.length > 0) {
            // 获取时间
            this.secondRankTime = res.data.kanbanym[0].year_month;
          }
        } catch (error) {
          console.error('获取时间失败:', error);
        }
      }
    },

    // 获取Banner数据
    //初始化数据
    initData() {
      let params = {
        mapperInterface: ["home", "homeStr"],
      };
      params = { ...params, ...this.propsObject };
      getKbData(params)
        .then((res) => {
          if (res && res.code == 200) {
            this.bannerDataHome = res.data.home;
            this.bannerDataHomeStr = res.data.homeStr;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    swiperChange(index) {
      this.swiperIndex = index;
    },
    handleChangeSwiper(item) {
      this.$refs.swiper.swipeTo(item.id)
    },
    loadCompanyInfoList() {
      return new Promise((resolve) => {
        if (this.companyInfoList) {
          resolve(this.companyInfoList);
          return;
        }

        getAllCompanyInfoList().then((res) => {
          if (res.data.cecLanxinUserRbCompanyList) {
            this.companyInfoList = res.data.cecLanxinUserRbCompanyList;
            setState("compnay_info_list", res.data.cecLanxinUserRbCompanyList);
            resolve(this.companyInfoList);
          }
        });
      });
    },
    //跳转至对应页面
    toPage(item) {
      if (!item.path) return false;
      if (!item.enabled) {
        Toast('暂无访问权限')
        return
      }
      this.$router.push({ path: item.path });
    },
    judgePermission(item) {
      if (item.disabled) {
        Toast('暂无访问权限')
        return false
      }
      return true
    },
    routeToRanking(item) {
      if (!this.judgePermission(item)) {
        Toast('暂无访问权限')
        return
      }
      if (!this.isShowIndi) return false;
      this.$router.push({ path: "/zbBoard?active=1" });
    },
    routeToMonitor(item) {
      if (!this.judgePermission(item)) {
        Toast('暂无访问权限')
        return
      }
      if (!this.isShowMonitor) return false;
      this.$router.push({ path: "/monitor?active=profits" });
      // this.$router.psh({path: "/cecBoard"})
    },
    routeToZbBoardSecond() {
      if (!this.isShowPhb) {
        Toast('暂无访问权限')
        return
      }
      
      // 获取当前选中的企业ID
      const companyId = this.selectedCompany ? this.selectedCompany.id : '99'
      
      this.$router.push({ 
        path: "/zbBoardSecond",
        query: { companyId: companyId }
      });
    },
    filterBannerList() {
      let index = 0

      this.bannerList = this.bannerList.map(item => {
        if (item.name == 'rank') {
          return {
            ...item,
            // id: this.ishowIndi ? index++ : 'empty',
            id: index++,
            disabled: !this.isShowIndi
          }
        }

        if (item.name == 'monitor') {
          return {
            ...item,
            // id: this.isShowMonitor ? index++ : 'empty',
            id: index++,
            disabled: !this.isShowMonitor
          }
        }

        return item
      })
    },
    getPermission(userId) {
      getPermission(userId)
        .then((res1) => {
          let data = res1.data;
          let permissions = data.permissions;
          this.isShowIndi = permissions.includes("indi");
          this.isShowMonitor = permissions.includes("lrjc");
          this.filterBannerList()
          const navList = []

          if (this.isShowIndi && this.reportViewEnabled) {
            permissions.push("ckbg");
          }

          let ownOrgName = data.ownOrgName;
          this.menus.forEach((item) => {
            // 查看报告需要同时拥有 indi 和 ckbg 权限
            if (item.id === 'ckbg') {
              if (permissions.includes("indi") && permissions.includes("ckbg")) {
                navList.push({
                  ...item,
                  enabled: true
                });
              }
            } else if (permissions.includes(item.id)) {
              navList.push({
                ...item,
                enabled: true
              });
            }
          });

          if (navList.length % 2 != 0) {
            const comingSoon = this.menus.find(item => item.id == 'jqqd')
            navList.push(comingSoon);
          }

          this.navList = navList

          sessionStorage.setItem("ownOrgName", ownOrgName);
          if (data.hasOwnProperty("orgList")) {
            let orgList = data.orgList;
            sessionStorage.setItem("orgList", orgList);
          }
        })
        .catch((err) => {
          console.log("err", err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.homeNav {
  font-family: "微软雅黑";
  background: #fff;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .top-selector {
    margin: 0.3rem;
    margin-top: 0.2rem;
    margin-bottom: 0;
    position: relative;
    z-index: 100;
    
    .dropdown-container {
      position: relative;
    }
    
    .dropdown-trigger {
      display: flex;
      align-items: center;
      background: #fff;
      padding: 0.25rem 0.3rem;
      cursor: pointer;
      
      .selected-text {
        color: #333;
        font-size: 0.28rem;
        margin-right: 0.1rem;
      }
      
      .dropdown-icon {
        width: 0;
        height: 0;
        border-left: 0.1rem solid transparent;
        border-right: 0.1rem solid transparent;
        border-top: 0.1rem solid #999;
        transition: transform 0.3s;
        
        &.is-active {
          transform: rotate(180deg);
        }
      }
    }
    
    .dropdown-menu {
      position: absolute;
      top: calc(100% + 0.1rem);
      left: 0;
      width: 100%;
      background: #fff;
      max-height: 6rem;
      overflow-y: auto;
      z-index: 10;
      
      .dropdown-menu-inner {
        padding: 0.1rem 0;
      }
      
      .dropdown-item {
        padding: 0.25rem 0.3rem;
        font-size: 0.28rem;
        color: #666;
        cursor: pointer;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &.is-active {
          background-color: #e6f1fc;
          color: #2d8cf0;
          font-weight: 500;
        }
      }
    }
    
    .dropdown-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 5;
    }
  }
  
  // 下拉框动画
  .dropdown-fade-enter-active,
  .dropdown-fade-leave-active {
    transition: opacity 0.2s, transform 0.2s;
  }
  
  .dropdown-fade-enter,
  .dropdown-fade-leave-to {
    opacity: 0;
    transform: translateY(-0.1rem);
  }

  .banner-container {
    margin: 0 0.3rem;
    margin-top: 0.2rem;
    margin-bottom: .3rem;
    border-radius: .2rem;
    overflow: hidden;
    box-shadow: 2px 2px 15px 0px #ccc;
    
    .swiper {
      box-shadow: 2px 4px 10px 0px #c2c1d3;

      .banner {
        position: relative;
        background-size: 100% 100%;
        color: #fff;
        height: 3.2rem;

        &.banner-rank {
          background: url("../../assets/img/home/<USER>") no-repeat;
          background-size: 100% 100%;

          span {
            bottom: 0.37rem;
          }
        }

        &.banner-monitor {
          background: url("../../assets/img/home/<USER>") no-repeat;
          background-size: 100% 100%;

          span {
            display: inline-block;
            min-width: 1.5rem;
            max-width: 3.5rem;
            padding: .1rem .05rem;
            position: absolute;
            left: 0;
            right: 0;
            margin: auto;
            bottom: 0.65rem;
            background: url(../../assets/img/home/<USER>
            background-size: 100% 100%;
          }
        }

        span {
          display: inline-block;
          width: 100%;
          color: #fff;
          font-size: 0.4rem;
          position: absolute;
          text-align: center;
        }
      }
    }

    .pagination {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: .17rem;
      padding-bottom: .1rem;

      .item {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .item-icon {
          width: 0.7rem;
          height: 0.4rem;
          position: absolute;
          left: 0;
          right: 0;
          margin: auto;
          top: -0.4rem;
          // background: #fff;

          background-image: linear-gradient(to bottom, #c4c1d9, #ece9f5);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          // background: #c4c1d8;

          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 1rem 1rem 0 0;
          // box-shadow: -2px -2px 10px 0px #ccc;

          img {
            width: .3rem;
            height: .28rem;
          }

          // &.item-icon-rank {
          //   background: url(../../assets/img/home/<USER>
          //     no-repeat;
          //   background-size: contain;
          // }

          // &.item-icon-monitor {
          //   background: url(../../assets/img/home/<USER>
          //     no-repeat;
          //   background-size: contain;
          // }
        }

        .item-btn {
          width: 1.8rem;
          height: 0.56rem;

          &.item-btn-rank {
            background: url(../../assets/img/home/<USER>
            background-size: 100%;
          }

          &.item-btn-rank-disabled {
            background: url(../../assets/img/home/<USER>
            background-size: 100%;
          }

          &.item-btn-monitor {
            background: url(../../assets/img/home/<USER>
            background-size: 100%;
          }

          &.item-btn-monitor-disabled {
            background: url(../../assets/img/home/<USER>
            background-size: 100%;
          }
        }
      }
    }
  }

  .container {
    flex: 1;
    overflow: auto;
    margin: 0 0.3rem;
    
    .no-data-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 4rem;
        height: 4rem;
      }
    }

    .box {
      width: 48%;
      height: 2.5rem;
      display: flex;
      
      flex-direction: column;
      justify-content: space-between;
      // background: linear-gradient(180deg, #2595e8 0%, #ffffff 100%);
      box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
      box-sizing: border-box;
      border-radius: 8px;
      float: left;
      margin-top: 0.3rem;
      padding: 0.2rem 0.3rem;
      // padding-bottom: 0.2rem;
      padding-top: 0.3rem;

      &:nth-of-type(even) {
        margin-left: 0.25rem;
      }

      &:nth-child(1),
      &:nth-child(2) {
        margin-top: 0.1rem;
      }

      .title {
        img {
          width: 0.33rem;
          height: 0.33rem;
          vertical-align: middle;
        }

        span {
          margin-left: 0.05rem;
          font-size: 0.3rem;
          font-weight: 700;
          vertical-align: middle;
        }
      }

      .text {
        display: inline-block;
        min-height: 1.2rem;
        padding: 0 0.175rem;
        color: #666;
        font-size: 0.275rem;
      }
    }
  }
  .banner-second-rank-img {
    width: 100%;
    height: 100%;
    border-radius: .2rem;
    object-fit: cover;
    display: block;
    cursor: pointer;
  }
}

.banner-second-container {
  position: relative;
  width: 100%;
  
  .banner-second-rank-img {
    width: 100%;
    height: 100%;
    border-radius: .2rem;
    object-fit: cover;
    display: block;
    cursor: pointer;
  }
  
  .banner-second-time {
    position: absolute;
    bottom: 0.2rem;
    left: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-size: 0.4rem;
  }
}
</style>
