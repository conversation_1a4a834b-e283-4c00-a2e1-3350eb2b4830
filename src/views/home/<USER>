<template>
  <div class="homeNav">
    <div class="banner" v-if="isShowBanner">
      <img v-if="showIndi" src="../../assets/img/homeBannerAboard.png" @click="jumpZbBoard" alt="">
      <img v-else src="../../assets/img/homeBanner.png" alt="">
    </div>
    <div class="container">
      <div class="modulesBox" v-for="(item ,index) in navList " :key="index">
        <h2 v-if="item.title"><span></span>{{ item.title }}</h2>
        <div class="itemBox" v-for="(items,indexs) in item.dataList" :key="indexs">
          <div class="modules">
            <img :src="items.imgUrl" width="100%" alt="">
            <div class="btn"><span @click="toPage(items.path)">查看</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {getPermission, getUserId} from "../../http/api";
import env from "../../http/env";

export default {
  name: 'home',
  data() {
    return {
      navList: [
        // {
        //   id: 'hr',
        //   imgUrl: require('../../assets/img/homeHr.png'),
        //   title: '资源家底',
        //   path: '/hrIndex'
        // },
        // {
        //   id: 'siku',
        //   imgUrl: require('../../assets/img/homeSiku.png'),
        //   title: '生产经营',
        //   path: ''
        // },
      ],
      menus: [
        {
          title: '资源家底',
          dataList: [
            {
              id: 'hr',
              imgUrl: require('../../assets/img/homeHr.png'),
              path: '/hrIndex'
            },
            {
              id: 'zc',
              imgUrl: require('../../assets/img/homeZcdt.png'),
              path: '/assetsMap'
            },
          ]
        },
        {
          title: '经营管理',
          dataList: [
            {
              id: 'siku',
              imgUrl: require('../../assets/img/homeSiku.png'),
              path: '/siku'
            },
            {
              id: 'jj',
              imgUrl: require('../../assets/img/homeJjyxbg.png'),
              path: '/jjyxReport'
            },
          ]
        },
        {
          title: '风险监控',
          dataList: [
            {
              id: 'lj',
              imgUrl: require('../../assets/img/homeLiangjin.png'),
              path: '/liangjin'
            },
          ]
        }
      ],
      // menus: [
      //   {
      //     id: 'hr',
      //     imgUrl: require('../../assets/img/homeHr.png'),
      //     title: '资源家底',
      //     path: '/hrIndex'
      //   },
      //   {
      //     id: 'zc',
      //     imgUrl: require('../../assets/img/homeZcdt.png'),
      //     title: '',
      //     path: '/assetsMap'
      //   },
      //   {
      //     id: 'siku',
      //     imgUrl: require('../../assets/img/homeSiku.png'),
      //     title: '经营管理',
      //     path: '/siku'
      //   },
      //   {
      //     id: 'jj',
      //     imgUrl: require('../../assets/img/homeJjyxbg.png'),
      //     title: '',
      //     path: '/jjyxReport'
      //   },
      //   {
      //     id: 'lj',
      //     imgUrl: require('../../assets/img/homeLiangjin.png'),
      //     title: '风险监控',
      //     path: '/liangjin'
      //   },
      // ],
      showIndi: false,
      isShowBanner:false
    }
  },
  created() {
    // let userId = localStorage.getItem("cec_hr_lx_userId");
    // if (userId === undefined || userId === "undefined" || userId === null || userId === "null" || userId.includes('object')) {
    //   lx.biz.getAuthCode({
    //     appId: env.appId,
    //     success: function (res) {
    //       console.log("res", res)
    //       // console.log("authCode", res.authCode)
    //       getUserId(res.authCode).then(res1 => {
    //         console.log("getUserId", res1)
    //         userId = res1.data
    //         if (userId !== {}) {
    //           localStorage.setItem("cec_hr_lx_userId", userId)
    //         }
    //         console.log("userId1111", JSON.stringify(userId))
    //         this.getPermission(userId);
    //       }).catch(err => {
    //         console.log("err", err)
    //       })
    //     },
    //     fail: function (err) {
    //       console.log("err", err)
    //     },
    //   });
    // } else {
    //   console.log("userId2222", userId)
    //   this.getPermission(userId);
    // }

    // this.getPermission("18910015136")
    this.getPermission("18211013898")
    // // this.getPermission("18510516069")
  },
  methods: {
    toPage(val) {
      this.$router.push({path: val})
    },
    //跳转到指标看板
    jumpZbBoard() {
      this.$router.push({path: "/zbBoard"})
    },
    getPermission(userId) {
      getPermission(userId).then(res1 => {
        console.log("getPermission", res1)
        this.isShowBanner=true
        let data = res1.data;
        let permissions = data.permissions;
        this.showIndi = permissions.includes('indi');
        let ownOrgName = data.ownOrgName;
        //this.ownOrgName = data.ownOrgName;
        this.menus.forEach(item1 => {
          let menu = {title: item1.title}
          let dataList = [];
          item1.dataList.forEach(item => {
            if (permissions.includes(item.id)) {
              if (ownOrgName !== "全集团" && item.id == 'hr') {
                item.path = "/hrIndexTwo";
              }
              dataList.push(item);
            }
          });

          if (dataList.length > 0) {
            menu.dataList = dataList;
            this.navList.push(menu);
          }
        });
        // this.menus.forEach(item => {
        //   if (permissions.includes(item.id)) {
        //     this.navList.push(item);
        //   }
        // });

        // let ownOrgName = data.ownOrgName;
        sessionStorage.setItem("ownOrgName", ownOrgName)
        console.log(sessionStorage.getItem("ownOrgName"), 'ownOrgName1');
        if (data.hasOwnProperty("orgList")) {
          let orgList = data.orgList;
          sessionStorage.setItem("orgList", orgList)
        }

        console.log("orgList", sessionStorage.getItem("orgList"))
      }).catch(err => {
        console.log("err", err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.homeNav {
  padding: 0.5rem 0.3rem;
  font-family: "微软雅黑";
  background: #fff;
  height: 100vh;
  overflow: auto;

  .banner {
    position: relative;
    // background: url("../../assets/img/jscBg.png") no-repeat;
    img {
      width: 100%;
      height: 3.2rem;
      box-shadow: 2px 6px 40px 0px rgba(44, 67, 108, 0.53);
      border: none;
      display: block;
      background: #dee3eb;
    }

    .text {
      position: absolute;
      top: 0.55rem;
      left: 0.9rem;
    }

    color: #fff;
    font-size: 0.3rem;

    h4 {
      font-size: 0.5rem;
      font-weight: 600;
      color: #ffffff;
      padding: 0.25rem 0;
    }
  }

  .itemBox {
    margin-bottom: 0.3rem;
  }

  .itemBox:last-child {
    margin-bottom: 0 !important;
  }

  .container {
    .modules {
      position: relative;

      .btn {
        position: absolute;
        right: 0.43rem;
        top: 1.38rem;

        span {
          display: flex;
          width: 2.1rem;
          height: 0.72rem;
          color: #26346d;
          background-color: rgba(255, 255, 255, 0.7);
          border-radius: 0.06rem;
          justify-content: center;
          align-items: center;
          font-size: 0.32rem;
        }
      }
    }


    h2 {
      color: #181c26;
      font-size: 0.36rem;
      font-weight: 600;
      padding-bottom: 0.3rem;
      padding-top: 0.5rem;

      span {
        width: 0.06rem;
        height: 0.36rem;
        background: #3e7af5;
        border-radius: 3px;
        display: inline-block;
        position: relative;
        top: 0.05rem;
        margin-right: 0.26rem;
      }
    }

    .box {
      background-color: #fff;
      border-radius: 0.1rem;
      padding: 0.3rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 2px 6px 20px 0px rgba(44, 67, 108, 0.16);

      .left {
        position: relative;

        text-align: center;

        img {
          width: 3rem;
          height: 2.05rem;
        }

        span {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 600;
          color: #ffffff;
          font-size: 0.46rem;
        }
      }

      .right {
        font-size: 0.3rem;
        color: #444444;
        padding-left: 0.3rem;
      }

      .btn {
        margin-top: 0.3rem;

        span {
          display: flex;
          width: 2.1rem;
          height: 0.72rem;
          color: #fff;
          background-color: #3e7af5;
          border-radius: 2.1rem;
          justify-content: center;
          align-items: center;
          font-size: 0.32rem;
        }
      }
    }
  }
}
</style>
