<template>
  <div class="zbBoard" :class="containerClass">
    <div class="dateBox">
      <van-cell-group>
        <van-field style="text-align:center" readonly clickable placeholder="请选择日期" :value="dateValue"
          @click="showPicker = true" />
        <van-popup v-model="showPicker" round position="bottom" :lazy-render='false'>
          <van-picker ref="dtPickerRef" title="请选择日期" show-toolbar :columns="columns"
            :columns-field-names="customFieldName" @confirm="yearConfirm" @cancel="cancel" />
        </van-popup>
      </van-cell-group>
    </div>
    <div class="tabsBox">
      <!-- tab区域 -->
      <van-tabs v-model="active" swipeable>
        <van-tab v-for="(item, index) in tabList" :title="item.label" :key="index" :name="item.key" />
      </van-tabs>
      <!-- 内容区域 -->
      <div v-if="dt" class="tabContent">
        <div v-if="active == '公司整体' || active == 'gszt'">
          <group-all :userId="userId" :companyInfoList="companyInfoList" @toPage="toPage" :dt="dt" :companyId="companyId" />
        </div>
        <div v-if="active == '指标排名' || active == 'zbpm'">
          <ranking :userId="userId" :companyInfoList="companyInfoList" :rankTab="rankTab" @rankTabChange="rankTabChange"
            @toPage="toPage" :dt="dt" :companyId="companyId" />
        </div>
        <div v-if="active == '二级企业' || active == 'ejqy'">
          <second :userId="userId" :companyInfoList="companyInfoList" :dt="dt" @updateDateList="updateDateList" :companyId="companyId"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs"

import { getAllThirdCompanyInfoList, getSecKbData, getModuleInfo } from "@/http/api";
import { getUserId } from "@/utils"
import { setState, getState } from "@/utils/cache"
import groupAll from './groupAll/index.vue';
import ranking from './ranking/index.vue'
import second from './second/index.vue';
import { setDocumentTitle } from '@/utils/document'
export default {
  name: 'zbBoard',
  components: {
    groupAll,
    ranking,
    second
  },
  watch: {
    active: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        let el = document.querySelector('.tabsBox .tabContent');
        el && el.scrollTo(0, 0)

        // 只有在oldVal存在（说明是用户切换）且newVal不同时才更新路由
        if (oldVal && newVal !== oldVal) {
          this.$router.replace({
            path: this.$route.path,
            query: {
              ...this.$route.query,
              active: newVal
            }
          })
        }

        this.updateDateList()
      }
    },
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.loadCompanyInfoList();
      }
    },
    companyClickSecond: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.active = newVal.active
        }
      }
    }
  },
  computed: {
    containerClass() {
      if (this.tabList.length > 0) {
        const className = this.tabList.find(item => item.key == this.active).className
        return className
      }
      return ''
    },
    companyClickSecond(){
      return this.$store.state.companyClickSecond
    } 
  },
  data() {
    return {
      companyId: this.$route.query.companyId || getState('selectedCompanyId'),
      userId: '',
      companyInfoList: getState('second_compnay_info_list') || null,
      rankTab: 'progress',
      dateValue: "",
      dt: null,
      showPicker: false,
      active: 'zbpm',
      columns: [],
      customFieldName: {
        text: 'text',
        children: 'children',
      },
      tabList: [],
      monthList: [
        { text: '1月' }, { text: '1-2月' }, { text: '1-3月' }, { text: '1-4月' }, { text: '1-5月' }, { text: '1-6月' },
        { text: '1-7月' }, { text: '1-8月' }, { text: '1-9月' }, { text: '1-10月' }, { text: '1-11月' }, { text: '1-12月' }
      ],
      defaultIndex: 0,
      
    }
  },

  async mounted() {
    setDocumentTitle(`经营业绩排行榜`)
    
    // 更新tab列表和默认active
    // this.updateTabList(this.$store.state.moduleInfo)
    
    const userId = await getUserId()
    this.userId = userId

    await this.initModulePermission()
    this.updateTabList(this.$store.state.moduleInfo)
    this.loadCompanyInfoList()
  },
  methods: {
    async initModulePermission() {
      const res = await getModuleInfo(this.userId, this.companyId);
        
      if (res && res.code === 200 && res.data && res.data.moduleInfo) {
        this.$store.dispatch('updateModuleInfo', res.data.moduleInfo);
      }
    },
    // 检查特定子模块是否有权限
    hasModulePermission(moduleInfo, moduleId) {
      if (!moduleInfo) return false;
      const hhbModule = moduleInfo.find(item => item.id === 'phb');
      if (!hhbModule || !hhbModule.children) return false;
      return hhbModule.children.some(child => child.id === moduleId);
    },

    getTabIndexByLabel(label) {
      return this.tabList.findIndex(item => item.label == label) + ''
    },
    
    // 更新标签页列表
    updateTabList(moduleInfo) {
      const tabs = [];
      
      // 检查各个子模块的权限
      if (this.hasModulePermission(moduleInfo, 'gszt')) {
        tabs.push({
          label: '公司整体',
          key: 'gszt',
          className: 'zbBoard-all'
        });
      }
      if (this.hasModulePermission(moduleInfo, 'zbpm')) {
        tabs.push({
          label: '指标排名',
          key: 'zbpm',
          className: 'zbBoard-rank'
        });
      }
      if (this.hasModulePermission(moduleInfo, 'ejqy')) {
        tabs.push({
          label: '二级企业',
          key: 'ejqy',
          className: 'zbBoard-second'
        });
      }
      
      this.tabList = tabs;
      
      // 设置默认active tab
      if (this.$route.query.active && tabs.some(tab => tab.label === this.$route.query.active)) {
        // 如果URL中有active参数，且该tab存在权限，则使用URL中的active
          this.active = this.$route.query.active
      } else {
        // 没有URL参数，使用默认规则
        const rankingTab = tabs.find(tab => tab.label === '指标排名')
        this.active = rankingTab ? rankingTab.key : tabs[0].key
      }
    },

    async updateDateList(entp_no) {
      let params = {
        dt: '2025-04',
        mapperInterface: ["xuanzheym"],
        moreChooseType: '0',
        entp_no,
        companyId: this.companyId
      }
      await getSecKbData(params).then(res => {
        if (res.code == 200) {
          let years = []
          let columns = res.data.xuanzheym;
          this.columns = []
          let columnObj = []
          columns.forEach(element => {
            columnObj.push({ text: this.monthList[new Date(element).getMonth()].text, value: element })
            let year = element.slice(0, 4)
            if (!years.includes(year)) {
              years.push(year)
            }
          })
          years = years.sort((a, b) => { return b - a });
          years.forEach(element => {
            let secondcol = columnObj.filter((ele) => { return ele.value.slice(0, 4) === element })
            let column = { 'text': element, children: secondcol }
            this.columns.push(column)
          });
          
          // Get latest date
          let year = years[0];
          let yearMonth = ''
          this.columns.forEach((element) => {
            if (element.text == year) {
              yearMonth = element.children[element.children.length - 1].value
              element.defaultIndex = element.children.length - 1
            }
          })
          let month = yearMonth.split("-")[1]
          if (Number(month) > 1) {
            month = `1-${Number(month)}`
          } else {
            month = "1"
          }

          this.dt = this.dt || `${yearMonth.split("-")[0]}-${yearMonth.split("-")[1]}`
          this.dateValue = this.dateValue || `${new Date(yearMonth).getFullYear()}年 ${month}月`
        }
      })
    },
    //年选择器
    yearConfirm(value) {
      let month = this.monthList.findIndex(item => {
        return item.text == value[1]
      }) + 1;
      if (month < 10) month = `0${month}`
      this.dt = `${value[0]}-${month}`
      this.dateValue = `${value[0]}年 ${value[1]}`
      this.showPicker = false;
    },
    //点击取消按钮时触发的事件
    cancel() {
      this.showPicker = false
    },
    loadCompanyInfoList() {
      if (this.companyInfoList) return
      
      getAllThirdCompanyInfoList({
        companyId: this.companyId,
        userId: this.userId
      }).then((res) => {
        if (res.data.cecLanxinUserRbCompanyList) {
          this.companyInfoList = res.data.cecLanxinUserRbCompanyList
          setState('second_compnay_info_list', res.data.cecLanxinUserRbCompanyList)
        }
      })
    },

    rankTabChange(tab) {
      this.rankTab = tab
    },
    toPage(val) {
      this.active = val;
    },
  }
}
</script>

<style lang="scss" scoped>
.zbBoard-all {
  // background: url(../../assets/img/ranking/all_bg.png);
  // background-size: cover !important;
  // background-repeat: no-repeat;
  overflow: hidden !important;

  .tabContent {
    overflow: hidden !important;
  }

  ::v-deep .tabsBox {
    background: url(../../assets/img/ranking/all_bg.png);
    background-size: cover !important;
    background-repeat: no-repeat;
  }

  ::v-deep .van-tabs__nav {
    .van-tab__text {
      color: #000;
    }
  }

  ::v-deep .van-tabs--line .van-tabs__wrap {
    box-shadow: none !important;

    .van-tabs__line {
      // background: #000;
    }
  }
}

.zbBoard-rank {
  // background: url(../../assets/img/ranking/all_bg.png);
  // background-size: cover !important;
  // background-repeat: no-repeat;
  overflow: hidden !important;
  background-color: #fff;

  .tabContent {
    overflow: hidden !important;
  }

  ::v-deep .tabsBox {
    background: url(../../assets/img/ranking/second_rank_bg.png);
    background-size: contain !important;
    background-repeat: no-repeat;
  }

  ::v-deep .van-tabs__nav {
    .van-tab__text {
      color: #000;
    }
  }

  ::v-deep .van-tabs--line .van-tabs__wrap {
    box-shadow: none !important;

    .van-tabs__line {
      // background: #000;
    }
  }
  // ::v-deep .van-tabs__nav {
  //   .van-tab__text {
  //     color: #FBD95F;
  //   }
  // }

  // &.zbBoard-rank-progress {

  //   ::v-deep .tabsBox {
  //     background: url(../../assets/img/ranking/progress_bg.png);
  //     background-size: cover !important;
  //     background-repeat: no-repeat;
  //   }
  // }

  // &.zbBoard-rank-retrogress {
  //   ::v-deep .tabsBox {
  //     background: url(../../assets/img/ranking/retrogress_bg.png);
  //     background-size: cover !important;
  //     background-repeat: no-repeat;
  //   }
  // }

  // ::v-deep .van-tabs--line .van-tabs__wrap {
  //   box-shadow: none !important;

  //   .van-tabs__line {
  //     background: linear-gradient(94deg, #FCE697 0%, #FBD95F 100%);
  //   }
  // }
}

.zbBoard-second {
  background: url(../../assets/img/ranking/second_bg.png);
  background-size: cover !important;
  background-repeat: no-repeat;

  ::v-deep .van-tabs--line .van-tabs__wrap {
    box-shadow: none !important;
  }
}

.zbBoard {
  // background-color: ;
  width: 100vw;

  // background: #f6f7f9;
  height: 100%;

  overflow: auto;
  font-family: "微软雅黑";

  //日期组件
  .dateBox {
    background: #fff;
    height: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;

    ::v-deep .van-cell {
      display: flex;
      align-items: center;
      width: 3.2rem;
      height: 0.6rem;
      line-height: 0.58rem;
      padding: 0px 10px;
      font-size: 0.28rem;
      border-radius: 0.08rem;
      border: 0.01rem solid #3e7bfa;
      background-color: #F1F6FF;
      color: rgba(62, 123, 250, 1);
      margin: 0 auto;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      position: relative;

      ::after {
        content: "";
        position: absolute;
        color: red;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("../../assets/img/rlIcon.png") no-repeat;
        background-size: cover;
        display: block;
        width: 0.28rem;
        height: 0.28rem;
      }
    }

    ::v-deep [class*="van-hairline"]::after {
      border: none;
    }

    ::v-deep .van-field__control:read-only {
      color: rgba(62, 123, 250, 1);
    }

    ::v-deep .van-cell-group {
      background-color: #fff;
      // position: inherit;
    }

    ::v-deep .van-field__control::placeholder {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item {
      color: rgba(34, 34, 34, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected {
      color: rgba(62, 123, 250, 1);
      font-size: 0.4rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected .van-ellipsis {
      background-color: #f1f6ff;
      width: 2.8rem;
      text-align: center;
      padding: 0.1rem 0;
    }

    ::v-deep .van-picker__confirm {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
  }

  .tabsBox {
    height: calc(100% - 1.1rem); // 100% - 日期高度

    .van-tabs {
      z-index: 99;
    }

    //tab滑动切换
    ::v-deep .van-tab {
      color: rgba(17, 17, 17, 1);
      font-size: 0.36rem;
    }

    ::v-deep .van-tab--active {
      color: #111;
      font-size: 0.36rem;
    }

    ::v-deep .van-tabs__line {
      background-color: #3e7bfa;
      width: 1rem;
      height: 0.07rem;
      border-radius: 0;
    }

    ::v-deep .van-tabs--line .van-tabs__wrap {
      box-shadow: 0px 0.03rem 0.07rem 0 #e6e6e6;
      height: 0.9rem;

      .van-tabs__nav {
        background-color: transparent !important;
      }

      .van-tabs__line {
        width: 1rem;
        height: 0.08rem;
        border-radius: 1em;
      }
    }

    .tabContent {
      height: calc(100% - 0.9rem);
      background: transparent !important;
      overflow: auto;
    }
  }
}
</style>
