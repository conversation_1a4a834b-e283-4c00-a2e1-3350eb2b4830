<template>
  <div class="groupAll">
    <ul>
      <li v-for="(item,index) in groupList" :key="index" @click="jumpPage(item.val)">
        <div class="box">
          <div class="one">
            <p>{{item.name}}</p>
            <h3 :style="{padding:item.name=='研发经费投入强度' ||item.name=='全员劳动生产率' ?'0':'0.15rem 0',color: blueReg}">{{ formatAmount(item.dq,1) }}</h3>
            <p :style="{color: blueReg}">{{item.unit}}</p>
          </div>
          <div class="commanBox two">
            <p>同期</p>
            <h3>{{ formatAmount(item.tq) }}</h3>
            <p>{{item.unit}}</p>
          </div>
          <div class="commanBox three">
            <p>同比</p>
            <h3 :style="{color: getTextColor(item)}">{{(item.tb*1)>0?'+'+item.tb:item.tb}}</h3>
            <p> {{ item.unit2 }} </p>
          </div>
          <div class="commanBox four">
            <p>任务</p>
            <h3>{{ formatAmount(item.rw) }}</h3>
            <p>{{item.unit}}</p>
          </div>
          <div class="commanBox five">
            <p>达成率</p>
            <!-- <h3 :style="{color: blueReg}">{{item.dcl}}</h3> -->
             <h3 :style="{color: blueReg}">
              <span v-if="item.dcl && item.dcl>999">999<i>⊕</i></span>
              <span v-else-if="item.dcl && item.dcl<-999">-999<i>⊕</i></span>
              <span v-else>{{item.dcl}}</span>
            </h3>
            <p>%</p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
const redReg = "#E60012";
const greenReg = "#32CD91";
const blueReg = "#008AE6";
import { getKbData } from "@/http/api";
import { getGroupList } from "./groupList.js";
import { formatAmount } from '@/utils/chart';
export default {
  name: 'groupAll',
  props: {
    dt: {
      type: String,
      default: ""
    }
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      }
    }
  },
  data() {
    return {
      blueReg: blueReg,
      groupList:[],
    }
  },
  computed: {
    getUnit() {

    }
  },
  created() {
    this.groupList = getGroupList()
    this.initData()
  },
  methods: {
    formatAmount,
    // 初始化页面
    initData() {
      if(!this.dt) return false;
      let params = {
        dt: this.dt,
        mapperInterface: ["yysr","jinglr","lrze","gmjlr","yyxjbl","zcfzl","jzcsyl","yftrqd","qyldscl"],
        moreChooseType: '0'
      }
      this.groupList = getGroupList()
      getKbData(params).then(res => {
        if(res.code == 200) {
          this.groupList.forEach(item => {
            if(res.data[item['key']]) {
              item = Object.assign(item,res.data[item['key']])
            }
          })
        }
      })
    },
    // 页面跳转
    jumpPage(val) {
      sessionStorage.setItem('secondPageVal',val)
      this.$emit('toPage',1)
    },
    getTextColor(item){
      if(!item.dq || item.dq == '-') return blueReg
      if((item.tb*1) == 0) {
        return blueReg
      } else if((item.tb*1)>0 && item.val != 'A08' || ((item.tb*1)<0 && item.val == 'A08')) {
        return redReg
      } else {
        return greenReg
      }
    },
    getTextWidth(text, font) {
      var canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
      var context = canvas.getContext("2d"); 
      context.font = font;
      var metrics = context.measureText(text);
      return metrics.width;
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  // background-color: red;
  ul {
      padding: 0.35rem;
    li {
      background: #ffffff;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.08rem;
      overflow: hidden;
      margin-bottom: .35rem;
    //   height: 2.33rem;
      .box {
        display: flex;
        text-align: center;
        .commanBox {
          display: flex;
          flex-direction: column;//纵向排列
          justify-content: center;
          padding: 0.3rem 0;
          width: 1.25rem;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(113, 115, 122, 1);
          }
          p:last-child {
            font-size: 0.2rem;
            color: rgba(34, 34, 34, 1);
          }
          h3 {
            font-size: 0.3rem;
            color: rgba(34, 34, 34, 1);
            font-weight: 400;
            padding:.3rem 0;
          }
        }
        .one {
          // width: 1.9rem;
          flex: 1;
          background-color: rgba(224, 243, 255, 1);
          padding: 0.3rem 0;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(34, 34, 34, 1);
            padding-left: 0.1rem;
            padding-right: 0.1rem;
          }
          p:last-child {
            font-size: 0.26rem;
            color: rgba(0, 138, 230, 1);
            font-weight: 400;
            padding-left: 0.1rem;
            padding-right: 0.1rem;
          }
          h3 {
            font-size: 0.42rem !important;
            color: rgba(0, 138, 230, 1);
            font-weight: 600;
            padding:.15rem 0;
          }
        }
        .three {
          h3 {
            color: rgba(50, 205, 145, 1);
          }
        }
        .two,.three,.four{
          position: relative;
        }
        .two::after,.three::after,.four::after{
          content: '';
          position: absolute;
          border-left: 1px dashed #999999;
          width: 1px;
          height: .66rem;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    li:last-child{
        margin-bottom: 0;
    }
  }
}
</style>
