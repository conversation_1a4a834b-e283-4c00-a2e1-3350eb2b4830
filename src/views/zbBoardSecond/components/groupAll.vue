<template>
  <div class="groupAll">
    <div class="report-link" @click="viewReport">
      <span>查看报告</span>
      <img src="../../../assets/img/ranking/view_report_icon.png" alt="">
    </div>
    <ul>
      <li v-for="(item,index) in groupList" :key="index">
        <div class="box">
          <div class="one">
            <p>{{item.name}}</p>
            <h3 :style="{padding: item.name.includes('净资产收益率') || item.name.includes('全员劳动生产率') ? '0' : '0.15rem 0',color: blueReg}">{{ formatNum(item.dq, item.unit == '亿元' ? undefined : 1) }}</h3>
            <p :style="{color: blueReg}">{{item.unit}}</p>
          </div>
          <div class="right">
            <div class="commanBox two">
              <p class="item-label">去年同期</p>
              <h3 class="item-value">{{ formatNum(item.tq) }}</h3>
              <p class="item-unit">{{item.unit}}</p>
            </div>
            <!-- <div class="split"/> -->
            <div class="commanBox three">
              <p class="item-label">同比</p>
              <h3
                v-if="['lrze', 'jinglr', 'gmjlr'].includes(item.key)"
                :style="{color: getTextColor(item)}"
                class="item-value"
              >
              {{ !isNaN(Number(item.dq) - Number(item.tq)) ? formatNum(Number(item.dq) - Number(item.tq), undefined, 'ratio') : '-' }}
              </h3>
              <h3 v-else class="item-value" :style="{color: getTextColor(item)}">{{ formatNum(item.tb, undefined, 'ratio') }}</h3>
              <p class="item-unit"> {{ ['lrze', 'jinglr', 'gmjlr'].includes(item.key) ? item.unit : item.unit2 }} </p>
            </div>
            <!-- <div class="split"/> -->
            <div class="commanBox four">
              <p class="item-label">预算目标</p>
              <h3 class="item-value">{{ formatNum(item.rw) }}</h3>
              <p class="item-unit">{{item.unit}}</p>
            </div>
          </div>
          
          <!-- <div class="commanBox five">
            <p>达成率</p>
             <h3 :style="{color: blueReg}">
              <span v-if="item.dcl && item.dcl>999">999<i>⊕</i></span>
              <span v-else-if="item.dcl && item.dcl<-999">-999<i>⊕</i></span>
              <span v-else>{{item.dcl}}</span>
            </h3>
            <p>%</p>
          </div> -->
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { Toast } from "vant";
import FileIcon from "../../../assets/img/ranking/icon_file.png"
import { getKbData, getReportStatus } from "@/http/api";
import { formatNum } from "@/utils";

import { getGroupList } from "./groupList.js";

const redReg = "#E60012";
const greenReg = "#32CD91";
const blueReg = "#008AE6";

export default {
  name: 'groupAll',
  props: {
    dt: {
      type: String,
      default: ""
    },
    currentMonth: Number,
    companyInfoList: Array,
    userId: String
  },
  // watch: {
  //   dt: {
  //     deep: true,
  //     handler(newVal, oldVal) {
  //       this.initData()
  //     }
  //   }
  // },
  data() {
    return {
      blueReg: blueReg,
      reportList: [],
      groupList: [],
    }
  },
  computed: {
    getUnit() {

    },
    reportViewEnabled() {
      let self = {}

      this.companyInfoList.forEach(item => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self = item
        }
      })

      // 只有全集团运行查看月报
      if (self.cpyTag == '01') return true

      return false
    }
  },
  created() {
    this.groupList = getGroupList()
    this.initData()
  },
  methods: {
    formatNum,
    viewReport() {
      // 只有全集团可以点击查看月报
      if (!this.reportViewEnabled) {
        Toast('无访问权限')
        return
      }
      this.$router.push({ path: '/viewReport' })
    },
    // 初始化页面
    initData() {
      if(!this.dt) return false;
      let params = {
        dt: this.dt,
        mapperInterface: ["yysr","jinglr","lrze","gmjlr","yyxjbl","zcfzl","jzcsyl","yftrqd","qyldscl"],
        moreChooseType: '0'
      }
      this.groupList = getGroupList()
      getKbData(params).then(res => {
        if(res.code == 200) {
          this.groupList.forEach(item => {
            if(res.data[item['key']]) {
              item = Object.assign(item,res.data[item['key']])
            }
          })
        }
      })
    },
    // 页面跳转
    jumpPage(val) {
      sessionStorage.setItem('secondPageVal',val)
      this.$emit('toPage',1)
    },
    getTextColor(item){
      if(!item.dq || item.dq == '-') return blueReg
      if((item.tb*1) == 0) {
        return blueReg
      } else if((item.tb*1)>0 && item.val != 'A08' || ((item.tb*1)<0 && item.val == 'A08')) {
        return redReg
      } else {
        return greenReg
      }
    },
    getTextWidth(text, font) {
      var canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
      var context = canvas.getContext("2d"); 
      context.font = font;
      var metrics = context.measureText(text);
      return metrics.width;
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  display: flex;
  flex-direction: column;

  .report-link {
    width: 100vw;
    background: #fff;
    padding: 0.3rem;
    color: #4894E4;
    position: fixed;
    top: calc(3.1rem + 5vh + env(safe-area-inset-top));
    font-size: 0.32rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 6px 20px rgba(74, 103, 157, 0.16);
    z-index: 10000;

    span {
      display: flex;
      height: 0.3rem;
      align-items: center;
    }

    span::before {
      content: " ";
      display: inline-block;
      width: 0.1rem;
      height: 100%;
      background: #4894E4;
      border-top-right-radius: 0.05rem;
      border-bottom-right-radius: 0.05rem;
      margin-right: 0.17rem;
    }

    img {
      height: 0.38rem;
      width: 0.5rem;
    }
  }

  ul {
    max-height: calc(100vh - 4.5rem);
    overflow: auto;
    padding: 0.35rem;
    padding-bottom: 100px;

    margin-top: 3.2rem;

    li {
      background: #ffffff;
      border-radius: 0.08rem;
      margin-bottom: .35rem;

      .box {
        display: flex;
        text-align: center;
        min-height: 2.35rem;

        .commanBox {
          display: flex;
          width: 100%;
          // flex-direction: column;//纵向排列
          justify-content: space-around;
          align-items: center;
          // padding: 0.35rem 0;
          // width: 1.25rem;

          p:first-child {
            font-size: 0.28rem;
            color: #71737A;
          }
          p:last-child {
            font-size: 0.26rem;
            color: #222;
          }
          h3 {
            font-size: 0.35rem;
            color: #222;
            font-weight: 400;
            // margin: .25rem 0;
          }
        }
        .one {
          width: 34%;
          // height: 100%;
          flex: 1;
          background-color: #d9f1fe;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0.28rem 0;
          justify-content: space-between;
          box-shadow: 0 6px 20px rgba(74, 103, 157, 0.16);
          overflow: hidden;
          border-top-left-radius: 0.1rem;
          border-bottom-left-radius: 0.1rem;

          p:first-child {
            font-size: 0.28rem;
            color: #222;
          }
          p:last-child {
            font-size: 0.26rem;
            color: #008AE6;
            font-weight: 400;
          }
          h3 {
            font-size: 0.50rem !important;
            color: #008AE6;
            font-weight: 600;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          width: calc(100% - 34%);
          padding: 0.28rem 0.15rem;
          // padding: 0 0.06rem;
          box-shadow: 5px 6px 20px rgba(74, 103, 157, 0.16);
          overflow: hidden;
          border-top-right-radius: 0.1rem;
          border-bottom-right-radius: 0.1rem;

          .item-label {
            width: 2.6rem;
            text-align: left;
            padding-left: 0.05rem;
          }

          .item-value {
            width: 1.5rem;
            text-align: right;
          }

          .item-unit {
            width: 1.8rem;
            text-align: right;
            // padding-right: 0.01rem;
          }

          .split {
            width: 1px;
            height: .66rem;
            border-right: 1px dashed #999;
          }

          .three {
            h3 {
              color: rgba(50, 205, 145, 1);
            }
          }
          .two,.three,.four{
            position: relative;
          }
          // .two::after,.three::after{
          //   content: '';
          //   position: absolute;
          //   border-left: 1px dashed #999999;
          //   width: 1px;
          //   height: .66rem;
          //   right: -1px;
          //   top: 50%;
          //   transform: translateY(-50%);
          // }
        }
      }
    }
    li:last-child{
        margin-bottom: 0;
    }
  }
}
</style>
