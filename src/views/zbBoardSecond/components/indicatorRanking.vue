<template>
  <div
    class="container"
    :class="rankTab == 'progress' ? 'progress-container' : 'retrogress-container'"
  >
    <div class="tab">
      <div class="tab-inner">
        <span
          :class="rankTab == 'progress' ? 'actived-tab' : ''"
          @click="handleTabChange('progress')"
        >
          红榜
        </span>
        <span
          :class="rankTab == 'retrogress' ? 'actived-tab' : ''"
          @click="handleTabChange('retrogress')"
        >
          黑榜
        </span>
      </div>
    </div>
    <div class="content">
      <div class="index-list">
        <div
          v-for="item in indexList"
          class="index-item"
          :class="item.value == tgt_no ? 'index-item-actived' : ''"
          :key="item.value"
          @click="handleIndexChange(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
      <p class="desc" @click="tableVisible = true">
        {{ 
          tgt_no == "A01" ? (
            "26户二级企业中，中电金投和中电蓝海不参与排名"
          ) : (
            rankTab == "progress" ? "26户二级企业参与排名，亏损企业不进入红榜" : "26户二级企业参与排名"
          )
        }}
        <img src="../../../assets/img/ranking/icon_enter.png"/>
      </p>

      <template v-if="rankTab == 'progress'">
        <rankling-list
          :judgeViewEnabled="judgeViewEnabled"
          :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab"
          type="value"
          title="完成值"
          :data="progress.value.slice(0, 8)"
        />
        <rankling-list
          :judgeViewEnabled="judgeViewEnabled"
          :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab"
          type="ratio"
          title="达成率"
          :data="progress.index.slice(0, 8)"
        />
      </template>

      <template v-if="rankTab == 'retrogress'">
        <rankling-list
          :judgeViewEnabled="judgeViewEnabled"
          :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab"
          type="value"
          title="完成值"
          :data="retrogress.value.slice(0, 8)"
        />
        <rankling-list
          :judgeViewEnabled="judgeViewEnabled"
          :judgeDetailsEnabled="judgeDetailsEnabled"
          :rankTab="rankTab"
          type="ratio"
          title="达成率"
          :data="retrogress.index.slice(0, 8)"
        />
      </template>
      
    </div>
    <van-popup closeable v-model="tableVisible" position="center" :style="{ width: '88%',padding: '0.5rem',borderRadius: '5px',maxHeight: '80vh',overflow: 'auto'}" @close="tableVisible = false">
      <h2 :style="{color: '#222222',fontSize: '0.32rem', fontWeight: '600', marginBottom: '0.2rem'}">参与排名的二级企业名单</h2>
      <div class="company-list-table" :class="rankTab == 'progress' ? 'company-list-table-progress' : 'company-list-table-retrogress'">
        <div class="company-list-table-header">
          <div class="company-list-table-row">
            <div class="company-list-table-col1">序号</div>
            <div class="company-list-table-col2">二级企业名称</div>
          </div>
        </div>
        <div class="company-list-table-body" :key="tgt_no + rankTab">
          <div v-for="item in sortList" :key="item.index" class="company-list-table-row">
            <div class="company-list-table-col1">{{ item.index }}</div>
            <div class="company-list-table-col2">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import dayjs from "dayjs"
import { Decimal } from 'decimal.js'

import { getKbData } from "@/http/api";
import itemTip from "@/components/itemTip.vue";
import { formatNum } from "@/utils"

import RanklingList from "./RankingList.vue";
export default {
  name: "indicatorRanking",
  components: {
    RanklingList,
    itemTip,
  },
  props: {
    companyInfoList: Array,
    rankTab: String,
    dt: String,
    userId: String
  },
  data() {
    return {
      tgt_no: "A02",
      tableVisible: false,
      indexList: [
        {
          label: "利润总额",
          value: "A02",
        },
        {
          label: "营业收入",
          value: "A01",
        },
      ],
      progress: {
        value: [],
        index: []
      },
      retrogress: {
        value: [],
        index: []
      },
      sortList: []
    };
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.updateData()
      this.updateCompanySortList()
    },
    judgeViewEnabled(item, value) {
      const {
        entp_no: entpNo,
        cpy_type
      } = item
      let self = []

      this.companyInfoList.forEach(item => {
        // 找出自己所在企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push(item)
        }
      })

      /**
       * 允许查看数据
       * 1. 自己所在企业
       * 2. 非上市公司
       * 3. 全集团
       */
      if (
        (self && self.some(item => item.entpNo == entpNo)) ||
        (cpy_type == 1) ||
        (self.some(item => item.cpyTag == '01'))
      ) {
        return value
      }

      return '***'
    },
    judgeDetailsEnabled(entpNo) {
      let self = []

      this.companyInfoList.forEach(item => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push(item)
        }
      })

      /**
       * 允许跳转详情页
       * 1. 是自己所在企业
       * 2. 自己是全集团
       */
      if (self.some(item => item.entpNo === entpNo) || self.some(item => item.cpyTag == '01')) return true

      return false
    },
    updateCompanySortList() {
      const params = {
        dt: this.dt,
        tgt_no: this.tgt_no,
        moreChooseType: "2",
        mapperInterface: ["rbcrgl"],
      }

      getKbData(params)
        .then((res) => {
          if(res.code == 200 && res.data.rbcrgl) {
            this.sortList = res.data.rbcrgl.map(item => ({
              name: item.entp_nm,
              index: item.seq_no
            }))
          }
        })
    },
    updateData(newParams = {}) {
      const params = {
        dt: this.dt,
        mapperInterface: ["rbczbpm"],
        moreChooseType: "1",
        tgt_no: this.tgt_no,
        ...newParams
      }

      // 完成值数据
      getKbData({
        ...params,
        dsp_typ_cd: "1"
      }).then(res => {
        if(res.code == 200 && res.data.rbczbpm) {
          const data = res.data.rbczbpm.map(item => {
            const value = formatNum(item.dsp_val)

            return {
              ...item,
              name: item.entp_nm,
              value: value
            }
          });
          this.retrogress.value = data
          this.progress.value = [...data].reverse()
        }
        // 达成率数据
        getKbData({
          ...params,
          dsp_typ_cd: "2"
        }).then(res => {
          if(res.code == 200 && res.data.rbczbpm) {
            const ratioData = res.data.rbczbpm.map(item => ({
              ...item,
              dsp_val: formatNum(new Decimal(item.dsp_val).mul(new Decimal(100)), 1),
            }))

            // 达成目标
            getKbData({
              ...params,
              dsp_typ_cd: "3"
            }).then(res => {
              if(res.code == 200 && res.data.rbczbpm) {
                const data = ratioData.map(ratioItem => {
                  const value = ratioItem.dsp_val
                  const target = res.data.rbczbpm.find(item => item.entp_nm == ratioItem.entp_nm)

                  return {
                    ...ratioItem,
                    name: ratioItem.entp_nm,
                    value: value,
                    addValue: target ? formatNum(target.dsp_val) : '-'
                  }
                });
                this.retrogress.index = data
                this.progress.index = [...data].reverse()
              }
            })
          }
        })

      })
    },
    handleTabChange(tab) {
      this.$emit('rankTabChange', tab)
    },
    handleIndexChange(index) {
      this.tgt_no = index;
      this.updateData()
      this.updateCompanySortList()
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem 0.3rem;
  padding-top: 3.72rem;

  &.progress-container {
    .tab-inner {
      color: #AC3333;
      color: rgba(172,51,51,.9);
      background-image: url(../../../assets/img/ranking/progress_header.png);

      .actived-tab {
        color: #FFF9A8;
      }
    }

    .index-item {
      color: #000;
      background-color: #fff;
      border: 0.04rem solid #eaeaea;
      box-shadow: 0px 0px 4px rgba(74,103,157,.16);

      &.index-item-actived {
        background-color: #fb5c5e;
        color: #fff;
        border: 0.04rem solid #fb5c5e;
        box-shadow: 0px 2px 4px rgba(156,156,156,.5);
      }
    }
  }

  &.retrogress-container {
    .tab-inner {
      color: #FFFAB8;
      color: rgb(255,250,184);
      background-image: url(../../../assets/img/ranking/retrogress_header.png);

      .actived-tab {
        color: #6A5300;
      }
    }

    .index-item {
      color: #000;
      background-color: #fff;
      border: 0.04rem solid rgba(239,182,83,1);

      &.index-item-actived {
        background-color: #443c3c;
        color: #fff;
        border: 0.04rem solid rgba(239,182,83,1);
      }
    }
  }
}

.tab {
  box-sizing: border-box;
  position: sticky;
  top: -0.06rem;
  z-index: 1000;

  .tab-inner {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    font-size: 0.37rem;
    height: 1.23rem;
    padding: 0.27rem 0.25rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    font-weight: 700;
  }

  // ::after {
  //   content: ' ';
  //   background-color: #fff;
  //   height: 0.4rem;
  //   position: absolute;
  //   top: calc(100% - 0.25rem);
  //   left: 0;
  //   right: 0;
  //   margin: 0 0.3rem;
  // }
}

.content {
  box-shadow: 0px 0px 10px #ccc;
  margin: 0 0.3rem;
  padding: 0.2rem 0.2rem;
  background-color: #fff;
  margin-top: -0.25rem;
  border-bottom-left-radius: 0.1rem;
  border-bottom-right-radius: 0.1rem;

  .index-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .index-item {
      padding: 0.14rem 0.19rem;
      border-radius: 0.08rem;
      font-size: 0.23rem;

      &.index-item-actived {
        font-weight: 700;
      }
    }
  }

  .desc {
    color: #666;
    margin-top: 0.2rem;
    display: flex;
    align-items: center;
    font-size: 0.19rem;

    img {
      width: 0.3rem;
      height: 0.25rem;
      margin-left: 0.1rem;
      margin-top: 0.02rem;
    }
  }
}

::v-deep .company-list-table {
  border: 1px solid #e1e1e1;

  &.company-list-table-progress {
    .company-list-table-header {
      background-color: #ffebec;
    }
  }

  &.company-list-table-retrogress {
    .company-list-table-header {
      background-color: #fce6c6;
    }
  }

  .company-list-table-header {

    .company-list-table-row {
      color: #666;
      font-size: 0.25rem;
      border-color: transparent;
    }
  }

  .company-list-table-body {
    height: 6rem;
    overflow: auto;

    .company-list-table-row:last-child {
      border-color: transparent;
    }
  }

  .company-list-table-row {
    width: 100%;
    min-height: 0.7rem;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    align-items: center;
    font-size: 0.31rem;

    .company-list-table-col1 {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 1.2rem;
    }
    .company-list-table-col2 {
      display: flex;
      justify-content: center;
      align-items: center;
      // min-width: 3rem;
      flex: 1;
    }
  }
}
</style>
