<template>
  <div class="groupAll">
    <div class="content" :class="[isDrop ? 'content_open' : '']">
      <van-dropdown-menu>
        <van-dropdown-item v-model="tgt_no" ref="item" :options="indicatorTypeoOption" @open="onOpen" @close="onClose"/>
        <van-dropdown-item title-class="search_placeholder">
          <template slot="title">
            <div @click.stop></div>
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
      <div class="search_type">
        <span class="reset" :class="[dsp_typ_cd =='1' ? 'active' : '']" @click.stop="onDataTypeChange('1')">完成值</span>
        <span class="look" :class="[dsp_typ_cd =='2' ? 'active' : '']" @click.stop="onDataTypeChange('2')">达成率</span>
      </div>
      <img class="dcl_desc tipBtn" v-show="dsp_typ_cd =='2'" @click="dclDescShow=true" src="@/assets/img/tip1.png" alt="">
    </div>
    <ul class="ranke_content" :style="{padding: '0 0.35rem'}">
      <rank-chart :data="chartData" :type="dsp_typ_cd" :unit="unit" @jumpPage="jumpPage"/>
    </ul>
    <item-tip :type="'card'" :show="dclDescShow" @handleClose="dclDescShow=false" :title="'达成率计算说明'" :content="dclDescText" :showCircle="true"/>
  </div>
</template>

<script>
import { getKbData } from "@/http/api";
import rankChart from "./rankChart";
import itemTip from '@/components/itemTip.vue';
export default {
  name: 'indicatorRanking',
  components: {
    rankChart,
    itemTip
  },
  props: {
    dt: {
      type: String,
      default: ""
    }
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      }
    },
    dsp_typ_cd: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      } 
    },
    tgt_no: {
      deep: true,
      handler(newVal, oldVal) {
        this.initData()
      } 
    }
  },
  computed: {
    unit() {
      if(this.dsp_typ_cd == '2') return '%'
      let unitVal = '';
      this.indicatorTypeoOption.forEach(item => {
        if(item.value == this.tgt_no) {
          unitVal = item.unit;
        }
      })
      return unitVal
    }
  },
  data() {
    return {
      dclDescShow: false,
      dclDescText: [
        '如任务>0，达成率公式=当期/任务',
        '任务<0，达成率公式=2-当期/任务',
        '任务=0且当期≥0，达成率=100%',
        '任务=0且当期<0，达成率=-100%',
        '当达成率>999%，显示999⊕%',
        '当达成率<-999%，显示-999⊕%'
      ],
      isDrop: false,
      dsp_typ_cd: '1',
      tgt_no: '',
      indicatorTypeoOption: [
        { text: '营业收入', value: 'A01', unit: '亿元' },
        { text: '利润总额', value: 'A02', unit: '亿元' },
        { text: '净利润', value: 'A03' , unit: '亿元'},
        { text: '归母净利润', value: 'A04', unit: '亿元' },
        { text: '营业现金比率', value: 'A06' , unit: '%'},
        { text: '资产负债率', value: 'A08', unit: '%' },
        { text: '净资产收益率', value: 'A05', unit: '%' },
        { text: '研发经费投入强度', value: 'A09', unit: '%' },
        { text: '全员劳动生产率', value: 'A07', unit: '万元/人' }
      ],
      chartData: []
    }
  },
  created() {
    if(sessionStorage.getItem('secondPageVal')) {
      this.tgt_no = sessionStorage.getItem('secondPageVal');
      setTimeout(() => {
        sessionStorage.removeItem('secondPageVal')
      },0)
    } else {
      this.tgt_no = "A01"
    }
  },
  mounted() {

  },
  methods: {
    // 初始化页面
    initData() {
      if(!this.dt) return false;
      let params = {
        dt: this.dt,
        mapperInterface: ["zbpm"],
        moreChooseType: '1',
        tgt_no: this.tgt_no,
        dsp_typ_cd: this.dsp_typ_cd
      }
      getKbData(params).then(res => {
        if(res.code == 200) {
          this.chartData = res.data.zbpm;
        }
      })
    },
    onOpen() {
      this.isDrop = true;
    },
    onClose() {
      setTimeout(() => {
        this.isDrop = false;
      },200)
    },
    onDataTypeChange(val){
      this.dsp_typ_cd = val;
    },
    jumpPage(val) {
      sessionStorage.setItem('thridPageVal',val)
      this.$emit('toPage',2)
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  ul {
    li {
      background: #ffffff;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.08rem;
      overflow: hidden;
      margin-bottom: 0.35rem;
      //   height: 2.33rem;
      .box {
        display: flex;
        text-align: center;
        .commanBox {
          display: flex;
          flex-direction: column; //纵向排列
          justify-content: center;
          padding: 0.3rem 0;
          width: 1.25rem;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(113, 115, 122, 1);
          }
          p:last-child {
            font-size: 0.2rem;
            color: rgba(34, 34, 34, 1);
          }
          h3 {
            font-size: 0.3rem;
            color: rgba(34, 34, 34, 1);
            font-weight: 400;
            padding: 0.3rem 0;
          }
        }
        .one {
          width: 1.9rem;
          background-color: rgba(224, 243, 255, 1);
          padding: 0.3rem 0.1rem;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(34, 34, 34, 1);
          }
          p:last-child {
            font-size: 0.26rem;
            color: rgba(0, 138, 230, 1);
            font-weight: 400;
          }
          h3 {
            font-size: 0.5rem !important;
            color: rgba(0, 138, 230, 1);
            font-weight: 600;
            padding: 0.15rem 0;
          }
        }
        .three {
          h3 {
            color: rgba(50, 205, 145, 1);
          }
        }
        .two,
        .three,
        .four {
          position: relative;
        }
        .two::after,
        .three::after,
        .four::after {
          content: '';
          position: absolute;
          border-left: 1px dashed #999999;
          width: 1px;
          height: .66rem;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    li:last-child {
      margin-bottom: 0;
    }
  }
  .content .selected {
    background-color: #fff;
    border: 1px solid #3e7bfa;
    color: #3e7bfa;
  }
  .content ul {
    overflow: hidden;
    flex: 1;
    overflow-y: scroll;
    li {
      width: 2.1rem;
      height: 0.72rem;
      text-align: center;
      // line-height: 0.72rem;
      background: #eeeeee;
      font-size: 0.32rem;
      display: flex;
      float: left;
      justify-content: center;
      align-items: center;
      border-radius: 36px;
      margin-left: 0.28rem;
      margin-bottom: 0.3rem;
      border: 1px solid #eeeeee;
      &:nth-child(3n) {
        // margin-left: 0;
      }
      span {
        padding: 0.14rem 0;
      }
    }
    .moreTextStyle {
      // line-height: 0.4rem;
      // font-size: 0.24rem;
      transform: scale(0.7);
    }
  }
  .content {
    position: relative;
    padding: 0.2rem 0.35rem 0;
    .dcl_desc.tipBtn {
      position: absolute;
      right: 0.3rem;
      top: 0.2rem;
      width: 0.4rem;
      height: 0.4rem;
    }
    ::v-deep.van-dropdown-item--down {
      padding: 0 0.35rem;
      display: block!important;
    }
    ::v-deep .van-dropdown-item__content {
      max-height: 90%;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      position: relative;
      display: flex;
      flex-direction: column;
      .van-dropdown-item__option--active {
        color: #6495fb;
      }
      .van-cell__value {
        display: none;
      }
    }
    ::v-deep .van-dropdown-menu__bar {
      box-shadow: unset;
      margin-top: 2px;
      .van-dropdown-menu__item {
        justify-content: flex-start;
        font-weight: 1000;
      }
      .van-dropdown-menu__title {
        margin: 0 0.35rem;
        padding: unset;
        font-size: 0.3rem;
      }
      .van-dropdown-menu__title--active {
        color: #6495fb;
      }
      .van-dropdown-menu__title::after {
        right: -20px;
        border-width: 5px;
        margin-top: -7px;
        border-color: transparent transparent #000 #000;
      }
      .van-dropdown-menu__title--active::after {
        right: -20px;
        border-width: 5px;
        margin-top: 0;
        border-color: transparent transparent currentColor currentColor;
      }
      .van-dropdown-menu__item:nth-of-type(2) {
        justify-content: flex-end;
        flex: 1;
      }
      .search_placeholder {
        width: 100%;
        height: 100%;
        div {
          width: 100%;
          height: 100%;
        }
      }
      .search_placeholder.van-dropdown-menu__title::after {
        display: none;
      }
    }

    .search_type {
      position: absolute;
      top: calc(50% + 0.2rem/2);
      right: 0.7rem;
      transform: translate(0px, -50%);
      z-index: 99;
      span {
        font-size: 0.23rem;
        font-weight: 600;
      }
      .reset {
        margin-right: 8px;
      }
      .reset,.look {
        display: inline-block;
        padding: 4px 12px;
        background: rgba(255,255,255,0.1);
        box-shadow: 0px 4px 8px 0px rgba(66,122,225,0.12), 0px 0px 4px 0px rgba(66,122,225,0.08);
        border-radius: 6px;
        border: 1px solid #FFFFFF;
      }
      .active {
        background: #3e7bfa;
        color: #fff;
        box-shadow: unset;
      }
    }
  }

  .content_open {
    padding: 0.2rem 0 0;
    .van-dropdown-item--down {
      padding: unset;
    }
    .search_type {
      right: 0.35rem;
    }
  }
}

.ranke_content>div {
  background: #fff;
}
.ranke_content>div::before {
  content: '';
  display: block;
  // width: calc(100% - 0.7rem);
  // transform: translateX(0.35rem);
  width: 100%;
  height: 1px;
  background: #CCCCCC;
}
</style>
