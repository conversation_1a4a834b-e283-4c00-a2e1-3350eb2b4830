<template>
  <div class="groupAll">
    <div class="content">
      <div class="header">
        <div class="header-top">
          <div class="header-top-left">
            <van-dropdown-menu>
              <van-dropdown-item :title="currentName" ref="dropDwonRef">
                <ul>
                  <li
                    v-for="item in companyList"
                    :key="item.entp_no"
                    :label="item.entp_nm"
                    :value="item.entp_no"
                    :class="{
                      selected: item.entp_no == entp_no,
                      disabled:
                        selfInfo.every(item => !item.isAll) && selfInfo.every(selfItem => selfItem.entpNo !== item.entp_no),
                    }"
                    @click="toggleSelect(item.entp_no)"
                  >
                    <span
                      :class="
                        item.entp_nm && item.entp_nm.length > 5
                          ? item.entp_nm.length > 7
                            ? 'size-7'
                            : 'size-5'
                          : ''
                      "
                    >{{ item.entp_nm }}</span>
                  </li>
                </ul>
              </van-dropdown-item>
            </van-dropdown-menu>
            <p class="title-remark" :style="{opacity: isShowRemark.show ? 1 : 0}">{{ isShowRemark.text }}</p>
            <div v-if="!isMetaCenter">
              <div class="title">营业收入</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yysr.dq || '-') }}</span>
                <span class="value-unit">{{ yysr.dq ? '亿元' : ''}}</span>
              </div>
            </div>
            <div v-else>
              <div class="title">交易规模</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yqjjyzx.trd_scl || '-') }}</span>
                <span class="value-unit">{{ yqjjyzx.trd_scl ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">集采业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.ctr_pur || '-')
                }}</span>
                <span class="other-value-unit">{{ yqjjyzx.ctr_pur ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">自助撮合业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.sef_mat_busi || '-')
                }}</span>
                <span class="other-value-unit">{{ yqjjyzx.sef_mat_busi ? '亿元' : '' }}</span>
              </div>
            </div>
          </div>

          <img
            src="../../../assets/img/ranking/second_top_icon.png"
            class="header-top-right"
            :class="isMetaCenter ? 'header-top-right-meta' : ''"
          />
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span
              :class="
              yysr.tb ? (
                formatNum(yysr.tb) > 0
                  ? 'other-value-up'
                  : 'other-value-down'
              ) : 'other-value'
              "
              >{{ formatNum(yysr.tb || '-', undefined, "ratio") }}</span
            >
            <span class="other-value-unit">{{ yysr.tb ? '%' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(yysr.rw || '-') }}</span>
            <span class="other-value-unit">{{ yysr.rw ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="bottom-title">
          <div class="title">利润总额</div>
          <div class="value">
            <span class="value-text">{{ formatNum(lrze.dq || '-') }}</span>
            <span class="value-unit">{{ lrze.dq ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span
              :class="
              lrze.tb ? (
                formatNum(lrze.tb) > 0
                  ? 'other-value-up'
                  : 'other-value-down'
              ) : 'other-value'
              "
              >{{ lrze.dq !== undefined ? formatNum(lrze.dq - lrze.tq, undefined, "ratio") : '-' }}</span
            >
            <span class="other-value-unit">{{ lrze.dq !== undefined ? '亿元' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(lrze.rw || '-') }}</span>
            <span class="other-value-unit">{{ lrze.rw ? '亿元' : '' }}</span>
          </div>
        </div>
      </div>

      <div v-if="!isMetaCenter && indexList.length > 0" class="index-card">
        <div class="index-item" v-for="(item, index) in indexList" :key="index">
          <div>
            <!-- <img :src="item.icon" alt="" /> -->
            <span class="index-item-name">{{ item.name }}</span>
          </div>
          <div class="index-item-tail">
            <span class="index-item-value">{{
              item.value == undefined ? '-' : formatNum(item.value, undefined, item.type)
            }}</span>
            <span class="index-item-unit" :style="item.unitStyle">{{ item.value == undefined ? '' : item.unit }}</span>
          </div>
        </div>
      </div>

      <div class="task-card">
        <div class="title">重点工作任务</div>
        <div v-if="taskList.length === 0 && !isMetaCenter" class="task-no-data">
          <img
            src="../../../assets/img/ranking/second_task_no_data.png"
            alt=""
          />
          <span>集团未下达企业年度重点工作任务</span>
        </div>
        <div
          v-else-if="taskList.length === 0 && isMetaCenter"
          class="task-no-data-meta-center"
        >
          <img
            src="../../../assets/img/ranking/second_task_no_data.png"
            alt=""
          />
          <span>集团未下达企业年度重点工作任务</span>
        </div>
        <div
          v-else
          class="task-item"
          v-for="(item, index) in taskList"
          :key="index"
        >
          <div class="index">{{ index + 1 }}</div>
          <div class="content">
            <span class="name">{{ item.task_name ? (
              item.task_name.length > 11 ? 
                item.task_name.slice(0, 11) + '...' : item.task_name
            ) : '' }}</span>
            <div class="bottom">
              <ProgressBar :value="Number(item.task_pct)" :disabled="item.task_name == '强军首责' && entp_no == '99007'" />
              <div
                class="btn"
                @click="handleViewDetails(item)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '85%' }"
      class="modal"
    >
      <div :class="{ tab1: activeTab == 1, tab2: activeTab == 2 }">
        <div class="tab-left" @click="activeTab = 1" />
        <div class="tab-right" @click="activeTab = 2" />
      </div>

      <!-- <h2>{{ taskDetails.task_name }}</h2> -->
      <div
        v-if="activeTab == 1 ? taskDetails.task_desp : taskDetails.task_detail"
        :key="activeTab"
        class="modal-content"
        v-html="activeTab == 1 ? taskDetails.task_desp : taskDetails.task_detail"
      />
      <div class="modal-no-data" v-else>
        <img src="../../../assets/img/ranking/second_task_no_data.png" alt="" />
        <span>暂无任务进展情况</span>
      </div>

      <div class="close-btn">
        <div class="close-btn-inner" @click="show = false">关闭</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Icon1 from "@/assets/img/ranking/second_icon_净资产收益率（年化）.png";
import Icon2 from "@/assets/img/ranking/second_icon_全员劳动生产率（年化）.png";
import Icon3 from "@/assets/img/ranking/second_icon_研发经费投入强度.png";
import Icon4 from "@/assets/img/ranking/second_icon_营业现金比率.png";
import Icon5 from "@/assets/img/ranking/second_icon_资产负债率.png";

import { getKbData } from "@/http/api";
import { formatNum } from "@/utils";
import ProgressBar from "@/components/ProgressBar/index.vue";

export default {
  name: "secondUnit",
  components: {
    ProgressBar,
  },
  props: {
    dt: {
      type: String,
      default: "",
    },
    userId: String,
    companyInfoList: Array,
  },
  watch: {
    entp_no: {
      handler() {
        this.$nextTick(() => {
          this.isMetaCenter = this.entp_no == "99041";
          this.updateData();
        });
      },
    },
    userId: {
      handler() {
        this.initSelfInfo();
      },
    },
    companyInfoList: {
      handler() {
        this.initSelfInfo();
      },
    },
    show: {
      handler() {
        // 弹窗关闭恢复 tab 到任务内容
        if (!this.show) {
          setTimeout(() => {
            this.activeTab = 1
          }, 500)
        }
      }
    }
  },
  data() {
    return {
      selfInfo: [],
      isMetaCenter: false,
      activeTab: 1,
      show: false,
      entp_no: "",
      companyList: [],
      defaultIndexList: [
        {
          icon: Icon1,
          key: "csh_rate",
          name: "营业现金比率",
          value: "",
          unit: "%",
        },
        {
          icon: Icon2,
          key: "dbas_rate",
          name: "资产负债率",
          value: "",
          unit: "%",
        },
        {
          icon: Icon3,
          key: "netas_rate",
          name: "净资产收益率（年化）",
          value: "",
          unit: "%",
        },
        {
          icon: Icon4,
          key: "rdeivt_deg",
          name: "研发经费投入强度",
          value: "",
          unit: "%",
        },
        {
          icon: Icon5,
          key: "tlp_rate",
          name: "全员劳动生产率（年化）",
          value: "",
          unit: "万元/人",
          unitStyle: {
            minWidth: '1rem'
          }
        },
      ],
      indexList: [],
      taskDetails: {},
      taskList: [],
      yysr: {},
      lrze: {},
      fiverate: {},
      yqjjyzx: {},
    };
  },
  computed: {
    currentName() {
      const target = this.companyList.find(
        (item) => item.entp_no == this.entp_no
      );
      return target ? target.entp_nm : "";
    },
    isShowRemark() {
      // 允许展示注释的企业编号
      const list = ['99001', '99015', '99043']
      // desc
      const descList = ['99005', '99011']
      let text = ''
      if (list.includes(this.entp_no)) {
        text = '管理口径'
      }else if (descList.includes(this.entp_no)) {
        const map = {
          99011: '不含彩虹股份投资收益',
          99005: '不含新洁能股票投资收益'
        }
        text = `注：${map[this.entp_no]}`
      }

      return {
        show: Boolean(text),
        text
      }
    },
  },
  created() {
    this.initCompanyList();
  },
  methods: {
    formatNum,
    // 初始化页面
    updateData() {
      // 每次切换先重置数据
      this.yysr = {};
      this.lrze = {};
      this.indexList = [];
      this.yqjjyzx = {};
      this.taskList = [];

      if (!this.isMetaCenter) {
        // 营业收入
        getKbData({
          dt: this.dt,
          entp_no: this.entp_no,
          mapperInterface: ["yysr"],
          moreChooseType: "2",
        })
          .then((res) => {
            res.data.yysr && (this.yysr = res.data.yysr[0] || {});
          })
        // 利润总额
        getKbData({
          dt: this.dt,
          entp_no: this.entp_no,
          mapperInterface: ["lrze"],
          moreChooseType: "2",
        })
          .then((res) => {
            res.data.lrze && (this.lrze = res.data.lrze[0] || {});
          })
        // 五个比率
        getKbData({
          dt: this.dt,
          entp_no: this.entp_no,
          mapperInterface: ["fiverate"],
          moreChooseType: "2",
        })
          .then((res) => {
            if (res.data.fiverate) {
              this.indexList = res.data.fiverate[0]
                ? this.defaultIndexList.map((item) => ({
                    ...item,
                    value: res.data.fiverate[0][item.key],
                  }))
                : [];
            }
          })
      } else {
        // 元器件中心数据
        getKbData({
          dt: this.dt,
          entp_no: this.entp_no,
          mapperInterface: ["yqjjyzx"],
          moreChooseType: "2",
        })
          .then((res) => {
            res.data.yqjjyzx && (this.yqjjyzx = res.data.yqjjyzx[0] || {});
          })
      }
      // 重要任务
      getKbData({
        dt: this.dt,
        entp_no: this.entp_no,
        mapperInterface: ["maintask"],
        moreChooseType: "2",
      })
        .then((res) => {
          this.taskList = res.data.maintask
            ? res.data.maintask.map((item) => ({
                ...item,
                task_pct:
                  item.task_pct !== undefined
                    ? Math.round(item.task_pct) / 100
                    : "-",
              }))
            : [];
        })
    },
    toggleSelect(entp_no) {
      // 如果非集团整体且不是自己所在企业则不能看数据
      if (this.selfInfo.every(item => !item.isAll) && this.selfInfo.every(item => item.entpNo !== entp_no)) return;

      this.entp_no = entp_no;
      this.$refs.dropDwonRef.toggle();
    },
    initSelfInfo() {
      if (!this.userId || !this.companyInfoList) return;

      this.companyInfoList.forEach((item) => {
        if (this.userId && item.userIds && item.userIds.includes(this.userId)) {
          this.selfInfo.push({
            ...item,
            isAll: item.cpyTag == "01",
          });
        }
      });

      this.initDefault();
    },
    initDefault() {
      // 优先使用路由传入
      if (this.$route.query.currentCompany) {
        this.entp_no = this.$route.query.currentCompany;
      } else {
        const selfFisrt = this.selfInfo[0] ? this.selfInfo[0].entpNo : ''
        // 集团整体默认展示第一个，其他二级企业默认展示自己所在企业
        this.entp_no = this.selfInfo.some(item => item.isAll)
          ? this.companyList[0].entp_no
          : selfFisrt;
      }
    },
    initCompanyList() {
      getKbData({
        dt: this.dt,
        dis_scp_no: "2",
        mapperInterface: ["scdcom"],
        moreChooseType: "2",
      }).then((res) => {
        // 拿到二级企业列表，初始化首选的二级企业
        this.companyList = res.data.scdcom;
        this.initSelfInfo();
      });
    },
    handleViewDetails(item) {
      const task_desp = item.task_desp ? `<p class='task-details-title'>${item.task_name}</p>${item.task_desp}` : ''
      const task_detail = item.task_detail ? `<p class='task-details-title'>${item.task_name}</p>${item.task_detail}` : ''
      this.taskDetails = {
        ...item,
        task_desp,
        task_detail
      }
      this.show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.groupAll {
  // background-color: red;
  padding-bottom: 0.35rem;
  ul {
    padding: 0 0 0.35rem;
    li {
      background: #ffffff;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.08rem;
      overflow: hidden;
      margin-bottom: 0.35rem;
      //   height: 2.33rem;
      .box {
        display: flex;
        text-align: center;
        .commanBox {
          display: flex;
          flex-direction: column; //纵向排列
          justify-content: center;
          padding: 0.3rem 0;
          width: 1.25rem;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(113, 115, 122, 1);
          }
          p:last-child {
            font-size: 0.2rem;
            color: rgba(34, 34, 34, 1);
          }
          h3 {
            font-size: 0.3rem;
            color: rgba(34, 34, 34, 1);
            font-weight: 400;
            padding: 0.3rem 0;
            i {
              position: relative;
            }
          }
        }
        .one {
          width: 1.9rem;
          background-color: rgba(224, 243, 255, 1);
          padding: 0.3rem 0.1rem;
          p:first-child {
            font-size: 0.26rem;
            color: rgba(34, 34, 34, 1);
          }
          p:last-child {
            font-size: 0.26rem;
            color: rgba(0, 138, 230, 1);
            font-weight: 400;
          }
          h3 {
            font-size: 0.42rem !important;
            color: rgba(0, 138, 230, 1);
            font-weight: 600;
            padding: 0.15rem 0;
          }
        }
        .two {
          width: 1.35rem;
        }
        .three {
          h3 {
            color: rgba(50, 205, 145, 1);
          }
        }
        .two,
        .three,
        .four {
          position: relative;
        }
        .two::after,
        .three::after,
        .four::after {
          content: "";
          position: absolute;
          border-left: 1px dashed #e1e1e1;
          width: 1px;
          height: 0.66rem;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    li:last-child {
      margin-bottom: 0;
    }
  }
  .content .selected {
    background-color: #fff;
    border: 1px solid #3e7bfa;
    color: #3e7bfa;
  }
  .content .disabled {
    color: #ccc;
  }
  .content ul {
    overflow: hidden;
    flex: 1;
    overflow-y: scroll;
    li {
      width: 2.1rem;
      height: 0.72rem;
      display: flex;
      justify-content: center;
      align-content: center;
      text-align: center;
      // line-height: 0.72rem;
      background: #eeeeee;
      font-size: 0.28rem;
      display: flex;
      float: left;
      justify-content: center;
      align-items: center;
      border-radius: 36px;
      margin-left: 0.28rem;
      margin-bottom: 0.3rem;
      border: 1px solid #eeeeee;
      &:nth-child(3n) {
        // margin-left: 0;
      }
      span {
        padding: 0.14rem 0;
      }
    }
    .size-5 {
      // line-height: 0.4rem;
      font-size: 0.2rem;
      // transform: scale(0.5);
    }

    .size-7 {
      font-size: 0.15rem;
    }
  }
  .content {
    .header {
      padding-top: calc(0.05rem + 45px);

      .header-top {
        display: flex;
        justify-content: space-between;
        position: relative;

        .header-top-left {
          display: flex;
          flex-direction: column;
          width: 3rem;

          .title-remark {
            width: 100vw;
            color: #999;
            font-size: 0.25rem;
            padding-left: 0.6rem;
            margin-top: 0.4rem;
            margin-bottom: -0.2rem;
            z-index: 100;
          }

          ::v-deep .van-dropdown-menu {
            position: fixed;
            top: calc(0.05rem + 45px + 5vh + env(safe-area-inset-top));
            z-index: 1000;
          }

          ::v-deep .van-popup.van-popup--top.van-dropdown-item__content {
            ul {
              padding: 10px 0;
            }
          }

          ::v-deep .van-dropdown-menu__title {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-top-right-radius: 2em;
            border-bottom-right-radius: 2em;
            padding-right: 1rem;
            box-shadow: 0px 4px 10px 2px rgba(66, 122, 225, 0.09);
            max-width: 5.6rem;
            height: 0.8rem;
            margin-top: 0.1rem;
            font-size: 0.3rem;

            &::after {
              right: 0.4rem;
            }
          }
        }

        .header-top-right {
          // width: calc(100% - 3rem);
          width: calc(100% - 3.4rem);
          right: 0;
          // bottom: -39%;
          top: -1rem;
          height: auto;
          position: absolute;
        }

        .header-top-right-meta {
          // top: -1.6rem;
        }
      }

      .title {
        font-weight: 700;
        font-size: 0.32rem;
        display: flex;
        align-items: center;
        padding-left: 0.36rem;
        margin-top: 0.4rem;
        margin-bottom: 0.2rem;

        &::before {
          content: " ";
          display: inline-block;
          width: 0.1rem;
          height: 0.1rem;
          border-radius: 100%;
          background-color: #000;
          margin-right: 0.15rem;
        }
      }

      .value {
        display: flex;
        align-items: center;
        font-size: 0.5rem;

        &.value-1 {
          padding-left: 0.6rem;
        }

        .value-text {
          font-weight: 700;
          font-size: 0.5rem;
        }

        .value-unit {
          color: #71737a;
          margin-left: 0.2rem;
          font-size: 0.26rem;
          min-width: 1rem;
          margin-top: 0.1rem;
        }
      }

      .other {
        width: 100vw;
        font-size: 0.3rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.3rem;
        padding: 0 0.6rem;
        box-sizing: border-box;
        gap: 0.2rem;
      }

      .other-item {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .label {
          display: inline-block;
          color: #71737a;
          font-size: 0.2rem;
        }

        .other-value {
          font-size: 0.32rem;
          padding: 0;
          margin-right: 0.2rem;
          margin-left: 0.2rem;
          min-width: 1.2rem;
          text-align: right;
          margin-top: -0.05rem;
        }

        .other-value-unit {
          display: inline-block;
          font-size: 0.2rem;
          font-size: 0.2rem;
        }

        .other-value-up {
          @extend .other-value;
          color: #e60012;
        }

        .other-value-down {
          @extend .other-value;
          color: #32cd91;
        }

        &:first-child {
          width: 45%;

          .label {
            min-width: 0.6rem;
          }

          .other-value-unit {
            width: 0.6rem;
          }
        }

        &:last-child {
          width: 55%;

          .label {
            min-width: 1.2rem;
          }

          .other-value-unit {
            min-width: 0.8rem;
          }
        }
      }

      .other-item-vertical {
        @extend .other-item;
        padding-left: 0.6rem;

        width: 100%;

        .label {
          min-width: 1.8rem !important;
        }

        .other-value-unit {
          min-width: 1rem !important;
        }
      }

      .bottom-title {
        display: flex;
        align-items: center;
        margin-top: 0.4rem;
        // margin-bottom: 0.2rem;

        .title {
          margin-right: 0.2rem;
          display: flex;
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }

    .index-card {
      margin: 0.36rem;
      background-color: #fff;
      border-radius: 0.16rem;
      box-shadow: 5px 6px 20px 0 rgba(74, 103, 157, 0.1);
      padding: 0.12rem 0.3rem;

      .index-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px dashed #e1e1e1;
        padding: 0.18rem 0;

        &:last-child {
          border-color: transparent;
        }

        img {
          margin-right: 0.15rem;
          width: 0.6rem;
          height: 0.6rem;
        }

        div {
          display: flex;
          align-items: center;
        }

        .index-item-name {
          max-width: 3.5rem;
          font-size: 0.3rem;
          color: #181c26;
        }

        .index-item-tail {
          // width: 2.6rem;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }

        .index-item-value {
          font-size: 0.4rem;
          font-weight: 700;
        }

        .index-item-unit {
          display: flex;
          justify-content: flex-end;
          // min-width: 1rem;
          margin-left: 0.18rem;
          font-size: 0.2rem;
          display: flex;
          margin-top: 0.05rem;
        }
      }
    }

    .task-card {
      padding: 0.4rem 0.3rem;
      font-size: 0.32rem;
      margin: 0.4rem 0.36rem;
      box-shadow: 5px 6px 20px 0 rgba(74, 103, 157, 0.1);
      background: url(../../../assets/img/ranking/second_task_bg.png);
      background-size: cover;
      border-radius: 0.16rem;

      .title {
        font-weight: 700;
        color: #4481f6;
        margin-bottom: 0.4rem;
      }

      .task-item {
        display: flex;
        margin-bottom: 0.24rem;

        .index {
          width: 0.2rem;
          margin-right: 0.22rem;
        }

        .content {
          display: flex;
          flex-direction: column;
          max-width: calc(100% - 0.42rem);
          min-width: calc(100% - 0.42rem);
          flex: 1;

          .name {
            // margin-bottom: 0.2rem;
          }

          .bottom {
            display: flex;
            align-items: center;
          }

          .btn {
            background: url(../../../assets/img/ranking/second_details_btn.png);
            width: 0.8rem;
            height: 0.8rem;
            line-height: 0.8rem;
            padding-left: 0.1rem;
            padding-top: 0.01rem;
            font-size: 0.2rem;
            color: #244db3;
            background-size: contain;
            background-repeat: no-repeat;
            margin-left: 0.3rem;

            span {
              // position: absolute;
              // top: -0.03rem;
              // left: 0.05rem;
            }
          }
          .progress-bar {
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .task-no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        img {
          margin-top: 0.4rem;
          margin-bottom: 0.7rem;
          width: 2.4rem;
          height: auto;
        }

        span {
          font-size: 0.28rem;
        }
      }

      .task-no-data-meta-center {
        @extend .task-no-data;
        height: 6.5rem;
        justify-content: center;

        img {
          margin-top: -0.5rem;
          margin-bottom: 1.2rem;
        }
      }
    }

    ::v-deep .van-dropdown-item__content {
      max-height: 90%;
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
      position: relative;
      display: flex;
      flex-direction: column;
      .btnBox {
      }
    }

    ::v-deep .van-dropdown-menu__bar {
      box-shadow: unset;
      margin-top: 2px;
      background-color: transparent;

      .van-dropdown-menu__item {
        justify-content: flex-start;
        font-weight: 1000;
      }
      .van-dropdown-menu__title {
        padding: 0 0.35rem;
        font-size: 0.32rem;
      }
      .van-dropdown-menu__title--active {
        color: #6495fb;
      }
      .van-dropdown-menu__title::after {
        right: -8px;
        border-width: 5px;
        margin-top: -8px;
        border-color: transparent transparent #000 #000;
      }
      .van-dropdown-menu__title--active::after {
        right: -8px;
        border-width: 5px;
        margin-top: 0;
        border-color: transparent transparent currentColor currentColor;
      }
    }
    ::v-deep .van-dropdown-menu__bar:not(.van-dropdown-menu__bar--opened) {
      // background: #f6f7f9;
    }
  }
  .modal {
    border-top-right-radius: 0.35rem;
    border-top-left-radius: 0.35rem;



    .modal-no-data {
      height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 0.32rem;

      img {
        width: 2.6rem;
        height: auto;
        margin-bottom: 0.6rem;
      }
    }

    .tab {
      width: 100%;
      height: 1.12rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tab-left {
        width: 50%;
        height: 100%;
      }

      .tab-right {
        width: 50%;
        height: 100%;
      }
    }

    .tab1 {
      @extend .tab;
      background: url(../../../assets/img/ranking/second_modal_tab_1.png);
      background-size: cover;
    }

    .tab2 {
      @extend .tab;
      background: url(../../../assets/img/ranking/second_modal_tab_2.png);
      background-size: cover;
    }

    .modal-content {
      background-color: #e8f0fa;
      padding: 0.3rem;
      margin: 0.4rem 0.36rem;
      font-size: 0.34rem;
      max-height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
      overflow: auto;
      border-radius: 0.1rem;
      text-align: justify;

      ::v-deep .task-details-title {
        font-weight: 700;
        color: #4f9dfa;
        margin-bottom: 0.2rem;
      }
    }

    .close-btn {
      width: 100%;
      padding: 0 0.36rem;
      position: absolute;
      bottom: 1rem;

      .close-btn-inner {
        width: 100%;
        background-image: linear-gradient(90deg, #6baaff 0%, #3b7afd 100%);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 0.86rem;
        font-size: 0.34rem;
        border-radius: 4em;
      }
    }
  }
}
</style>
