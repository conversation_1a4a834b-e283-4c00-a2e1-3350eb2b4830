<template>
  <component :is="componentName" v-bind="$props" v-on="$listeners" />
</template>

<script>
import index2025 from './components/index2025.vue'
import mixin from './mixin'
export default {
  components: {
    index2025,
  },
  mixins: [mixin],
  data: {
    componentName: '',
  },
  watch: {
    dt: {
      immediate: true,
      handler(val) {
        if (val) {
          let  year = val.slice(0, 4)
          if ( Number(year) < 2025) {
            year = '2024'
          }
          this.componentName = `index${year}`
        }
      }
    }
  },
  destroyed(){
   this.$store.dispatch('commitCompanyClickSecond',null)
  }
}
</script>

<style src="./index.scss" lang="scss" scoped />
