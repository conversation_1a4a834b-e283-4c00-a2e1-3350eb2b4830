<template>
  <div class="groupAll">
    <div class="content">
      <div class="header">
        <div class="header-top">
          <div class="header-top-left">
            <van-dropdown-menu>
              <van-dropdown-item :title="currentName" ref="dropDwonRef">
                <ul>
                  <li v-for="item in companyList" :key="item.entp_no" :label="item.entp_nm" :value="item.entp_no"
                    :class="{
                      selected: item.entp_no == entp_no,
                      disabled:
                        selfInfo.every(item => !item.isAll) && selfInfo.every(selfItem => selfItem.entpNo !== item.entp_no),
                    }" @click="toggleSelect(item.entp_no)">
                    <span :class="item.entp_nm && item.entp_nm.length > 5
                      ? item.entp_nm.length > 7
                        ? 'size-7'
                        : 'size-5'
                      : ''
                      ">{{ item.entp_nm }}</span>
                  </li>
                </ul>
              </van-dropdown-item>
            </van-dropdown-menu>
            <p class="title-remark" :style="{ opacity: textTips ? 1 : 0 }">{{
              textTips }}</p>
            <div v-if="!isMetaCenter">
              <div class="title">营业收入</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yysr.dq || '-') }}</span>
                <span class="value-unit">{{ yysr.dq ? '亿元' : '' }}</span>
              </div>
            </div>
            <div v-else>
              <div class="title">交易规模</div>
              <div class="value value-1">
                <span class="value-text">{{ formatNum(yqjjyzx.trd_scl || '-') }}</span>
                <span class="value-unit">{{ yqjjyzx.trd_scl ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">集采业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.ctr_pur || '-')
                  }}</span>
                <span class="other-value-unit">{{ yqjjyzx.ctr_pur ? '亿元' : '' }}</span>
              </div>
              <div class="other-item-vertical">
                <span class="label">自助撮合业务</span>
                <span class="other-value">{{
                  formatNum(yqjjyzx.sef_mat_busi || '-')
                  }}</span>
                <span class="other-value-unit">{{ yqjjyzx.sef_mat_busi ? '亿元' : '' }}</span>
              </div>
            </div>
          </div>

          <img src="../../../../assets/img/ranking/second_top_icon.png" class="header-top-right"
            :class="isMetaCenter ? 'header-top-right-meta' : ''" />
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span :class="yysr.tb ? (
              yysr.tb > 0
                ? 'other-value-up'
                : 'other-value-down'
            ) : 'other-value'
              ">{{ formatNum(yysr.tb || '-', undefined, "ratio") }}</span>
            <span class="other-value-unit">{{ yysr.tb ? '%' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(yysr.rw || '-') }}</span>
            <span class="other-value-unit">{{ yysr.rw ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="bottom-title">
          <div class="title">利润总额</div>
          <div class="value">
            <span class="value-text">{{ formatNum(lrze.dq || '-') }}</span>
            <span class="value-unit">{{ lrze.dq ? '亿元' : '' }}</span>
          </div>
        </div>
        <div v-if="!isMetaCenter" class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span :class="lrze.tb ? (
              lrze.tb > 0
                ? 'other-value-up'
                : 'other-value-down'
            ) : 'other-value'
              ">{{ lrze.dq !== undefined ? formatNum1(lrze.dq - lrze.tq, undefined, "ratio") : '-' }}</span>
            <span class="other-value-unit">{{ lrze.dq !== undefined ? '亿元' : '' }}</span>
          </div>
          <div class="other-item">
            <span class="label">预算目标</span>
            <span class="other-value">{{ formatNum(lrze.rw || '-') }}</span>
            <span class="other-value-unit">{{ lrze.rw ? '亿元' : '' }}</span>
          </div>
        </div>
      </div>

      <div v-if="!isMetaCenter && indexList.length > 0" class="index-card">
        <div class="index-item" v-for="(item, index) in indexList" :key="index">
          <div>
            <!-- <img :src="item.icon" alt="" /> -->
            <span class="index-item-name">{{ item.name }}</span>
          </div>
          <div class="index-item-tail">
            <span class="index-item-value">{{
              item.value == undefined ? '-' : formatNum(item.value, undefined, item.type)
              }}</span>
            <span class="index-item-unit" :style="item.unitStyle">{{ item.value == undefined ? '' : item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mixin from '../mixin.js'
import { formatNum } from "@/utils";
import { getKbData, getIndexData } from "@/http/api";
export default {
  mixins: [mixin],
  data() {
    return {
      textTips: '',
      delopedOver: false
    }

  },
  methods: {
    formatNum1(data1, data2, data3) {
      let formattedValue = formatNum(data1, data2, data3);

      if (formattedValue.endsWith('0')) {
        formattedValue = formattedValue.slice(0, -1);
      }
      if (formattedValue.endsWith('.')) {
        formattedValue = formattedValue + "0"
      }
      return formattedValue;

    },
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
