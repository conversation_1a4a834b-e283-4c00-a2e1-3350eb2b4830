.groupAll {
  // background-color: red;
  padding-bottom: 0.35rem;

  ul {
    padding: 0 0 0.35rem;

    li {
      background: #ffffff;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.08rem;
      overflow: hidden;
      margin-bottom: 0.35rem;

      //   height: 2.33rem;
      .box {
        display: flex;
        text-align: center;

        .commanBox {
          display: flex;
          flex-direction: column; //纵向排列
          justify-content: center;
          padding: 0.3rem 0;
          width: 1.25rem;

          p:first-child {
            font-size: 0.26rem;
            color: rgba(113, 115, 122, 1);
          }

          p:last-child {
            font-size: 0.2rem;
            color: rgba(34, 34, 34, 1);
          }

          h3 {
            font-size: 0.3rem;
            color: rgba(34, 34, 34, 1);
            font-weight: 400;
            padding: 0.3rem 0;

            i {
              position: relative;
            }
          }
        }

        .one {
          width: 1.9rem;
          background-color: rgba(224, 243, 255, 1);
          padding: 0.3rem 0.1rem;

          p:first-child {
            font-size: 0.26rem;
            color: rgba(34, 34, 34, 1);
          }

          p:last-child {
            font-size: 0.26rem;
            color: rgba(0, 138, 230, 1);
            font-weight: 400;
          }

          h3 {
            font-size: 0.42rem !important;
            color: rgba(0, 138, 230, 1);
            font-weight: 600;
            padding: 0.15rem 0;
          }
        }

        .two {
          width: 1.35rem;
        }

        .three {
          h3 {
            color: rgba(50, 205, 145, 1);
          }
        }

        .two,
        .three,
        .four {
          position: relative;
        }

        .two::after,
        .three::after,
        .four::after {
          content: "";
          position: absolute;
          border-left: 1px dashed #e1e1e1;
          width: 1px;
          height: 0.66rem;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    li:last-child {
      margin-bottom: 0;
    }
  }

  .content .selected {
    background-color: #fff;
    border: 1px solid #3e7bfa;
    color: #3e7bfa;
  }

  .content .disabled {
    color: #ccc;
  }

  .content ul {
    overflow: hidden;
    flex: 1;
    overflow-y: scroll;

    li {
      width: 2.1rem;
      height: 0.72rem;
      display: flex;
      justify-content: center;
      align-content: center;
      text-align: center;
      // line-height: 0.72rem;
      background: #eeeeee;
      font-size: 0.28rem;
      display: flex;
      float: left;
      justify-content: center;
      align-items: center;
      border-radius: 36px;
      margin-left: 0.28rem;
      margin-bottom: 0.3rem;
      border: 1px solid #eeeeee;

      &:nth-child(3n) {
        // margin-left: 0;
      }

      span {
        padding: 0.14rem 0;
      }
    }

    .size-5 {
      // line-height: 0.4rem;
      font-size: 0.2rem;
      // transform: scale(0.5);
    }

    .size-7 {
      font-size: 0.15rem;
    }
  }

  .content {
    .header {
      padding-top: calc(0.05rem + 45px);

      .header-top {
        display: flex;
        justify-content: space-between;
        position: relative;

        .header-top-left {
          display: flex;
          flex-direction: column;
          width: 3rem;

          .title-remark {
            width: 100vw;
            color: #999;
            font-size: 0.25rem;
            padding-left: 0.6rem;
            margin-top: 0.4rem;
            margin-bottom: -0.2rem;
            z-index: 100;
          }

          ::v-deep .van-dropdown-menu {
            position: fixed;
            top: calc(0.9rem + 5vh + env(safe-area-inset-top) + 1.1rem); // tab 高度 + title 高度 + 顶部安全高度 + 日期高度
            z-index: 1000;
          }

          ::v-deep .van-popup.van-popup--top.van-dropdown-item__content {
            ul {
              padding: 10px 0;
            }
          }

          ::v-deep .van-dropdown-menu__title {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-top-right-radius: 2em;
            border-bottom-right-radius: 2em;
            padding-right: 1rem;
            box-shadow: 0px 4px 10px 2px rgba(66, 122, 225, 0.09);
            max-width: 5.6rem;
            height: 0.8rem;
            margin-top: 0.1rem;
            font-size: 0.3rem;

            &::after {
              right: 0.4rem;
            }
          }
        }

        .header-top-right {
          // width: calc(100% - 3rem);
          width: calc(100% - 3.8rem);
          right: 0;
          // bottom: -39%;
          top: -1rem;
          height: auto;
          position: absolute;
        }

        .header-top-right-meta {
          // top: -1.6rem;
        }
      }

      .title {
        font-weight: 700;
        font-size: 0.32rem;
        display: flex;
        align-items: center;
        padding-left: 0.36rem;
        margin-top: 0.4rem;
        margin-bottom: 0.2rem;

        &::before {
          content: " ";
          display: inline-block;
          width: 0.1rem;
          height: 0.1rem;
          border-radius: 100%;
          background-color: #000;
          margin-right: 0.15rem;
        }
      }

      .value {
        display: flex;
        align-items: center;
        font-size: 0.5rem;

        &.value-1 {
          padding-left: 0.6rem;
        }

        .value-text {
          font-weight: 700;
          font-size: 0.5rem;
        }

        .value-unit {
          color: #71737a;
          margin-left: 0.2rem;
          font-size: 0.26rem;
          min-width: 1rem;
          margin-top: 0.1rem;
        }
      }

      .other {
        width: 100vw;
        font-size: 0.3rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.3rem;
        padding: 0 0.6rem;
        box-sizing: border-box;
        gap: 0.2rem;
      }

      .other-item {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .label {
          display: inline-block;
          color: #71737a;
          font-size: 0.2rem;
        }

        .other-value {
          font-size: 0.32rem;
          padding: 0;
          margin-right: 0.2rem;
          margin-left: 0.2rem;
          min-width: 1.2rem;
          text-align: right;
          margin-top: -0.05rem;
        }

        .other-value-unit {
          display: inline-block;
          font-size: 0.2rem;
          font-size: 0.2rem;
        }

        .other-value-up {
          @extend .other-value;
          color: #e60012;
        }

        .other-value-down {
          @extend .other-value;
          color: #32cd91;
        }

        &:first-child {
          width: 45%;

          .label {
            min-width: 0.6rem;
          }

          .other-value-unit {
            width: 0.6rem;
          }
        }

        &:last-child {
          width: 55%;

          .label {
            min-width: 1.2rem;
          }

          .other-value-unit {
            min-width: 0.8rem;
          }
        }
      }

      .other-item-vertical {
        @extend .other-item;
        padding-left: 0.6rem;

        width: 100%;

        .label {
          min-width: 1.8rem !important;
        }

        .other-value-unit {
          min-width: 1rem !important;
        }
      }

      .bottom-title {
        display: flex;
        align-items: center;
        margin-top: 0.4rem;
        // margin-bottom: 0.2rem;

        .title {
          margin-right: 0.2rem;
          display: flex;
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }

    .index-card {
      margin: 0.36rem;
      background-color: #fff;
      border-radius: 0.16rem;
      box-shadow: 5px 6px 20px 0 rgba(74, 103, 157, 0.1);
      padding: 0.12rem 0.3rem;

      .index-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px dashed #e1e1e1;
        padding: 0.18rem 0;

        &:last-child {
          border-color: transparent;
        }

        img {
          margin-right: 0.15rem;
          width: 0.6rem;
          height: 0.6rem;
        }

        div {
          display: flex;
          align-items: center;
        }

        .index-item-name {
          max-width: 3.5rem;
          font-size: 0.3rem;
          color: #181c26;
        }

        .index-item-tail {
          // width: 2.6rem;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }

        .index-item-value {
          font-size: 0.4rem;
          font-weight: 700;
        }

        .index-item-unit {
          display: flex;
          justify-content: flex-end;
          // min-width: 1rem;
          margin-left: 0.18rem;
          font-size: 0.2rem;
          display: flex;
          margin-top: 0.05rem;
        }
      }
    }

    .task-card {
      padding: 0.4rem 0.3rem;
      font-size: 0.32rem;
      margin: 0.4rem 0.36rem;
      box-shadow: 5px 6px 20px 0 rgba(74, 103, 157, 0.1);
      background: url(../../../assets/img/ranking/second_task_bg.png);
      background-size: cover;
      border-radius: 0.16rem;

      .title {
        font-weight: 700;
        color: #4481f6;
        margin-bottom: 0.4rem;
      }

      .task-item {
        display: flex;
        margin-bottom: 0.24rem;

        .index {
          width: 0.2rem;
          margin-right: 0.22rem;
        }

        .content {
          display: flex;
          flex-direction: column;
          max-width: calc(100% - 0.42rem);
          min-width: calc(100% - 0.42rem);
          flex: 1;

          .name {
            // margin-bottom: 0.2rem;
            font-weight: 700;
         
          }
          .sort{
            margin-right: 0.1rem;
          }

          .secondTitle {
            font-size: 0.26rem;
            margin-left: 0.1rem;

            &-first {
              margin-top: 0.2rem;
            }
          }

          .bottom {
            display: flex;
            align-items: center;
          }

          .btn {
            background: url(../../../assets/img/ranking/second_details_btn.png);
            width: 0.8rem;
            height: 0.8rem;
            line-height: 0.8rem;
            padding-left: 0.1rem;
            padding-top: 0.01rem;
            font-size: 0.2rem;
            color: #244db3;
            background-size: contain;
            background-repeat: no-repeat;
            margin-left: 0.3rem;

            span {
              // position: absolute;
              // top: -0.03rem;
              // left: 0.05rem;
            }
          }

          .progress-bar {}
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .task-no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        img {
          margin-top: 0.4rem;
          margin-bottom: 0.7rem;
          width: 2.4rem;
          height: auto;
        }

        span {
          font-size: 0.28rem;
        }
      }

      .task-no-data-meta-center {
        @extend .task-no-data;
        height: 6.5rem;
        justify-content: center;

        img {
          margin-top: -0.5rem;
          margin-bottom: 1.2rem;
        }
      }
    }

    ::v-deep .van-dropdown-item__content {
      max-height: 90%;
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
      position: relative;
      display: flex;
      flex-direction: column;

      .btnBox {}
    }

    ::v-deep .van-dropdown-menu__bar {
      box-shadow: unset;
      margin-top: 2px;
      background-color: transparent;

      .van-dropdown-menu__item {
        justify-content: flex-start;
        font-weight: 1000;
      }

      .van-dropdown-menu__title {
        padding: 0 0.35rem;
        font-size: 0.32rem;
      }

      .van-dropdown-menu__title--active {
        color: #6495fb;
      }

      .van-dropdown-menu__title::after {
        right: -8px;
        border-width: 5px;
        margin-top: -8px;
        border-color: transparent transparent #000 #000;
      }

      .van-dropdown-menu__title--active::after {
        right: -8px;
        border-width: 5px;
        margin-top: 0;
        border-color: transparent transparent currentColor currentColor;
      }
    }

    ::v-deep .van-dropdown-menu__bar:not(.van-dropdown-menu__bar--opened) {
      // background: #f6f7f9;
    }
  }

  .modal {
    border-top-right-radius: 0.35rem;
    border-top-left-radius: 0.35rem;



    .modal-no-data {
      height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 0.32rem;

      img {
        width: 2.6rem;
        height: auto;
        margin-bottom: 0.6rem;
      }
    }

    .tab {
      width: 100%;
      height: 1.12rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tab-left {
        width: 50%;
        height: 100%;
      }

      .tab-right {
        width: 50%;
        height: 100%;
      }
    }

    .tab1 {
      @extend .tab;
      background: url(../../../assets/img/ranking/second_modal_tab_1.png);
      background-size: cover;
    }

    .tab2 {
      @extend .tab;
      background: url(../../../assets/img/ranking/second_modal_tab_2.png);
      background-size: cover;
    }

    .modal-content {
      background-color: #e8f0fa;
      padding: 0.3rem;
      margin: 0.4rem 0.36rem;
      font-size: 0.34rem;
      max-height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
      overflow: auto;
      border-radius: 0.1rem;
      text-align: justify;

      ::v-deep .task-details-title {
        font-weight: 700;
        color: #4f9dfa;
        margin-bottom: 0.2rem;
      }
    }

    .close-btn {
      width: 100%;
      padding: 0 0.36rem;
      position: absolute;
      bottom: 1rem;

      .close-btn-inner {
        width: 100%;
        background-image: linear-gradient(90deg, #6baaff 0%, #3b7afd 100%);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 0.86rem;
        font-size: 0.34rem;
        border-radius: 4em;
      }
    }
  }
}