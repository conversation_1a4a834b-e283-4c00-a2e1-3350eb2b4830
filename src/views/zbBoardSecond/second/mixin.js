import Icon1 from "@/assets/img/ranking/second_icon_净资产收益率（年化）.png";
import Icon2 from "@/assets/img/ranking/second_icon_全员劳动生产率（年化）.png";
import Icon3 from "@/assets/img/ranking/second_icon_研发经费投入强度.png";
import Icon4 from "@/assets/img/ranking/second_icon_营业现金比率.png";
import Icon5 from "@/assets/img/ranking/second_icon_资产负债率.png";

import { getSecKbData } from "@/http/api";
import { formatNum } from "@/utils";
import ProgressBar from "@/components/ProgressBar/index.vue";

export default {
  name: "secondUnit",
  components: {
    ProgressBar,
  },
  props: {
    dt: {
      type: String,
      default: "",
    },
    userId: String,
    companyInfoList: Array,
    companyId: String,
  },
  watch: {
    entp_no: {
      handler(newVal) {
        this.$nextTick(() => {
          this.isMetaCenter = this.entp_no == "99041";
          this.updateData();
        });
        if (newVal) {
          this.$store.dispatch("commitCompanyClickSecond", { active: 'ejqy', currentCompany: newVal });
        }
      },
    },
    userId: {
      handler() {
        this.initSelfInfo();
      },
    },
    companyInfoList: {
      handler() {
        this.initSelfInfo();
      },
    },
    show: {
      handler() {
        // 弹窗关闭恢复 tab 到任务内容
        if (!this.show) {
          setTimeout(() => {
            this.activeTab = 1
          }, 500)
        }
      }
    },
    dt: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        this.initCompanyList(newVal).then(() => {
          this.updateData()
        })
      }
    }
  },
  data() {
    return {
      rateData: {
        key: "busi_cash_rep_rat",
        name: "营业收现率",
      },
      selfInfo: [],
      isMetaCenter: false,
      activeTab: 1,
      show: false,
      entp_no: "",
      companyList: [],
      defaultIndexList: [
        {
          icon: Icon2,
          key: "csh_rate",
          name: "营业现金比率",
          value: "",
          unit: "%",
        },
        {
          icon: Icon2,
          key: "cst_biz_rat",
          name: "成本费用占收入比率",
          value: "",
          unit: "%",
        },
        {
          icon: Icon2,
          key: "rdeivt_deg",
          name: "研发经费投入强度",
          value: "",
          unit: "%",
        },
        {
          icon: Icon2,
          key: "dbas_rate",
          name: "资产负债率",
          value: "",
          unit: "%",
        },
        {
          icon: Icon5,
          key: "tlp_rate",
          name: "全员劳动生产率（年化）",
          value: "",
          unit: "万元/人",
          unitStyle: {
            minWidth: '1rem'
          }
        },
      ],
      indexList: [],
      taskDetails: {},
      taskList: [],
      yysr: {},
      lrze: {},
      fiverate: {},
      yqjjyzx: {},
    };
  },
  computed: {
    currentName() {
      const target = this.companyList.find(
        (item) => item.entp_no == this.entp_no
      );
      return target ? target.entp_nm : "";
    },
    isShowRemark() {
      // 允许展示注释的企业编号
      const list = ['99001', '99015', '99043']
      // desc
      const descList = ['99005', '99011']
      let text = ''
      if (list.includes(this.entp_no)) {
        text = '管理口径'
      } else if (descList.includes(this.entp_no)) {
        const map = {
          99011: '不含彩虹股份投资收益',
          99005: '不含新洁能股票投资收益'
        }
        text = `注：${map[this.entp_no]}`
      }

      return {
        show: Boolean(text),
        text
      }
    },
  },
  created() {
    this.defaultIndexList = [{
      icon: Icon1,
      key: this.rateData.key,
      name: this.rateData.name,
      value: "",
      unit: "%",
    }, ...this.defaultIndexList]
  },
  methods: {
    formatNum,
    progressBarDisabled(item) {
      // 强军首则或展示为 - 时，禁用
      if (item.task_name == '强军首责' || (item.task_pct !== undefined && isNaN(item.task_pct))) return true
      // if (item.task_name == '强军首责') {
      //   if (['99007', '99008'].includes(this.entp_no)) {
      //     return true
      //   }
      // }

      return false
    },
    // 初始化页面
    updateData() {
      // 每次切换先重置数据
      this.yysr = {};
      this.lrze = {};
      this.indexList = [];
      this.yqjjyzx = {};
      this.taskList = [];

      // 营业收入
      getSecKbData({
        dt: this.dt,
        entp_no: this.entp_no,
        mapperInterface: ["yysr"],
        moreChooseType: "3",
      })
        .then((res) => {
          console.log(res.data.yysr, 'asdasd')
          res.data.yysr && (this.yysr = res.data.yysr[0] || {});
        })
      // 利润总额
      getSecKbData({
        dt: this.dt,
        entp_no: this.entp_no,
        mapperInterface: ["lrze"],
        moreChooseType: "3",
      })
        .then((res) => {
          res.data.lrze && (this.lrze = res.data.lrze[0] || {});
        })
      // 五个比率
      getSecKbData({
        dt: this.dt,
        entp_no: this.entp_no,
        mapperInterface: ["fiverate"],
        moreChooseType: "3",
      })
        .then((res) => {
          if (res.data.fiverate) {
            this.indexList = res.data.fiverate[0]
              ? this.defaultIndexList.map((item) => ({
                ...item,
                value: res.data.fiverate[0][item.key],
              }))
              : [];
          }
        })
    },
    toggleSelect(entp_no) {
      // 如果非集团整体且不是自己所在企业则不能看数据
      if (this.selfInfo.every(item => !item.isAll) && this.selfInfo.every(item => item.entpNo !== entp_no)) return;
      this.entp_no = entp_no;
      this.$refs.dropDwonRef.toggle();
      this.$emit('updateDateList', entp_no)
    },
    initSelfInfo() {
      if (!this.userId || !this.companyInfoList) return;

      // 清空之前的数据，避免累积
      this.selfInfo = [];

      this.companyInfoList.forEach((item) => {
        if (this.userId && item.userIds && item.userIds.includes(this.userId)) {
          this.selfInfo.push({
            ...item,
            isAll: item.cpyTag == "01",
          });
        }
      });

      this.initDefault();
    },
    initDefault() {
      // 优先排行榜点击传入的企业编号
      if (this.$store.state.companyClickSecond) {
        this.entp_no = this.$store.state.companyClickSecond.currentCompany;
      } else {
        const selfFisrt = this.selfInfo[0] ? this.selfInfo[0].entpNo : ''
        // 集团整体默认展示第一个，其他二级企业默认展示自己所在企业
        if (this.selfInfo.some(item => item.isAll)) {
          // 如果是全集团权限，需要确保选择的是二级企业而不是集团企业
          if (this.companyList && this.companyList.length > 0) {
            // 过滤掉集团企业编号，选择第一个二级企业
            const secondCompany = this.companyList.filter(item => item.entp_no !== this.companyId)

            this.entp_no = secondCompany && secondCompany.length > 0 ? secondCompany[0].entp_no : ''
          } else {
            this.entp_no = ''
          }
        } else {
          this.entp_no = selfFisrt
        }
      }
    },
    initCompanyList(dt) {
      return new Promise((resolve, reject) => {
        getSecKbData({
          dt,
          dis_scp_no: "2",
          mapperInterface: ["scdcom"],
          moreChooseType: "3",
          companyId: this.companyId
        }).then((res) => {
          // 拿到二级企业列表，初始化首选的二级企业
          this.companyList = res.data.scdcom;
          this.initSelfInfo();
        }).finally(() => {
          resolve(true)
        })
      })
    },
    handleViewDetails(item, father) {
      const task_desp = item.task_desp ? `<p class='task-details-title'>${father.children.length > 1 ? item.task_nm_l2 : father.task_name}</p>${item.task_desp}` : ''
      const task_detail = item.task_stas ? `<p class='task-details-title'>${father.children.length > 1 ? item.task_nm_l2 : father.task_name}</p>${item.task_stas}` : ''
      this.taskDetails = {
        ...item,
        task_desp,
        task_detail
      }
      this.show = true;
    },
  },
}
