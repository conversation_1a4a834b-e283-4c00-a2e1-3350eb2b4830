.groupAll {
  display: flex;
  flex-direction: column;

  .report-link {
    width: 100vw;
    background: #fff;
    padding: 0.3rem;
    color: #4894E4;
    position: fixed;
    top: calc(20vh + 5vh + env(safe-area-inset-top) + 1.1rem); // 背景图高度 + title 高度 + 设备顶部安全高度 + 日期高度
    font-size: 0.32rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 6px 20px rgba(74, 103, 157, 0.16);
    z-index: 1000;

    span {
      display: flex;
      height: 0.3rem;
      align-items: center;
    }

    span::before {
      content: " ";
      display: inline-block;
      width: 0.1rem;
      height: 100%;
      background: #4894E4;
      border-top-right-radius: 0.05rem;
      border-bottom-right-radius: 0.05rem;
      margin-right: 0.17rem;
    }

    img {
      height: 0.38rem;
      width: 0.5rem;
    }
  }

  ul {
    max-height: calc(100vh - 4.5rem);
    overflow: auto;
    padding: 0.35rem;
    padding-bottom: 200px;

    margin-top: 2.7rem;

    li {
      background: #ffffff;
      border-radius: 0.08rem;
      margin-bottom: .35rem;

      .box {
        display: flex;
        text-align: center;
        min-height: 2.35rem;

        .commanBox {
          display: flex;
          width: 100%;
          // flex-direction: column;//纵向排列
          justify-content: space-around;
          align-items: center;
          // padding: 0.35rem 0;
          // width: 1.25rem;

          p:first-child {
            font-size: 0.28rem;
            color: #71737A;
          }
          p:last-child {
            font-size: 0.26rem;
            color: #222;
          }
          h3 {
            font-size: 0.35rem;
            color: #222;
            font-weight: 400;
            // margin: .25rem 0;
          }
        }
        .one {
          width: 34%;
          // height: 100%;
          flex: 1;
          background-color: #d9f1fe;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0.28rem 0;
          justify-content: space-between;
          box-shadow: 0 6px 20px rgba(74, 103, 157, 0.16);
          overflow: hidden;
          border-top-left-radius: 0.1rem;
          border-bottom-left-radius: 0.1rem;

          p:first-child {
            font-size: 0.28rem;
            color: #222;
          }
          p:last-child {
            font-size: 0.26rem;
            color: #008AE6;
            font-weight: 400;
          }
          h3 {
            font-size: 0.50rem !important;
            color: #008AE6;
            font-weight: 600;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          width: calc(100% - 34%);
          padding: 0.28rem 0.15rem;
          // padding: 0 0.06rem;
          box-shadow: 5px 6px 20px rgba(74, 103, 157, 0.16);
          overflow: hidden;
          border-top-right-radius: 0.1rem;
          border-bottom-right-radius: 0.1rem;

          .item-label {
            width: 2.6rem;
            text-align: left;
            padding-left: 0.05rem;
          }

          .item-value {
            width: 1.5rem;
            text-align: right;
          }

          .item-unit {
            width: 1.8rem;
            text-align: right;
            // padding-right: 0.01rem;
          }

          .split {
            width: 1px;
            height: .66rem;
            border-right: 1px dashed #999;
          }

          .three {
            h3 {
              color: rgba(50, 205, 145, 1);
            }
          }
          .two,.three,.four{
            position: relative;
          }
          // .two::after,.three::after{
          //   content: '';
          //   position: absolute;
          //   border-left: 1px dashed #999999;
          //   width: 1px;
          //   height: .66rem;
          //   right: -1px;
          //   top: 50%;
          //   transform: translateY(-50%);
          // }
        }
      }
    }
    li:last-child{
        margin-bottom: 0;
    }
  }
}