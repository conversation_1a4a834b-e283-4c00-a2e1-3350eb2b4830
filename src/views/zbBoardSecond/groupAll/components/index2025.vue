<template>
  <div class="groupAll">
    <!-- <div class="report-link" @click="viewReport">
      <span>查看报告</span>
      <img src="../../../../assets/img/ranking/view_report_icon.png" alt="">
    </div> -->
    <ul>
      <li v-for="(item,index) in groupList" :key="index">
        <div class="box">
          <div class="one">
            <p>{{item.name}}</p>
            <h3 :style="{padding: item.name.includes('全员劳动生产率') ? '0' : '0.15rem 0',color: blueReg}">{{ formatNum(item.dq, item.unit == '亿元' ? undefined : 1) }}</h3>
            <p :style="{color: blueReg}">{{item.unit}}</p>
          </div>
          <div class="right">
            <div class="commanBox two">
              <p class="item-label">去年同期</p>
              <h3 class="item-value">{{ formatNum(item.tq) }}</h3>
              <p class="item-unit">{{item.unit}}</p>
            </div>
            <!-- <div class="split"/> -->
            <div class="commanBox three">
              <p class="item-label">同比</p>
              <h3
                v-if="['lrze'].includes(item.key)"
                :style="{color: getTextColor(item)}"
                class="item-value"
              >
              {{ !isNaN(Number(item.dq) - Number(item.tq)) ? formatNum(Number(item.dq) - Number(item.tq), undefined, 'ratio') : '-' }}
              </h3>
              <h3 v-else class="item-value" :style="{color: getTextColor(item)}">{{ formatNum(item.tb, undefined, 'ratio') }}</h3>
              <p class="item-unit"> {{ ['lrze'].includes(item.key) ? item.unit : item.unit2 }} </p>
            </div>
            <!-- <div class="split"/> -->
            <div class="commanBox four">
              <p class="item-label">预算目标</p>
              <h3 class="item-value">{{ formatNum(item.rw) }}</h3>
              <p class="item-unit">{{item.unit}}</p>
            </div>
          </div>
          
          <!-- <div class="commanBox five">
            <p>达成率</p>
             <h3 :style="{color: blueReg}">
              <span v-if="item.dcl && item.dcl>999">999<i>⊕</i></span>
              <span v-else-if="item.dcl && item.dcl<-999">-999<i>⊕</i></span>
              <span v-else>{{item.dcl}}</span>
            </h3>
            <p>%</p>
          </div> -->
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import mixin from '../mixin.js'
import { grouList2025 } from "../config.js";

export default {
  mixins: [mixin],
  created() {
    this.groupList = grouList2025
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
