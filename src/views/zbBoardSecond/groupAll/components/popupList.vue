<template>
    <div class="container">
        <div class="title">{{ keyValue == 'yysr' ? '营业收入' : "利润总额" }}</div>
        <div class="listContainer">

            <van-row class="listRow">
                <van-col span="4">序号</van-col>
                <van-col span="12">二级企业</van-col>
                <van-col span="8">{{ keyValue == 'yysr' ? '营业收入（亿元）' : "利润总额（亿元）" }}</van-col>
            </van-row>
            <div class="listBody" ref="listBody">
                <van-row v-for="(item, index) in companyList" :key="index">
                    <van-col span="4">{{ index + 1 }}</van-col>
                    <van-col span="12">{{ item.name }}</van-col>
                    <van-col span="8">{{ judgeViewEnabled(item, item.value) }}</van-col>
                </van-row>
            </div>
        </div>


    </div>
</template>
<script>
import { getKbData } from "@/http/api"
import {getState} from "@/utils/cache"
import { formatNum } from "@/utils";
export default {
    name: "popupList",
    props: {
        dt: String,
        showPopup: Boolean,
        keyValue: String,
        userId: String
    },
    data() {
        return {
            companyList: [],
            companyInfoList: [],
            showAllCompany: false
        }
    },
    watch: {
        showPopup(newVal) {
            if (newVal) {
                this.updateData()
                this.$refs['listBody'].scrollTop = 0

            } else {
                this.showAllCompany = false
            }
        }
    },
    mounted() {
        this.updateData()
    },
    methods: {
        updateData(newParams = {}) {
            let tgt_no = 'A01';
            let mapperInterface = 'yysrpm'
            if (this.keyValue == "lrze") {
                tgt_no = 'A02'
                mapperInterface = "lrzepm"
            }
            const params = {
                dt: this.dt,
                mapperInterface: [mapperInterface],
                moreChooseType: "0",
                ...newParams
            }

            // 完成值数据
            getKbData({
                ...params,
            }).then(res => {
                if (res.code == 200 && res.data[mapperInterface]) {
                    const data = res.data[mapperInterface].map(item => {
                        const value = formatNum(item.dsp_val)

                        return {
                            ...item,
                            name: item.entp_nm,
                            value
                        }
                    });
                    console.log(data);
                    this.companyList =
                        data.sort((a, b) => { return Number(b.dsp_val) - Number(a.dsp_val) }); //降序排列年份

                
                      
                            this.companyInfoList = getState('second_compnay_info_list')
                            this.companyInfoList.forEach(element => {
                                if (element.userIds.includes(this.userId) && element.entpNo == '99') {
                                    this.showAllCompany = true
                                }


                            });

                            const list = this.companyList.map(ratioItem => {
                                const target = this.companyInfoList.find(item => item.entpNo == ratioItem.entp_no)
                                console.log(target);

                                return {
                                    ...ratioItem,
                                    ...target
                                }
                            });
                            this.companyList = list
                        
                    

                }
            }
            ).catch(error => {
                this.companyList = []
            })
        },
        judgeViewEnabled(item, value) {
            const {
                entp_no: entpNo,
                cpyType: cpy_type
            } = item
            let self = []
            this.companyList.forEach(item => {
                // 找出自己所在企业
                if (item.userIds && item.userIds.includes(this.userId)) {
                    self.push(item)
                }
            })
            console.log(self);

            /**
             * 允许查看数据
             * 1. 自己所在企业
             * 2. 非上市公司
             * 3. 全集团
             */
            if (
                (self && self.some(item => item.entp_no == entpNo)) ||
                (cpy_type == '1') ||
                (self.some(item => item.cpyTag == '01')) || (this.showAllCompany)
            ) {
                return value
            }

            return value
        },
    }
}
</script>

<style lang="scss" scoped>
.container {
    .title {
        color: rgb(34, 34, 34);
        font-size: 0.32rem;
        font-weight: 600;
        height: .9rem;
        padding-left: 0.34rem;
        display: flex;
        align-items: center;
    }

    .listContainer {

        .van-row {
            // height: 0.6rem;
            // line-height: 0.6rem;
            text-align: center;
            padding-top: 0.12rem;
            padding-bottom: 0.12rem;
            border-bottom: 1px solid #dcdcdc;
        }

        .listRow {
            top: .9rem;
            width: 100%;
            line-height: 0.6rem;
            text-align: center;
            color: #434343;
            background-color: rgb(176, 208, 242);
            font-size: 0.24rem;
        }

        .listBody {
            height: 6.2rem;
            overflow: auto;
            padding-bottom: 0.1rem;
        }
    }
}
</style>