<template>
  <div class="container">
    <div class="content">
      <div class="index-list">
        <div v-for="item in indexList" class="index-item" :class="item.value == tgt_no ? 'index-item-actived' : ''"
          :key="item.value" @click="handleIndexChange(item.value)">
          {{ item.label }}
        </div>
      </div>

      <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
        :rankTab="rankTab" type="value" title="完成值" :data="incomeData.value" />
      <rankling-list :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"
        :rankTab="rankTab" type="ratio" title="同比变化" secondTitle="去年同期" :hasTip="false" :unit="otherUnit"
        :data="revenueData.value" />

    </div>
    <!-- <van-popup closeable v-model="tableVisible" position="center"
      :style="{ width: '88%', padding: '0.5rem', borderRadius: '5px', maxHeight: '80vh', overflow: 'auto' }"
      @close="tableVisible = false">
      <h2 :style="{ color: '#222222', fontSize: '0.32rem', fontWeight: '600', marginBottom: '0.2rem' }">参与排名的二级企业名单</h2>
      <div class="company-list-table"
        :class="rankTab == 'progress' ? 'company-list-table-progress' : 'company-list-table-retrogress'">
        <div class="company-list-table-header">
          <div class="company-list-table-row">
            <div class="company-list-table-col1">序号</div>
            <div class="company-list-table-col2">二级企业名称</div>
          </div>
        </div>
        <div class="company-list-table-body" :key="tgt_no + rankTab">
          <div v-for="item in sortList" :key="item.index" class="company-list-table-row">
            <div class="company-list-table-col1">{{ item.index }}</div>
            <div class="company-list-table-col2">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </van-popup> -->
  </div>
</template>

<script>
import mixin from '../mixin'
import { Decimal } from 'decimal.js'

import { getKbData } from "@/http/api";
import { formatNum } from "@/utils"
export default {
  mixins: [mixin],
  data() {
    return {
      dsp_typ_cd_1: '6',
      dsp_typ_cd_2: "4",
      otherUnit: ""
    }
  },
  created() {
    const list = { A01: '6', A02: '5' }
    const unitList = { A01: '', A02: '亿元' }
    this.dsp_typ_cd_1 = list[this.tgt_no]
    this.otherUnit = unitList[this.tgt_no]
  },
  watch: {
    tgt_no(newVal) {
      const list = { A01: '6', A02: '5' }
      const unitList = { A01: '', A02: '亿元' }
      this.otherUnit = unitList[newVal]
      this.dsp_typ_cd_1 = list[newVal]
    },
    immediate: true
  },
}
</script>

<style src="../index.scss" lang="scss" scoped />
