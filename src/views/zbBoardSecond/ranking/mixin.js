import dayjs from "dayjs"
import { Decimal } from 'decimal.js'

import { getSecKbData } from "@/http/api";
import itemTip from "@/components/itemTip.vue";
import { formatNum } from "@/utils"

import RanklingList from "./RankingList.vue";

export default {
  name: "indicatorRanking",
  components: {
    RanklingList,
    itemTip,
  },
  props: {
    companyInfoList: Array,
    rankTab: String,
    dt: String,
    userId: String,
    companyId: String
  },
  data() {
    return {
      tgt_no: "A02",
      tableVisible: false,
      indexList: [
        {
          label: "利润总额",
          value: "A02",
        },
        {
          label: "营业收入",
          value: "A01",
        },
      ],
      incomeData: {
        value: [],
        index: []
      },
      revenueData: {
        value: [],
        index: []
      },
      sortList: [],
      showAllCompany:false
    };
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.init()
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.updateData()
      // this.updateCompanySortList()
    },
    //
    judgeViewEnabled(item, value) {
      const {
        entp_no: entpNo,
      } = item
      let cpyType ,cpyTag,userIds;
      let self = []
      this.showAllCompany=false
      this.companyInfoList && this.companyInfoList.forEach(item => {
        if(item.entpNo==entpNo){
          cpyType=item.cpyType
          cpyTag=item.cpyTag
          userIds=item.userIds
        }
        if(item.userIds.includes(this.userId)&&item.entpNo=='99'){
          this.showAllCompany=true
        }
        
        // 找出自己所在企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push({...item})
        }
      })
      
      /**
       * 允许查看数据
       * 1. 自己所在企业
       * 2. 非上市公司
       * 3. 全集团
       */
      if (
        (self && self.some(item => item.entpNo == entpNo)) ||
        (cpyType == 1) ||
        (self.some(item => item.cpyTag == '01'))||this.showAllCompany
      ) {
        return value
      }

      return '***'
    },
    judgeDetailsEnabled(entpNo) {
      let self = []

      this.companyInfoList.forEach(item => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push(item)
        }
      })

      /**
       * 允许跳转详情页
       * 1. 是自己所在企业
       * 2. 自己是全集团
       */
      if (self.some(item => item.entpNo === entpNo) || self.some(item => item.cpyTag == '01')) return true

      return false
    },
    // updateCompanySortList() {
    //   const params = {
    //     dt: this.dt,
    //     tgt_no: this.tgt_no,
    //     moreChooseType: "2",
    //     mapperInterface: ["rbcrgl"],
    //   }

    //   getSecKbData(params)
    //     .then((res) => {
    //       if(res.code == 200 && res.data.rbcrgl) {
    //         this.sortList = res.data.rbcrgl.map(item => ({
    //           name: item.entp_nm,
    //           index: item.seq_no
    //         }))
    //       }
    //     })
    // },
    updateData(newParams = {}) {
      // 完成值数据
      getSecKbData({
        dt: this.dt,
        mapperInterface: ["rbczbpm"],
        moreChooseType: "2",
        tgt_no: this.tgt_no,
        ...newParams,
        dsp_typ_cd: "1",
        companyId: this.companyId
      }).then(res => {
        if(res.code == 200 && res.data.rbczbpm) {
          const data = res.data.rbczbpm.map(item => {
            const value = formatNum(item.dsp_val)

            return {
              ...item,
              name: item.entp_nm,
              value: value
            }
          });
          this.incomeData.value = [...data].reverse()
        }
      })

      // 同比变化数据
      getSecKbData({
        dt: this.dt,
        mapperInterface: ["rbczbpm"],
        moreChooseType: "2",
        tgt_no: this.tgt_no,
        ...newParams,
        dsp_typ_cd: "2",
        companyId: this.companyId
      }).then(tbRes => {

        // 同期值
        getSecKbData({
          dt: this.dt,
          mapperInterface: ["rbczbpm"],
          moreChooseType: "2",
          tgt_no: this.tgt_no,
          ...newParams,
          dsp_typ_cd: "3",
          companyId: this.companyId
        }).then(tqRes => {
          if(tqRes.code == 200 && tbRes.code == 200) {
            const data = tbRes.data.rbczbpm.map(rbcItem => {
              const value = formatNum(rbcItem.dsp_val, undefined, 'ratio')
              const addValue = tqRes.data.rbczbpm.find(item => rbcItem.entp_no === item.entp_no).dsp_val
  
              return {
                ...rbcItem,
                name: rbcItem.entp_nm,
                addValue: formatNum(addValue),
                value
              }
            });
            this.revenueData.value = data
          }
        })
      })
    },
    handleTabChange(tab) {
      this.$emit('rankTabChange', tab)
    },
    handleIndexChange(index) {
      this.tgt_no = index;
      this.updateData()
      // this.updateCompanySortList()
    },
  },
}