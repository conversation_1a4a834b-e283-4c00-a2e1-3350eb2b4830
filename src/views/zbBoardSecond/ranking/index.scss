.container {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem 0.3rem;
  padding-top: 3rem;

  .tab-inner {
    color: #AC3333;
    color: rgba(172,51,51,.9);
    background-image: url(../../../assets/img/ranking/progress_header.png);

    .actived-tab {
      color: #FFF9A8;
    }
  }

  .index-item {
    color: #000;
    background-color: #fff;
    border: 0.04rem solid #eaeaea;
    box-shadow: 0px 0px 4px rgba(74,103,157,.16);

    &.index-item-actived {
      background-color: #348ae6;
      color: #fff;
      border: 0.04rem solid #348ae6;
      box-shadow: 0px 2px 4px rgba(156,156,156,.5);
    }
  }
}

.content {
  height: calc(100vh - 3rem);
  overflow: auto;
  padding: 0.2rem 0.2rem;
  padding-bottom: 200px;
  background-color: #fff;
  margin-top: -0.25rem;
  border-bottom-left-radius: 0.1rem;
  border-bottom-right-radius: 0.1rem;

  .index-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .index-item {
      padding: 0.14rem 0.19rem;
      border-radius: 0.08rem;
      font-size: 0.23rem;

      &.index-item-actived {
        font-weight: 700;
      }
    }
  }

  .desc {
    color: #666;
    margin-top: 0.2rem;
    display: flex;
    align-items: center;
    font-size: 0.19rem;

    img {
      width: 0.3rem;
      height: 0.25rem;
      margin-left: 0.1rem;
      margin-top: 0.02rem;
    }
  }
}

body ::v-deep .company-list-table {
  // 样式内容
}