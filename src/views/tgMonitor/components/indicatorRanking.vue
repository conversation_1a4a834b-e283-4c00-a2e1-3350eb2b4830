<template>
  <div class="groupAll">
    <public-title :title="'任务完成情况'" unit=""></public-title>
    <div class="rwwcqk">
      <div class="rwwcqk_box" v-if="taskFinishData.length">
        <div class="rwwcqk_hd rwwcqk_tb">
          <span class="rwwcqk_ct_item0">序号</span>
          <span class="rwwcqk_ct_item1">企业名称</span>
          <span class="rwwcqk_ct_item1">两金增长率</span>
          <span class="rwwcqk_ct_item1">营收增长率</span>
        </div>
        <div class="rwwcqk_ct rwwcqk_tb" v-for="(item,index) in dataList" :key="index">
          <span class="rwwcqk_ct_item0">{{ item.seq_no }}</span>
          <span class="rwwcqk_ct_item1" :style="{textAlign: 'left'}">{{ item.entp_nm }}</span>
          <span class="rwwcqk_ct_item1" :style="{textAlign: 'right',paddingRight: '0.3rem'}">{{ item.ljzzl ? `${item.ljzzl}%` : '' }}</span>
          <span class="rwwcqk_ct_item1" :style="{textAlign: 'right',paddingRight: '0.3rem'}">{{ item.yszzl ? `${item.yszzl}%` : '' }}</span>
        </div>
      </div>
      <div class="rwwcqk_box nodata" v-else :style="{position: 'relative'}">
        <div v-show="!dataList.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
          <img src="@/assets/img/noData.png" :style="{width: '100%'}">
          <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue'
export default {
  name: 'indicatorRanking',
  components: {
    publicTitle
  },
  props: {
    taskFinishData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    taskFinishData: {
      deep: true,
      handler(newVal, oldVal) {
        this.dataList = JSON.parse(JSON.stringify(newVal)) || []
      }
    }
  },
  data() {
    return {
      dataList: []
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  box-shadow: -0.02rem 0.13rem 0.2rem 0.01rem rgba(160, 160, 161, 0.1);
  border-radius: 0.08rem;
  .rwwcqk {
    padding: 0.3rem;
    background: #fff;
    min-height: 300px;
    width: 100%;
    box-sizing: border-box;
    .rwwcqk_box {
      min-height: 300px;
      font-size: 0.3rem;
      .rwwcqk_tb {
        color: #303030;
        background: #fff;
        font-size: 0.3rem 0 0.4rem 0;
        padding: 0.15rem 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        border-bottom: 1px solid #dcdcdc;
        span {
          padding: 0 0.1rem;
        }
        .rwwcqk_ct_item0 {
          width: 45px;
          text-align: left;
        }
        .rwwcqk_ct_item1 {
          flex: 1;
          text-align: center;
        }
      }
      .rwwcqk_tb:nth-last-of-type(1) {
        border-bottom: unset;
      }
      .rwwcqk_hd {
        color: #434343;
        background: #ebf0fa;
        border-bottom: none;
        span:not(span:nth-last-of-type(1)) {
          position: relative;
          &::after {
            content: '';
            position: absolute;
            border-right: 0.03rem solid #afb0b2;
            width: 0.1rem;
            height: 0.3rem;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .rwwcqk_ct_item0 {
          text-align: center;
        }
        .rwwcqk_ct_item1 {
          text-align: left;
        }
      }
    }
    .rwwcqk_box:not(.nodata) {
      border: 1px solid #dcdcdc;
    }
  }
}
</style>
