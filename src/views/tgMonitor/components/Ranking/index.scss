.container {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem 0.3rem;
  padding-top: 3.72rem;

  &.progress-container {
    .tab-inner {
      color: #AC3333;
      color: rgba(172,51,51,.9);
      background-image: url(../../../../assets/img/ranking/progress_header.png);

      .actived-tab {
        color: #FFF9A8;
      }
    }

    .index-item {
      color: #000;
      background-color: #fff;
      border: 0.04rem solid #eaeaea;
      box-shadow: 0px 0px 4px rgba(74,103,157,.16);

      &.index-item-actived {
        background-color: #fb5c5e;
        color: #fff;
        border: 0.04rem solid #fb5c5e;
        box-shadow: 0px 2px 4px rgba(156,156,156,.5);
      }
    }
  }

  &.retrogress-container {
    .tab-inner {
      color: #FFFAB8;
      color: rgb(255,250,184);
      background-image: url(../../../../assets/img/ranking/retrogress_header.png);

      .actived-tab {
        color: #6A5300;
      }
    }

    .index-item {
      color: #000;
      background-color: #fff;
      border: 0.04rem solid rgba(239,182,83,1);

      &.index-item-actived {
        background-color: #443c3c;
        color: #fff;
        border: 0.04rem solid rgba(239,182,83,1);
      }
    }
  }
}

.tab {
  box-sizing: border-box;
  position: sticky;
  top: -0.06rem;
  z-index: 1000;

  .tab-inner {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    font-size: 0.37rem;
    height: 1.23rem;
    padding: 0.27rem 0.25rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    font-weight: 700;
  }

  // ::after {
  //   content: ' ';
  //   background-color: #fff;
  //   height: 0.4rem;
  //   position: absolute;
  //   top: calc(100% - 0.25rem);
  //   left: 0;
  //   right: 0;
  //   margin: 0 0.3rem;
  // }
}

.content {
  box-shadow: 0px 0px 10px #ccc;
  margin: 0 0.3rem;
  padding: 0.2rem 0.2rem;
  background-color: #fff;
  margin-top: -0.25rem;
  border-bottom-left-radius: 0.1rem;
  border-bottom-right-radius: 0.1rem;

  .index-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .index-item {
      padding: 0.14rem 0.19rem;
      border-radius: 0.08rem;
      font-size: 0.23rem;

      &.index-item-actived {
        font-weight: 700;
      }
    }
  }

  .desc {
    color: #666;
    margin-top: 0.2rem;
    display: flex;
    align-items: center;
    font-size: 0.19rem;

    img {
      width: 0.3rem;
      height: 0.25rem;
      margin-left: 0.1rem;
      margin-top: 0.02rem;
    }
  }
}