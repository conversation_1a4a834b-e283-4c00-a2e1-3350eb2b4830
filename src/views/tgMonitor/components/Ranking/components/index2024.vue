<template>
  <div class="container" :class="rankTab == 'progress' ? 'progress-container' : 'retrogress-container'">
    <div class="tab">
      <div class="tab-inner">
        <span :class="rankTab == 'progress' ? 'actived-tab' : ''" @click="handleTabChange('progress')">
          红榜
        </span>
        <span :class="rankTab == 'retrogress' ? 'actived-tab' : ''" @click="handleTabChange('retrogress')">
          黑榜
        </span>
      </div>
    </div>
    <div class="content">
      <template v-if="rankTab == 'progress'">
        <rankling-list :companyInfoList="companyInfoList" :rankTab="rankTab" type="value" title="任务差距（同比）" :titleContent="tbTitleContent" :data="progress.value" :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"/>
        <rankling-list :companyInfoList="companyInfoList" :rankTab="rankTab" type="value" title="任务差距（期初比）" :titleContent="qcbTitleContent" :data="qc_progress.value" :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"/>
      </template>

      <template v-if="rankTab == 'retrogress'">
        <rankling-list :companyInfoList="companyInfoList" :rankTab="rankTab" type="value" title="任务差距（同比）"  :titleContent="tbTitleContent" :data="retrogress.value" :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"/>
        <rankling-list :companyInfoList="companyInfoList" :rankTab="rankTab" type="value" title="任务差距（期初比）" :titleContent="qcbTitleContent" :data="qc_retrogress.value" :judgeViewEnabled="judgeViewEnabled" :judgeDetailsEnabled="judgeDetailsEnabled"/>
      </template>

    </div>
  </div>
</template>

<script>
import mixin from '../mixin'

export default {
  mixins: [mixin],
  data() {
    return {
      dsp_typ_cd_1: "2",
      dsp_typ_cd_2: "3",
      tbTitleContent: ['注：任务差距（同比）=当期的两金净值-去年同期的两金净值*（1+本年累计收入增速-两金考核指标的增速）'],
      qcbTitleContent: ['注：任务差距（期初比）=当期的两金净值-期初的两金净值*（1+本年累计收入增速-两金考核指标的增速）'],
    }
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
