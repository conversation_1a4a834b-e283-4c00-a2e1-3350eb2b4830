import dayjs from "dayjs"
import { Decimal } from 'decimal.js'

import { getKbData, getLJRankData, getLJRankDescData, getLJRankQcData, getLJRankDescQcData } from "@/http/api";
import itemTip from "@/components/itemTip.vue";
import { formatNum } from "@/utils"

import RanklingList from "./RankingList.vue";

export default {
  name: "indicatorRanking",
  components: {
    RanklingList,
    itemTip,
  },
  props: {
    companyInfoList: Array,
    rankTab: String,
    dt: String,
    userId: String
  },
  data() {
    return {
      tgt_no: "A02",
      tableVisible: false,
      indexList: [
        {
          label: "利润总额",
          value: "A02",
        },
        {
          label: "营业收入",
          value: "A01",
        },
      ],
      qc_progress: {
        value: [],
        index: []
      },
      qc_retrogress: {
        value: [],
        index: []
      },
      progress: {
        value: [],
        index: []
      },
      retrogress: {
        value: [],
        index: []
      },
      sortList: [],
      showAllCompany: false
    };
  },
  watch: {
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.init()
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.updateData()
      this.updateCompanySortList()
    },
    //
    judgeViewEnabled(item, value) {
      return formatNum(Math.abs(+value)+'');

      const {
        entp_no: entpNo,
      } = item
      let cpyType, cpyTag, userIds;
      let self = []
      this.showAllCompany = false
      this.companyInfoList.forEach(item => {
        if (item.entpNo == entpNo) {
          cpyType = item.cpyType
          cpyTag = item.cpyTag
          userIds = item.userIds
        }
        if (item.userIds.includes(this.userId) && item.entpNo == '99') {
          this.showAllCompany = true
        }

        // 找出自己所在企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push({ ...item })
        }
      })

      /**
       * 允许查看数据
       * 1. 自己所在企业
       * 2. 非上市公司
       * 3. 全集团
       */
      if (
        (self && self.some(item => item.entpNo == entpNo)) ||
        (cpyType == 1) ||
        (self.some(item => item.cpyTag == '01')) || this.showAllCompany
      ) {
        return value
      }

      return '***'
    },
    judgeDetailsEnabled(entpNo) {
      let self = []

      this.companyInfoList.forEach(item => {
        // 找到用户所在的企业
        if (item.userIds && item.userIds.includes(this.userId)) {
          self.push(item)
        }
      })

      /**
       * 允许跳转详情页
       * 1. 是自己所在企业
       * 2. 自己是全集团
       */
      if (self.some(item => item.entpNo === entpNo) || self.some(item => item.cpyTag == '01')) return true

      return false
    },
    updateCompanySortList() {
      const params = {
        dt: this.dt,
        tgt_no: this.tgt_no,
        moreChooseType: "2",
        mapperInterface: ["rbcrgl"],
      }

      getKbData(params)
        .then((res) => {
          if (res.code == 200 && res.data.rbcrgl) {
            this.sortList = res.data.rbcrgl.map(item => ({
              name: item.entp_nm,
              index: item.seq_no
            }))
          }
        })
    },
    updateData(newParams = {}) {
      const params = {
        dt: this.dt,
        // mapperInterface: ["rbczbpm"],
        // moreChooseType: "1",
        // tgt_no: this.tgt_no,
        // ...newParams
      }

      getLJRankData(params)
        .then(res => {
          const data = res.data.data.map(item => {
            return {
              ...item,
              name: item.snd_entp_nm,
              value: formatNum(item.rwcj)
            }
          })
          this.progress.value = data
        })
      getLJRankQcData(params)
        .then(res => {
          const data = res.data.data.map(item => {
            return {
              ...item,
              name: item.snd_entp_nm,
              value: formatNum(item.rwcj_qc)
            }
          })
          this.qc_progress.value = data
        })

      getLJRankDescData(params)
        .then(res => {
          const data = res.data.data.map(item => {
            return {
              ...item,
              name: item.snd_entp_nm,
              value: formatNum(item.rwcj)
            }
          })
          this.retrogress.value = data
        })
      getLJRankDescQcData(params)
        .then(res => {
          const data = res.data.data.map(item => {
            return {
              ...item,
              name: item.snd_entp_nm,
              value: formatNum(item.rwcj_qc)
            }
          })
          this.qc_retrogress.value = data
        })
    },
    handleTabChange(tab) {
      this.$emit('rankTabChange', tab)
    },
    handleIndexChange(index) {
      this.tgt_no = index;
      this.updateData()
      this.updateCompanySortList()
    },
  },
}