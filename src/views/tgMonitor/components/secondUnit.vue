<template>
  <div class="groupAll">
    <public-title :title="'主要企业两金排序'" unit="亿元"></public-title>
    <div class="content" :style="{minHeight: contentHeight}">
      <van-dropdown-menu>
        <van-dropdown-item v-model="viewTypCdOder" ref="item" :options="viewTypCdOderOptions" @open="onOpen" @close="onClose" @change="onViewTypCdOder"/>
        <van-dropdown-item title-class="search_placeholder">
          <template slot="title">
            <div @click.stop></div>
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
      <rank-chart :type="viewTypCdOder" :data="chartData" @autoHeight="onContentHeight"/>
    </div>
  </div>
</template>

<script>
import publicTitle from '@/components/publicTitle.vue';
import rankChart from "./rankChart";
export default {
  name: 'secondUnit',
  components: {
    publicTitle,
    rankChart
  },
  props: {
    view_typ_cd_oder: {
      type: String,
      default: ""
    },
    oderData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    oderData: {
      deep: true,
      handler(newVal, oldVal) {
        this.chartData = JSON.parse(JSON.stringify(newVal));
      }
    },
    view_typ_cd_oder: {
      deep: true,
      immediate: true,
      handler(newVal,oldVal) {
        this.viewTypCdOder = newVal
      }
    },
  },
  data() {
    return {
      contentHeight: 300,
      chartData: [],
      isDrop: false,
      viewTypCdOder: "",
      viewTypCdOderOptions: [
        { text: '两金原值', value: "T" },
        { text: '两金净值', value: "TN" },
        { text: '应收原值', value: "R" },
        { text: '存货原值', value: "I" },
        { text: '两金计提', value: "P" },
        { text: '营业收入', value: "BI" },
      ]
    }
  },
  computed: {
 
  },
  created() {
    
  },
  mounted() {
  },
  methods: {
    onViewTypCdOder(val) {
      this.$emit('changeViewTypCdOder',val)
    },  
    onOpen() {
      this.isDrop = true;
    },
    onClose() {
      setTimeout(() => {
        this.isDrop = false;
      },200)
    },
    onContentHeight(val) {
      this.contentHeight = val + 'px';
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  box-shadow: -0.02rem 0.13rem 0.2rem 0.01rem rgba(160, 160, 161, 0.1);
  border-radius: 0.08rem;
}
.content {
  background-color: #fff;
  position: relative;
  ::v-deep .van-dropdown-item__content {
      .van-dropdown-item__option--active {
        color: #6495fb;
      }
      .van-cell__value {
        display: none;
      }
  }
  ::v-deep .van-dropdown-menu__bar {
    box-shadow: unset;
    margin-top: 2px;
    height: 40px;
    // border-bottom: 1px solid #d9d9d9;
    position: relative;
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      bottom: 0;
      width: calc(100% - 0.5rem);
      height: 1px;
      transform: translateX(0.25rem);
      background: #d9d9d9;
      box-sizing: border-box;
      z-index: 999;
    }
    .van-dropdown-menu__item:not(.van-dropdown-menu__item:nth-last-of-type(1)){
      justify-content: flex-start;
      font-weight: 1000;
      z-index: 99;
    }
    .van-dropdown-menu__title {
      margin: 0 0.25rem;
      padding: unset;
      font-size: 0.32rem;
    }
    .van-dropdown-menu__title--active {
      color: #6495fb;
    }
    .van-dropdown-menu__title::after {
      right: -20px;
      border-width: 5px;
      margin-top: -7px;
      border-color: transparent transparent #000 #000;
    }
    .van-dropdown-menu__title--active::after {
      right: -20px;
      border-width: 5px;
      margin-top: 0;
      border-color: transparent transparent currentColor currentColor;
    }
    .van-dropdown-menu__item:nth-of-type(2) {
      justify-content: flex-end;
      flex: 2;
    }
    .search_placeholder {
      width: 100%;
      height: 100%;
      div {
        width: 100%;
        height: 100%;
      }
    }
    .search_placeholder.van-dropdown-menu__title::after {
      display: none;
    }
  }

  
  ::v-deep .van-dropdown-item.van-dropdown-item--down{
    width: calc(100% - 0.4rem)!important;
    margin-left: 0.2rem!important;
  }
}
</style>
