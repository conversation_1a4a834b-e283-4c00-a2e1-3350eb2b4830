<template>
  <div>
    <groupAll :dsp_typ_cd="dsp_typ_cd" :overviewData="overviewData" @changeDspTypeCd="changeDspTypeCd" />
    <indicatorRanking :taskFinishData="taskFinishData" />
    <secondUnit :view_typ_cd_oder="view_typ_cd_oder" :oderData="oderData" @changeViewTypCdOder="changeViewTypCdOder" />
  </div>
</template>

<script>
import { getLJData, getLjMonth } from "@/http/api"
import groupAll from '../groupAll'
import indicatorRanking from '../indicatorRanking'
import secondUnit from '../secondUnit'
export default {
  name: 'tgMonitor',
  components: {
    groupAll,
    indicatorRanking,
    secondUnit
  },
  props: {
    dt: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      showPicker: false,
      overviewData: null, //两金总览数据
      taskFinishData: [], //任务完成情况数据\z
      oderData: [], //主要两金排序数据
      dsp_typ_cd: "O", //总览类型  O:原值 N:净值 P:计提
      view_typ_cd_oder: "T" //两金排序类型 T:两金原值 R:应收 I:存货 P:计提 TN:两金净值 BI:营收
    }
  },
  watch: {
    dt: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        this.initData({
          view_typ_cd_overview: this.dsp_typ_cd,
          view_typ_cd_oder: this.view_typ_cd_oder
        });
      }
    }
  },
  mounted() {
  },
  methods: {
    // 请求数据接口
    initData(data) {
      if (!this.dt) return false;
      let params = {
        dt: this.dt,
        ...data
      }
      getLJData(params).then(res => {
        if (res.code == 200) {
          let keys = Object.keys(data);
          if (keys.length ==2) {
            this.overviewData = res.data['overviewData'] || null;
            this.taskFinishData = res.data['taskFinishData'] || [];
            this.oderData = res.data['oderData'] || [];
          } else if (keys[0] == 'view_typ_cd_oder') {
            this.oderData = res.data['oderData'] || [];
          } else if (keys[0] == 'view_typ_cd_overview') {
            this.overviewData = res.data['overviewData'] || null;
          }
        }
      })
    },
    // 切换两金总览类型
    changeDspTypeCd(val) {
      this.dsp_typ_cd = val;
      this.initData({
        view_typ_cd_overview: this.dsp_typ_cd,
      });
    },
    changeViewTypCdOder(val) {
      this.view_typ_cd_oder = val;
      this.initData({
        view_typ_cd_oder: this.view_typ_cd_oder
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.zbBoard {
  background: #f6f7f9;
  height: 100%;
  font-family: "微软雅黑";
  display: flex;
  flex-direction: column;

  //日期组件
  .dateBox {
    background: #fff;
    padding: 0.2rem 0 0.3rem;

    ::v-deep .van-cell {
      display: flex;
      align-items: center;
      width: 3.2rem;
      height: 0.6rem;
      line-height: 0.58rem;
      padding: 0px 10px;
      font-size: 0.28rem;
      border-radius: 0.08rem;
      border: 0.01rem solid #3e7bfa;
      background-color: #F1F6FF;
      color: rgba(62, 123, 250, 1);
      margin: 0 auto;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      position: relative;

      ::after {
        content: "";
        position: absolute;
        color: red;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("../../../../assets/img/rlIcon.png") no-repeat;
        background-size: cover;
        display: block;
        width: 0.28rem;
        height: 0.28rem;
      }
    }

    ::v-deep [class*="van-hairline"]::after {
      border: none;
    }

    ::v-deep .van-field__control:read-only {
      color: rgba(62, 123, 250, 1);
    }

    ::v-deep .van-cell-group {
      background-color: #fff;
      // position: inherit;
    }

    ::v-deep .van-field__control::placeholder {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item {
      color: rgba(34, 34, 34, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected {
      color: rgba(62, 123, 250, 1);
      font-size: 0.4rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected .van-ellipsis {
      background-color: #f1f6ff;
      width: 2.8rem;
      text-align: center;
      padding: 0.1rem 0;
    }

    ::v-deep .van-picker__confirm {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
  }

  .tabsBox {
    flex: 1;
    overflow: auto;
    padding: 0.35rem 0.2rem;
    ;
  }
}
</style>
