<template>
  <div class="groupAll">
    <div class="p_header">
      <div class="search_type">
        <span class="search_type_btn" :class="[dspTypCd =='O' ? 'active' : '']" @click.stop="onDataTypeChange('O')">原值</span>
        <span class="search_type_btn" :class="[dspTypCd =='N' ? 'active' : '']" @click.stop="onDataTypeChange('N')">净值</span>
        <span class="search_type_btn" :class="[dspTypCd =='P' ? 'active' : '']" @click.stop="onDataTypeChange('P')">计提</span>
      </div>
      <span class="unit">单元：亿元</span>
    </div>
    <!-- 两金总览 -->
    <div class="overview">
      <span class="label">两金总览</span>
      <span class="value">{{ formatAmount(dataForm.ljzl) }}</span>
    </div>
    <!-- 应收账款 -->
    <div class="tg_c tg_one">
      <div class="tg_ov">
        <span class="label">应收账款</span>
        <span class="value">{{ formatAmount(dataForm.ysje) }}</span>
      </div>
      <div class="box">
        <div class="commanBox">
          <p>期初</p>
          <h3>{{ formatAmount(dataForm.ysqc) }}</h3>
        </div>
        <div class="commanBox">
          <p>期初比</p>
          <h3>{{ dataForm.ysqcb }}%</h3>
        </div>
        <div class="commanBox">
          <p>同期</p>
          <h3>{{ formatAmount(dataForm.ystq) }}</h3>
        </div>
        <div class="commanBox">
          <p>同比</p>
          <h3>{{dataForm.ystqb}}%</h3>
        </div>
      </div>
    </div>
    <!-- 存货 -->
    <div class="tg_c tg_two">
      <div class="tg_ov">
        <span class="label">存货</span>
        <span class="value">{{ formatAmount(dataForm.chje) }}</span>
      </div>
      <div class="box">
        <div class="commanBox">
          <p>期初</p>
          <h3>{{ formatAmount(dataForm.chqc) }}</h3>
        </div>
        <div class="commanBox">
          <p>期初比</p>
          <h3>{{ formatAmount(dataForm.chqcb) }}%</h3>
        </div>
        <div class="commanBox">
          <p>同期</p>
          <h3>{{ formatAmount(dataForm.chtq) }}</h3>
        </div>
        <div class="commanBox">
          <p>同比</p>
          <h3>{{ formatAmount(dataForm.chtqb) }}%</h3>
        </div>
      </div>
    </div>
    <!-- 应收比率 -->
    <div class="sybl" v-show="dspTypCd !=='P'">
      <div class="sybl_item">
        <span class="label">{{ `营收增长\n率` }}</span>
        <span class="value"> {{ formatAmount(dataForm.yszzl) }} %</span>
      </div>
      <div class="sybl_item">
        <span class="label">{{ `两金增长\n率` }}</span>
        <span class="value"> {{ formatAmount(dataForm.ljzzl) }} %</span>
      </div>
      <div class="sybl_item">
        <span class="label">{{ `营收与两\n金增速差` }}</span>
        <span class="value" :style="{color: getTextColor(dataForm.zsc)}"> {{ formatAmount(dataForm.zsc) }} %</span>
      </div>
    </div>
  </div>
</template>

<script>
import { formatAmount } from '@/utils/chart';
export default {
  name: 'groupAll',
  props: {
    dsp_typ_cd: {
      type: String,
      default: ""
    },  
    overviewData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    dsp_typ_cd: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        this.dspTypCd = newVal;
      }
    },
    overviewData: {
      deep: true,
      handler(newVal, oldVal) {
        if(newVal) {
          this.dataForm = JSON.parse(JSON.stringify(newVal))
        } else {
          this.reset()
        }
      }
    }
  },
  data() {
    return {
      dspTypCd: "O",
      dataForm: {
        ljzl: '-', //两金总览
        ysje: '-', //应收账款
        ysqc: '-', //应收账款-期初
        ysqcb: '-', //应收账款-期望比
        ystq: '-', //应收账款-同期
        ystqb: '-', //应收账款-同比
        chje: '-', //存货
        chqc: '-', //存货-期初
        chqcb: '-', //存货-期望比
        chtq: '-', //存货-同期
        chtqb: '-', //存货-同比
        yszzl: '-', //营收增长率
        ljzzl: '-', //两金增长率
        zsc: '-', //营收与两金增速差
      }
    }
  },
  created() {
  },
  methods: {
    formatAmount,
    reset() {
      this.dataForm = {
        ljzl: '-', //两金总览
        ysje: '-', //应收账款
        ysqc: '-', //应收账款-期初
        ysqcb: '-', //应收账款-期望比
        ystq: '-', //应收账款-同期
        ystqb: '-', //应收账款-同比
        chje: '-', //存货
        chqc: '-', //存货-期初
        chqcb: '-', //存货-期望比
        chtq: '-', //存货-同期
        chtqb: '-', //存货-同比
        yszzl: '-', //营收增长率
        ljzzl: '-', //两金增长率
        zsc: '-', //营收与两金增速差
      }
    },
    // 初始化页面
    getTextColor(item){
      if(item == '-') return '#2c2c2c';
      if(!item) return '#008AE6'
      if(item > 0) {
        return '#32CD91'
      } else if(item < 0){
        return '#E60012'
      }
    },
    onDataTypeChange(val) {
      this.$emit("changeDspTypeCd",val)
      if(val=='P'){
        $(".overview .label").text('计提合计')
      }else{
        $(".overview .label").text('两金总览')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  padding: 0.3rem;
  box-sizing: border-box;
  background: url("../../../assets/img/bj-两金背景.png") 100% no-repeat;
  background-size: cover;
  box-shadow: -0.02rem 0.13rem 0.2rem 0.01rem rgba(160, 160, 161, 0.1);
  border-radius: 0.08rem;
  .p_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search_type {
      .search_type_btn {
        display: inline-block;
        padding: 0.08rem 0.29rem;
        background: #f4faff;
        box-shadow: 0px 4px 8px 0px rgba(66,122,225,0.12), 0px 0px 4px 0px rgba(66,122,225,0.08);
        border-radius: 4px;
        border: 1px solid #FFFFFF;
        margin-right: 0.15rem;
        color: #a5c2ff;
        font-weight: 600;
        font-size: 0.25rem;
      }
      .search_type_btn:nth-last-of-type(1) {
        margin-right: unset;
      }
      .active {
        background: #3e7bfa;
        color: #fff;
        box-shadow: unset;
        border-width: 0;
      }
    }
    .unit {
      font-size: 0.3rem;
      color: #666666;
    }
  }
  // 两金总览
  .overview {
    margin: 0.45rem 0 0.2rem;
    .label {
      font-size: 0.35rem;
      font-weight: 500;
    }
    .value {
      font-size: 0.35rem;
      font-weight: 1000;
      margin-left: 0.2rem;
    }
  }
  // 应收账款-存货
  .tg_c {
    .tg_ov {
      padding-top: 0.1rem;
      .label::before {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        border: 0.07rem solid #3e7bfa;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .label {
        font-size: 0.33rem;
        position: relative;
        padding-left: 0.35rem;
      }
      .value {
        font-size: 0.33rem;
        font-weight: 1000;
        margin-left: 0.2rem;
      }
    }

    .box {
      margin-top: 0.1rem;
      display: flex;
      text-align: center;
      .commanBox {
        flex: 1;
        display: flex;
        flex-direction: column;//纵向排列
        justify-content: center;
        padding: 0.3rem 0 0;
        position: relative;
        p:first-child {
          font-size: 0.29rem;
          color: rgba(113, 115, 122, 1);
        }
        p:last-child {
          font-size: 0.2rem;
          color: rgba(34, 34, 34, 1);
        }
        h3 {
          font-size: 0.3rem;
          color: rgba(34, 34, 34, 1);
          font-weight: 400;
          padding:.3rem 0;
        }
      }
      .commanBox:not(:nth-last-of-type(1)) {
        &::after {
          content: '';
          position: absolute;
          border-left: 1px dashed #999999;
          width: 1px;
          height: .66rem;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
  // 应收增长率
  .sybl {
    padding: 0.1rem 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sybl_item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .label {
        white-space: pre-wrap;
        text-align: center;
        font-size: 0.33rem;
      }
      .value {
        margin-top: 0.2rem;
        font-size: 0.33rem;
        font-weight: 700;
      }
    }
  }
}
</style>
