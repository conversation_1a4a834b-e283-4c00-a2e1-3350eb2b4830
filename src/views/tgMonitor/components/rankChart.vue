<template>
  <div :style="{position: 'absolute',top: '0',width: '100%',padding: '0 0.25rem',minHeight:'calc(100vh - 180px)',backgroundColor: '#fff'}">
    <div ref="rankChartRef" :style="{width: '100%',height: 'calc(100vh - 200px)'}"></div>
    <div v-show="!data.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>

<script>
import { findLargest, formatAmount } from '@/utils/chart';
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    type: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  watch: {
    type: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if(newVal != 'P') {
          this.name = [{key: 'dq',text: '当期'},{key: 'tq',text: '同期'}];
          this.rateText = "同比"
        } else {
          this.name = [{key: 'dq',text: '当期'},{key: 'tq',text: '期初'}];
          this.rateText = "期初比"
        }
      }
    },
    data: {
      deep: true,
      handler: function (data) {
        this.chartData = [];
        this.chartDataCopy = [];
        this.categories = [];
        this.seriesOption = [];
        if (data) {
          for (var i = 0; i < this.name.length; i++) {
            let tmpCategories = [];
            let tmpChartData = [];
            for (let j = 0; j < data.length; j++) {
              tmpCategories.push(data[j]['entp_nm'])
              tmpChartData.push(data[j][this.name[i]['key']])
              this.chartDataCopy.push(data[j][this.name[i]['key']])
            }
            this.categories.push(...tmpCategories);
            this.chartData.push(tmpChartData)
          }
          this.categories = Array.from(new Set(this.categories));
          if (this.categories) this.labelLargest = findLargest(this.categories);
          for (let i = 0; i < this.name.length; i++) {
            let obj = {
              type: 'bar',
              name: this.name[i]['text'],
              barWidth: "13px",
              showBackground: true,
              data: this.chartData[i],
              backgroundStyle: {
                color: '#F2F6FD'
              },
              itemStyle: {
                borderRadius: [0, 3, 3, 0],
                color: this.barItemColor[i],
              },
              label: {
                show: true,        // 开启显示
                distance: 5,     // 条柱之间的距离
                position: 'right',       // 数值样式
                color: '#373F6D',
                fontSize: '18px',
                formatter: (params, index) => {
                  const reg = /(\d)(?=(?:\d{3})+$)/g;
                  return formatAmount(params.value)  
                }
              }
            }
            this.seriesOption.push(obj)
          }
          this.initEcharts();
        }
      }
    },
  },
  data() {
    return {
      barItemColor: ['#3c7ffe','#6dc6fa','#929292'],
      name: [{key: 'dq',text: '当期'},{key: 'tq',text: '同期'}],
      rateText: "同比",
      categories: [],
      chartData: [],
      chartDataCopy: [],
      seriesOption: [],
      labelLargest: 0,
      myChart: null
    }
  },
  methods: {
    initEcharts() {
      // 清除 图表实例
      if(this.myChart != null && this.myChart !== '' && this.myChart !== undefined) {
        this.myChart.dispose(this.$refs.rankChartRef)
      }
      // 在组件中直接使用this.$echarts来进行操作
      this.myChart = this.$echarts.init(this.$refs.rankChartRef);
      let maxNumber = Math.max(...this.chartDataCopy.map(item =>{return Number(item || 0)}));
      // if(this.chartDataCopy.length) {
      //   // maxNumber = this.chartDataCopy.reduce((a,b)=>Number(a)+Number(b),0) / this.name.length;  //累加所有值
      //   // maxNumber = Math.max(...this.chartDataCopy.map(item =>{return Number(item)}));
      //   // console.log(maxNumber, maxValue, 'asdsad')
      //   // if((maxValue / maxNumber) > 0.6 ) maxNumber = maxNumber * 3;
      //   // if((maxValue / maxNumber) < 0.6 ) maxNumber = maxNumber * 0.55;
      // }
      // 指定图表的配置项和数据
      var option = {
        title: {
          show: false,
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          extraCssText:'z-index:2',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let zzl = this.data.filter(item => {return item.entp_nm == params[0].name})
            let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`
            if (params) {
              let str = `<div>${params[0].name}</div>`
              for(let i = 0; i < params.length; i++) {
                str += `<div>${circle}${this.barItemColor[i]}"></span>${params[i]['seriesName']}：${(params[0] ? formatAmount(params[i].data) : '暂无数据')}</div>`
              }
              if(this.type != 'P') {
                str += `<div>${circle}${this.barItemColor[2]}"></span>${this.rateText}：${ zzl[0]['zzl'] || '0.0'}%</div>`
              } else {
                str += `<div>${this.rateText}：${ zzl[0]['zzl'] || '0.0'}%</div>`
              }
              return str
            }
          },
        },
        legend: {
          show: true,
          right: 0,
          top: '10px',
          zlevel: 100,
          itemHeight: 16,
          itemWidth: 16,
          textStyle: {
            fontSize: '17px'
          }
        },
        grid: {
          top: '45px',
          left: '1%',
          right: '0.25rem',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          show: false,
          max: maxNumber + (maxNumber * 0.4)
        },
        yAxis: {
          data: this.categories,
          inverse: true, //如果数据数组倒置排序，加上此代码
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: { //修改背景线条样式
            show: false
          },
          offset: 1,
          axisLabel: {
            color: "#222222",
            fontWeight: '400',
            fontSize: '18px',
            formatter: (params, index) => {
              let label = "";
              if (this.labelLargest > 6) {
                if (params.length > 6) {
                  label = `${params.substr(0, 6)}...`
                } else {
                  label = `${params.padEnd(6, '\u3000')}`
                  label = `${label.padEnd(9, '\u0020')}`
                }
              } else if (this.labelLargest <= 6) {
                label = params.length < this.labelLargest ? params.padEnd(this.labelLargest, '\u3000') : params;
              }
              return [`{a|${index + 1}}  ${label}`].join('\n')
            },
            rich: {
              a: {
                color: "#373F6D",
                fontSize: '18px',
                align: 'left',
                width: 12
              }
            }
          }
        },
        series: this.seriesOption
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option, true);
      // 自适应高度
      let autoHeight = this.chartDataCopy.length * 25 + 0;
      this.myChart.getDom().style.height = autoHeight + "px";
      this.myChart.resize(); 
      this.$emit('autoHeight',autoHeight)
    },
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
</style>
