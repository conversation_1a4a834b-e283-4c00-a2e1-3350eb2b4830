import { getCompanyOrgInfo } from "@/http/api";
import { formatNum } from "@/utils";
import ProgressBar from "@/components/ProgressBar/index.vue";
import { getUserId } from "@/utils/index";
import { getSummary, getReceivable, getInventory } from "@/http/api";

export default {
  name: "secondUnit",
  components: {
    ProgressBar,
  },
  props: {
    dt: {
      type: String,
      default: "",
    },
    companyInfoList: Array,
  },
  watch: {
    entp_no: {
      handler(newVal) {
        this.$nextTick(() => {
          if (newVal) {
            this.$store.dispatch("commitCompanyClickTG", { active: "二级企业", currentCompany: newVal });
            this.activeTab = 0
          }
          this.updateData();
        });
      },
    },
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        // this.updateData()
        this.activeTab = 0
      }
    },
    activeTab: {
      handler(newVal) {
        if (newVal == 0) { this.getReceivable() } else if (newVal == 1) {
          this.getWarnList(newVal);

        }
      },
    },
    companyInfoList: {
      handler(newVal) {
        this.initSelfInfo();
        this.updateData()
      },
      deep: true,
    }
  },
  data() {
    return {
      selfInfo: [],
      activeTab: 0,
      entp_no: "",
      companyList: [],
      indexList: [],
      taskDetails: {},
      companyInfo: {},
      warnList: [],
    };
  },
  computed: {
    currentName() {
      const target = this.companyInfoList.find(
        (item) => item.entp_no == this.entp_no
      );
      return target ? target.entp_nm : "";
    },

    isShowRemark() {
      // 允许展示注释的企业编号
      const map = { '99001': "管理口径", '99015': "管理口径", '99043': "管理口径" }

      let text = `注：${map[this.entp_no]}`
      return {
        show: Boolean(map[this.entp_no]),
        text
      }
    },
  },
  created() {
    getUserId().then(res => {
      this.userId = res
      this.initCompanyList(res);
    })
  },
  methods: {
    formatNum,
    // 初始化页面
    updateData() {
      this.getWarnList(this.activeTab)
      this.indexList = [];

      getSummary({ dt: this.dt, name: this.currentName }).then(res => {
        if (res.code == 200 && res.data.data) {
          this.companyInfo = res.data.data
          this.indexList.push({ name: '任务差距（同比）', value: formatNum(res.data.data.rwcj), unit: "亿元", tips: ['注：任务差距（同比）=当期的两金净值-去年同期的两金净值*（1+本年累计收入增速-两金考核指标的增速）'] })
          this.indexList.push({ name: '任务差距（期初比）', value: formatNum(res.data.data.rwcj_qc), unit: "亿元", tips: ['注：任务差距（期初比）=当期的两金净值-期初的两金净值*（1+本年累计收入增速-两金考核指标的增速）'] })
          this.indexList.push({ name: '1年以上应收原值', value: formatNum(res.data.data.rcvb_ov_1yr_orig_val), unit: "亿元" })
          this.indexList.push({ name: '其中：3年以上应收原值', value: formatNum(res.data.data.rcvb_ov_3yr_orig_val), unit: "亿元" })
          this.indexList.push({ name: '1年以上存货原值', value: formatNum(res.data.data.mat_ov_1yr_orig_val), unit: "亿元" })
          this.indexList.push({ name: '其中：3年以上存货原值', value: formatNum(res.data.data.mat_ov_3yr_orig_val), unit: "亿元" })
          this.indexList.push({ name: '应收账款周转率', value: formatNum(res.data.data.rcvb_orig_val_tnr_rat), unit: "次/年", tips: ["应收账款周转率=年化营业收入/平均应收账款原值"] })
          this.indexList.push({ name: '存货周转率', value: formatNum(res.data.data.rcvb_orig_tnr_rat), unit: "次/年", tips: ["存货周转率=年化营业成本/平均存货原值"] })
        } else {
          this.companyInfo = {}
        }
      })

    },
    getReceivable() {
      getReceivable({ dt: this.dt, org_no: this.entp_no }).then(res => {
        if (res.code == 200) {
          this.warnList = res.data.data
          let total = 0;
          this.warnList.forEach(item => {
            item.rsk_amt = item.rsk_amt.replaceAll(" ", "")
            total += parseFloat(item.rsk_amt.replaceAll(",", ""))
            this.$set(item, 'showCust_num', false)
            this.$set(item, 'showMat_nm', false)
            this.$set(item, 'showRsk_typ_nm', false)
            this.$set(item, 'showEntp_nm', false)
          })
          //转为千分位计数法
          const formattedTotal = total.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          });
          // if (this.warnList.length > 0) {
          //   this.warnList.push({ cust_nm: "合计", rsk_amt: formattedTotal })

          // }
        }
      })
    },
    getInventory() {
      getInventory({ dt: this.dt, org_no: this.entp_no }).then(res => {
        if (res.code == 200) {
          this.warnList = res.data.data;
          let total = 0;
          this.warnList.forEach(item => {
            item.rsk_amt = item.rsk_amt.replaceAll(" ", "")
            this.$set(item, 'showCust_num', false)
            this.$set(item, 'showMat_nm', false)
            this.$set(item, 'showRsk_typ_nm', false)
            this.$set(item, 'showEntp_nm', false)
            // this.$set(item, 'rsk_amt', '9,999,999,999')
            //转为浮点型小数
            total += parseFloat(item.rsk_amt.replaceAll(",", ""))
          })
          //转为千分位计数法
          const formattedTotal = total.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          });

          // if (this.warnList.length > 0) {
          //   this.warnList.push({ rsk_typ_mean: "合计", rsk_amt: formattedTotal })

          // }
        }
      })
    },
    getWarnList(num) {
      this.warnList = [];
      if (num == 0) {
        this.getReceivable()
      } else if (num == 1) {
        this.getInventory()
      }

    },
    toggleSelect(entp_no) {
      // 如果非集团整体且不是自己所在企业则不能看数据
      if (this.selfInfo.every(item => item.entp_no !== entp_no)) return;

      this.entp_no = entp_no;
      this.$refs.dropDwonRef.toggle();
    },
    initSelfInfo() {
      if (!this.companyInfoList) return;

      // 清空之前的数据，避免累积
      this.selfInfo = [];

      this.companyInfoList.forEach((item) => {
        this.companyList.forEach(element => {
          if (item.entp_no == element.entp_no || element.entp_no == '99') {
            this.selfInfo.push(item);
          }
        })

      });

      this.initDefault();
    },
    initDefault() {
      // 优先使用路由传入
      if (this.$store.state.companyClickTG) {
        this.entp_no = this.$store.state.companyClickTG.currentCompany;
      } else {
        const selfFisrt = this.selfInfo[0] ? this.selfInfo[0].entp_no : ''
        // 集团整体默认展示第一个，其他二级企业默认展示自己所在企业
        if (this.selfInfo.some(item => item.isAll)) {
          // 如果是全集团权限，需要确保选择的是二级企业而不是集团企业
          if (this.companyList && this.companyList.length > 0) {
            // 过滤掉集团企业编号（'99'），选择第一个二级企业
            const secondCompany = this.companyList.filter(item => item.entp_no !== '99')
            this.entp_no = secondCompany && secondCompany.length > 0 ? secondCompany[0].entp_no : ''
          } else {
            this.entp_no = ''
          }
        } else {
          this.entp_no = selfFisrt
        }
      }
    },
    initCompanyList(userId) {
      getCompanyOrgInfo({
        userId: userId,
      }).then((res) => {
        // 拿到二级企业列表，初始化首选的二级企业
        this.companyList = res.data.data;
        this.initSelfInfo();
      });
    },
    showTips(content, title) {
      if (content) {
        this.remarkVisible = true;
        this.content = content;
        this.title = `${title}计算公式`;
      }

    },
    getTextColor(num) {
      if (num > 0) {
        return 'other-value-up'

      } else if (num < 0) {
        return 'other-value-down'
      } else {
        return ""
      }
    },
    absData(value, index) {
      if (value) {
        if (value === '0.000') {
          return '0'
        } else {
          if (index == 0 || index == 1) {
            return formatNum(Math.abs(+value) + '');
          } else {
            return value
          }

        }
      } else {
        return '-'
      }

    }
  },
}