<template>
  <div class="groupAll">
    <div class="rwwcqk">
      <div class="rwwcqk_box" v-if="warnList.length">
        <div class="rwwcqk_hd rwwcqk_tb">
          <span class="rwwcqk_ct_item0">法人企业</span>
          <span class="rwwcqk_ct_item1" v-if="activeTab === 0">客户名称</span>
          <span class="rwwcqk_ct_item1" v-if="activeTab === 1">存货类型</span>
          <span class="rwwcqk_ct_item1" v-if="activeTab === 1">具体明细</span>
          <!-- <span class="rwwcqk_ct_item1" v-if="activeTab === 0">风险金额(万元)</span> -->
          <van-popover v-model="showPopover" trigger="click" placement="top" :offset="[0, 16]">
            <span class="rwwcqk_ct_item1"> 风险金额(万元)</span>

            <template #reference>
              <span class="rwwcqk_ct_item1">风险金额(万元)</span>
            </template>

          </van-popover>

          <span class="rwwcqk_ct_item1">风险类型</span>
        </div>
        <div class="header" @scroll="scroll">
          <div class="rwwcqk_ct rwwcqk_tb" v-for="(item, index) in warnList" :key="index">


            <van-popover v-model="item.showEntp_nm" trigger="click" placement="top" :offset="[0, 16]">
              <div class="cust_nm">{{ item.entp_nm
                }}</div>

              <template #reference>
                <span class="rwwcqk_ct_item1">{{ item.entp_nm }}</span>
              </template>

            </van-popover>
            <van-popover v-if="activeTab === 0"  v-model="item.showCust_num" trigger="click" placement="top" :offset="[0, 16]">
              <div class="cust_nm">{{ item.cust_nm
                }}</div>

              <template #reference>
                <span class="rwwcqk_ct_item1" v-if="activeTab === 0">{{ item.cust_nm
                  }}</span>
              </template>

            </van-popover>
            <span class="rwwcqk_ct_item1" v-if="activeTab === 1">{{ item.rsk_typ_mean
              }}</span>
            <van-popover v-model="item.showMat_nm" trigger="click" placement="top" :offset="[0, 16]"
              v-if="activeTab === 1 &&warnList.length > index + 1">
              <div class="cust_nm">{{ item.mat_nm
                }}</div>

              <template #reference>
                <span class="rwwcqk_ct_item1" v-if="activeTab === 1 && warnList.length > index + 1">{{ item.mat_nm
                  }}</span>
              </template>

            </van-popover>


            <span class="rwwcqk_ct_item0"
              :style="{ fontSize: shouldShrinkFont(item.rsk_amt)&&warnList.length > index + 1 ? '0.17rem' : '0.2rem' }">{{
                item.rsk_amt
              }}</span>
            <van-popover v-model="item.showRsk_typ_nm" trigger="click" placement="top" :offset="[0, 16]">
              <div class="cust_nm">{{ item.rsk_typ_nm
                }}</div>

              <template #reference>
                <span class="rwwcqk_ct_item1">{{ item.rsk_typ_nm
                  }}</span>
              </template>

            </van-popover>

          </div>
        </div>

      </div>
      <div class="rwwcqk_box nodata" v-else>
        <img src="@/assets/img/ranking/second_task_no_data.png">
        <p :style="{ fontSize: '0.28rem' }">{{activeTab==0?"未有涉诉、逾期及账龄超3年的数据":"未有库龄超3年的数据"}}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'tableList',
  props: {
    warnList: {
      type: Array,
      default: () => {
        return []
      }
    },
    activeTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showPopover: false,
    }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    shouldShrinkFont(text) {
      return text && text.length > 4;
    },
    scroll() {

      this.warnList.forEach(item => {
        this.$set(item, 'showCust_num', false)
        this.$set(item, 'showMat_nm', false)
        this.$set(item, 'showRsk_typ_nm', false)
        this.$set(item,"showEntp_nm",false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.groupAll {
  box-shadow: -0.02rem 0.13rem 0.2rem 0.01rem rgba(160, 160, 161, 0.1);
  border-radius: 0.08rem;

  .rwwcqk {

    padding: 0.3rem;
    background: #fff;
    width: 100%;
    box-sizing: border-box;

    .rwwcqk_box {
      .header {
        max-height: 5rem;
        overflow: scroll;
      }

      .rwwcqk_ct {
        font-size: 0.2rem;

      }
      .van-popover__wrapper{
        flex: 1;
      }
      .rwwcqk_tb {
        color: #303030;
        background: #fff;
        padding: 0.15rem 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        border-bottom: 1px solid #dcdcdc;

        span {
          padding: 0 0.1rem;
        }

        .rwwcqk_ct_item0 {
          flex: 1;
          text-align: right;
          word-break: break-all;
        }

        ::v-deep .rwwcqk_ct_item1 {
          flex: 1;
          // width: 1.2rem;
          text-align: left;
          word-break: break-all;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          /* 限制显示两行 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;

          /* 超出部分显示省略号 */
        }
      }

      .rwwcqk_tb:nth-last-of-type(1) {
        border-bottom: unset;
      }

      .rwwcqk_hd {
        font-size: 0.2rem;

        color: #434343;
        background: #ebf0fa;
        border-bottom: none;

        span:not(span:nth-last-of-type(1)) {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            border-right: 0.03rem solid #afb0b2;
            width: 0.1rem;
            height: 0.3rem;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        .rwwcqk_ct_item0 {
          text-align: center;
        }

        .rwwcqk_ct_item1 {
          text-align: center;
        }
      }

    }

    .nodata {
      display: flex;
      align-items: center;
      flex-direction: column;

      img {
        width: 2.4rem;
        margin-top: 0.4rem;
        margin-bottom: 0.7rem;
      }
    }

    .rwwcqk_box:not(.nodata) {
      border: 1px solid #dcdcdc;
    }
  }
}
</style>
