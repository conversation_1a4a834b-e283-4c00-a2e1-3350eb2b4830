<template>
  <div>
    <van-popup v-if="type=='item'" :closeable="closeable" v-model="open" position="top" :style="{ width: '100%',maxHeight: '55vh',overflow: 'auto', backgroundImage: 'linear-gradient(to bottom, #6173FE 0%,#7A9EFF 100%)',padding: '20px 20px 0 20px'}" @close="handleClose">
      <p v-for="(item,index) in content" :key="index" :style="{color:'#fff',fontSize:'0.34rem',marginBottom: '20px'}">{{item}}</p>
    </van-popup>
    <van-popup v-else-if="type=='card'" :closeable="closeable" v-model="open" position="center" :style="{ width: '88%',padding: '0.5rem',borderRadius: '5px',maxHeight: '55vh',overflow: 'auto'}" @close="handleClose">
      <div style="line-height:0.5rem" :class="[showCircle?'show_circle':'']">
        <h4 :style="{color: '#222222',fontSize: '0.42rem', fontWeight: '600', marginBottom: '0.2rem'}">{{title}}</h4>
        <p v-for="(item,index) in content" :key="index" :style="{color:'#222',fontSize:'0.3rem',margin: '0 0',fontWeight: 'Regular',whiteSpace:'pre-wrap'}">{{item}}</p>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'ItemTip',
  props: {
    type: {
      type: String,
      default: 'item'
    },
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    content: {
      type: Array,
      default: () => {
        return []
      }
    },
    showCircle: {
      type: Boolean,
      default: false
    },
    closeable: Boolean
  },
  watch: {
    show: {
      handler: function (val) {
        this.open = val;
      }
    },
  },
  data() {
    return {
      open: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('handleClose')
    }
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
  ::v-deep .van-popup {
    border-radius: 0 0 10px 10px;
    top: calc(env(safe-area-inset-top) + 5vh);

    &.van-popup--center {
      top: calc(env(safe-area-inset-top) + 5vh + 40%);
  }
}
.show_circle {
  text-indent: 0.3rem;
  h4 {
    position: relative;
  }
  h4::before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border: 0.08rem solid #3e7bfa;
    border-radius: 50%;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
