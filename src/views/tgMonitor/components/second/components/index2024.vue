<template>
  <div class="secondUnit">
    <div class="content">
      <div class="header">
        <div class="header-top">
          <div class="header-top-left">
            <van-dropdown-menu>
              <van-dropdown-item :title="currentName" ref="dropDwonRef">
                <ul>
                  <li v-for="item in companyInfoList" :key="item.entp_no" :label="item.entp_nm" :value="item.entp_no"
                    :class="{
                      selected: item.entp_no == entp_no,
                      disabled:
                        selfInfo.every(selfItem => selfItem.entp_no !== '99') && selfInfo.every(selfItem => selfItem.entp_no !== item.entp_no),
                    }" @click="toggleSelect(item.entp_no)">
                    <span :class="item.entp_nm && item.entp_nm.length > 5
                      ? item.entp_nm.length > 7
                        ? 'size-7'
                        : 'size-5'
                      : ''
                      ">{{ item.entp_nm }}</span>
                  </li>
                </ul>
              </van-dropdown-item>
            </van-dropdown-menu>
            <p class="title-remark"
              :style="{ opacity: isShowRemark.show ? 1 : 0, marginTop: isShowRemark.show ? '0.4rem' : '0.2rem' }">{{
                isShowRemark.text }}</p>


          </div>

          <img src="../../../../../assets/img/ranking/second_top_icon.png" class="header-top-right" />
        </div>
        <div>
          <div class="title">两金净值合计</div>

          <div class="value value-1">
            <span class="value-text">{{ formatNum(companyInfo.cur_rcvb_invtr_amt || "-") }}</span>
            <span class="value-unit">{{ companyInfo.cur_rcvb_invtr_amt ? '亿元' : '' }}</span>

          </div>
        </div>
        <div class="other">
          <div class="other-item">
            <span class="label">同比</span>
            <span class="other-value" :class="getTextColor(companyInfo.tsp_rcvb_invtr_net_rate)">{{
              formatNum(companyInfo.tsp_rcvb_invtr_net_rate || '-', undefined, 'ratio')
              }}</span>
            <span class="other-value-unit">{{ companyInfo.tsp_rcvb_invtr_net_rate ? '%' : '' }}</span>
          </div>
        </div>
        <div class="other">
          <div class="other-item">
            <span class="label">考核目标</span>
            <span class="label-value">{{ companyInfo.khmb }}</span>
          </div>
        </div>
      </div>
      <div v-if="indexList.length > 0" class="index-card">
        <div class="index-item" v-for="(item, index) in indexList" :key="index">
          <div @click="showTips(item.tips, item.title ? item.title : item.name)">
            <span class="index-item-name">{{ item.name }}</span>
            <img src="../../../../../assets/img/ranking/icon_remark.png" alt="" v-show="item.tips" />
          </div>
          <div class="index-item-tail">
            <span class="index-item-prefix" v-if="index == 0 || index == 1">
              {{ (item.value > 0) ? '低于任务' : (item.value === 0 ? '完成任务' : '优于任务') }}
            </span>
            <span class="index-item-value">{{
              absData(item.value, index)
            }}</span>
            <span class="index-item-unit" :style="item.unitStyle">{{ item.unit }}</span>
          </div>
        </div>
      </div>

      <div class="task-card">
        <div class="title">风险预警</div>
        <van-tabs type="card" color="#3e7bfa" v-model="activeTab" title-active-color="#fff" class="van-tabs">
          <van-tab title="应收">
            <tableList :warnList="warnList" :activeTab="activeTab" />
          </van-tab>
          <van-tab title="存货">
            <tableList :warnList="warnList" :activeTab="activeTab" />
          </van-tab>

        </van-tabs>
        <div>


        </div>
      </div>
    </div>
    <item-tip :type="'card'" :show="remarkVisible" @handleClose="remarkVisible = false" :title="title"
      :content="content" :showCircle="false" closeable />
  </div>
</template>

<script>
import mixin from '../mixin.js'
import tableList from './tableList.vue';
import itemTip from "./itemTip.vue";

export default {
  mixins: [mixin],
  components: {
    tableList,
    itemTip
  },
  data() {
    return {
      rateData: {
        key: "csh_rate",
        name: "营业现金比率",
      },
      remarkVisible: false,
      content: [],
      title: ""
    }
  }
}
</script>

<style src="../index.scss" lang="scss" scoped />
