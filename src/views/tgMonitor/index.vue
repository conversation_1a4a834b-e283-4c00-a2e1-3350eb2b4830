<template>
  <div class="zbBoard" :class="containerClass">
    <div class="dateBox">
      <van-cell-group>
        <van-field style="text-align:center" readonly clickable placeholder="请选择日期" :value="dateValue"
          @click="showPicker = true" />
        <van-popup v-model="showPicker" round position="bottom" :lazy-render='false'>
          <van-picker ref="dtPickerRef" title="请选择日期" show-toolbar :columns="columns"
            :columns-field-names="customFieldName" @confirm="yearConfirm" @cancel="cancel" />
        </van-popup>
      </van-cell-group>
    </div>
    <div class="tabsBox">
      <!-- tab区域 -->
      <van-tabs v-model="active" swipeable>
        <van-tab v-for="(item, index) in tabList" :title="item" :key="index" :name="item" />
      </van-tabs>
      <!-- 内容区域 -->
      <div v-if="dt" class="tabContent">
        <div v-if="active == '集团整体'">
          <Overall :dt="dt" />
        </div>
        <div v-if="active == '指标排名'">
          <Ranking :dt="dt" :rankTab="rankTab" @rankTabChange="rankTabChange" :companyInfoList="companyInfoList" />
        </div>
        <div v-if="active == '二级企业'">
          <second :dt="dt" :companyInfoList="companyInfoList" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCompanyAll, getLjMonth, getCompanyOrgInfo } from "@/http/api";
import { getUserId } from "@/utils/index";

import Overall from './components/Overall/index.vue'
import Ranking from './components/Ranking/index.vue'
import Second from "./components/second/index.vue"
export default {
  name: 'tgMonitor',
  components: {
    Overall,
    Ranking,
    Second
    // groupAll,
    // indicatorRanking,
    // secondUnit
  },
  data() {
    return {
      tabList: [,
        "指标排名",
        "二级企业"
      ],
      companyInfoList: [],
      active: "指标排名",
      dateValue: "",
      rankTab: 'progress',
      dt: "",
      showPicker: false,
      columns: [],
      customFieldName: {
        text: 'text',
        children: 'children',
      },
      monthList: [
        { text: '1月' }, { text: '1-2月' }, { text: '1-3月' }, { text: '1-4月' }, { text: '1-5月' }, { text: '1-6月' },
        { text: '1-7月' }, { text: '1-8月' }, { text: '1-9月' }, { text: '1-10月' }, { text: '1-11月' }, { text: '1-12月' }
      ],
      ljMonthM: [],
      cecAll: false
    }
  },
  watch: {
    active: {
      deep: true,
      handler(newVal, oldVal) {
        this.columns = this.getComputedMonth()
        let el = document.querySelector('.tabsBox .tabContent');
        el.scrollTo(0, 0)

        let path = `${this.$route.path}?active=${newVal}`

        this.$router.replace({
          path
        })
      }
    },
    dt: {
      deep: true,
      handler(newVal, oldVal) {
        this.loadCompanyInfoList();
        if (new Date(newVal) >= new Date('2025-04')) {
          if (this.cecAll) {
            this.tabList = ["集团整体", "指标排名", "二级企业"]
          } else {
            this.tabList = ["指标排名", "二级企业"]
          }

        } else {
          if (this.cecAll) {
            this.tabList = ["集团整体"]
          } else {
            this.tabList = ["暂无权限"]
          }

        }
      },
    },
    companyClickTG: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.active = newVal.active
        }
      }
    }
  },
  created() {

    getUserId().then(res => {
      this.getCompanyOrgInfo(res);
    })
    
    if (this.dt) {
      this.loadCompanyInfoList();
    }
  },
  mounted() {
  },
  computed: {
    containerClass() {
      switch (this.active) {
        case "集团整体": {
          return 'zbBoard-all'
        }
        case "指标排名": {
          return this.rankTab == 'progress' ? 'zbBoard-rank zbBoard-rank-progress' : 'zbBoard-rank zbBoard-rank-retrogress'
        }
        case "二级企业": {
          return 'zbBoard-second'
        }
        default: {
          return ''
        }
      }
    },
    companyClickTG() {
      return this.$store.state.companyClickTG
    }
  },
  methods: {
    getCompanyOrgInfo(userId) {
      getCompanyOrgInfo({ userId: userId }).then(res => {
        if (res.code == 200) {
          let companyList = res.data.data
          if (companyList.length > 0 && companyList[0].entp_no == '99') {
            this.tabList = ["集团整体", "指标排名", "二级企业"]
            this.cecAll = true
          
          } else {
            this.tabList = ["指标排名", "二级企业"]
          }
          this.createDateOptions();
        }
      })
    },
    rankTabChange(tab) {
      this.rankTab = tab
    },
    loadCompanyInfoList() {
      getCompanyAll({ dt: this.dt }).then((res) => {
        if (res.data.data) {
          this.companyInfoList = res.data.data
        }
      })
    },
    //年选择器
    yearConfirm(value) {
      let month = this.monthList.findIndex(item => {
        return item.text == value[1]
      }) + 1;
      if (month < 10) month = `0${month}`
      this.dt = `${value[0]}-${month}`
      this.dateValue = `${value[0]}年 ${value[1]}`
      this.showPicker = false;
    },
    //点击取消按钮时触发的事件
    cancel() {
      this.showPicker = false
    },
    async createDateOptions() {
      let columns = [];
      await this.handleGetJjMonth().then(res => {
        columns = res;
      }).catch(err => {
        columns = this.createDtOption()
      })
      this.columns = columns;
      let maxMonthText = columns[0]['children'][columns[0]['children'].length - 1]['text'];
      let maxYear = columns[0]['text'];
      let maxMonth = this.monthList.findIndex(item => {
        return item.text == maxMonthText
      }) + 1;
      this.dateValue = `${maxYear}年 ${maxMonthText}`;
      this.dt = `${maxYear}-${maxMonth < 10 ? '0' + maxMonth : maxMonth}`;
      this.$nextTick(() => {
        this.$refs.dtPickerRef.setColumnIndex(1, maxMonth - 1)
      })
    },
    createDtOption() {
      let columns = [];
      let maxYear = new Date().getFullYear();
      let maxMonth = new Date().getMonth() + 1;
      let curMonth = this.monthList.slice(0, maxMonth)
      let minYear = '2000';
      for (let i = maxYear; i >= minYear; i--) {
        let obj = {
          text: i,
          children: i == maxYear ? curMonth : this.monthList
        }
        columns.push(obj)
      }
      return columns
    },
    handleGetJjMonth() {
      return new Promise((resolve, reject) => {
        getLjMonth().then(res => {
          if (res.code == 200) {
            this.ljMonthM = res.data;
            let columns = this.getComputedMonth()
            resolve(columns)
          } else {
            reject([])
          }
        }).catch(err => {
          reject([])
        })
      })
    },
    //计算当前用户的月份
    getComputedMonth() {
      let years = [];
      let columns = [];
      let dataList = []
      if (this.active=='集团整体') {
        dataList = this.ljMonthM;
      } else {
        dataList = this.ljMonthM.filter(item => {
          // 过滤掉2025年4月之前的数据
          return new Date(item['value']) >= new Date('2025-04-30')
        })
      }



      for (let i = 0; i < dataList.length; i++) {
        let cjdt = dataList[i]['label'].split("年")[0];
        if (!years.includes(cjdt)) years.push(cjdt)
      }
      years = years.sort((a, b) => { return b - a }); //降序排列年份
      for (let i = 0; i < years.length; i++) {
        let monthList = [];
        for (let j = 0; j < dataList.length; j++) {
          if (dataList[j]['label'].indexOf(years[i]) != -1) {
            monthList.push({
              text: this.monthList[new Date(dataList[j]['value']).getMonth()]['text']
            })
          }
        }
        let obj = {
          text: years[i],
          children: monthList
        }
        columns.push(obj)
      }
      return columns
    }
  }
}
</script>

<style lang="scss" scoped>
.zbBoard-rank {
  ::v-deep .van-tabs__nav {
    .van-tab__text {
      color: #FBD95F;
    }
  }

  &.zbBoard-rank-progress {

    ::v-deep .tabsBox {
      background: url(../../assets/img/tgMonitor/progress_bg.png);
      background-size: cover !important;
      background-repeat: no-repeat;
    }
  }

  &.zbBoard-rank-retrogress {
    ::v-deep .tabsBox {
      background: url(../../assets/img/tgMonitor/retrogress_bg.png);
      background-size: cover !important;
      background-repeat: no-repeat;
    }
  }

  ::v-deep .van-tabs--line .van-tabs__wrap {
    box-shadow: none !important;

    .van-tabs__line {
      background: linear-gradient(94deg, #FCE697 0%, #FBD95F 100%);
    }
  }
}

.zbBoard {
  background: #f6f7f9;
  height: 100%;
  font-family: "微软雅黑";
  display: flex;
  flex-direction: column;

  //日期组件
  .dateBox {
    background: #fff;
    padding: 0.2rem 0 0.3rem;

    ::v-deep .van-cell {
      display: flex;
      align-items: center;
      width: 3.2rem;
      height: 0.6rem;
      line-height: 0.58rem;
      padding: 0px 10px;
      font-size: 0.28rem;
      border-radius: 0.08rem;
      border: 0.01rem solid #3e7bfa;
      background-color: #F1F6FF;
      color: rgba(62, 123, 250, 1);
      margin: 0 auto;
      box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
      position: relative;

      ::after {
        content: "";
        position: absolute;
        color: red;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("../../assets/img/rlIcon.png") no-repeat;
        background-size: cover;
        display: block;
        width: 0.28rem;
        height: 0.28rem;
      }
    }

    ::v-deep [class*="van-hairline"]::after {
      border: none;
    }

    ::v-deep .van-field__control:read-only {
      color: rgba(62, 123, 250, 1);
    }

    ::v-deep .van-cell-group {
      background-color: #fff;
      // position: inherit;
    }

    ::v-deep .van-field__control::placeholder {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item {
      color: rgba(34, 34, 34, 1);
      font-size: 0.3rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected {
      color: rgba(62, 123, 250, 1);
      font-size: 0.4rem;
    }

    ::v-deep li.van-picker-column__item.van-picker-column__item--selected .van-ellipsis {
      background-color: #f1f6ff;
      width: 2.8rem;
      text-align: center;
      padding: 0.1rem 0;
    }

    ::v-deep .van-picker__confirm {
      color: rgba(62, 123, 250, 1);
      font-size: 0.3rem;
    }
  }

  .tabsBox {
    height: calc(100% - 1.1rem); // 100% - 日期高度

    .van-tabs {
      z-index: 99;
    }

    //tab滑动切换
    ::v-deep .van-tab {
      color: rgba(17, 17, 17, 1);
      font-size: 0.36rem;
    }

    ::v-deep .van-tab--active {
      color: #111;
      font-size: 0.36rem;
    }

    ::v-deep .van-tabs__line {
      background-color: #3e7bfa;
      width: 1rem;
      height: 0.07rem;
      border-radius: 0;
    }

    ::v-deep .van-tabs--line .van-tabs__wrap {
      box-shadow: 0px 0.03rem 0.07rem 0 #e6e6e6;
      height: 0.9rem;

      .van-tabs__nav {
        background-color: transparent !important;
      }

      .van-tabs__line {
        width: 1rem;
        height: 0.08rem;
        border-radius: 1em;
      }
    }

    .tabContent {
      height: calc(100% - 0.9rem);
      background: transparent !important;
      overflow: auto;
    }
  }
}
</style>
