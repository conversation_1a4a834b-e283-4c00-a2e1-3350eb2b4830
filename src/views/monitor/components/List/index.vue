<template>
  <div class="list-container">
    <div
      class="list-item"
      v-for="(item, index) in data"
      :key="item.entp_nm"
      @click="handleItemClick(item)"
    >
      <div class="title">
        <div class="left">
          <div class="index">{{ index + 1 }}</div>
          <div class="name">{{ item.entp_nm }}</div>
          <div class="question"></div>
        </div>
        <div
          class="right"
        >
          <div class="value-item">
            <span class="label">完成度</span>
            <span class="value" :class="item.tar_ach_flg == 1 ? 'finished' : 'unfinished'">
              {{ formatNumForMonitor(item.pft_tar_yr_pct * 100, 0, 'percent') }}
            </span>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="value-item">
          <span class="label">{{father_data.delopedOver?"考核目标":"预算建议值"}}</span>
          <span class="value">{{
            formatNumForMonitor(numChangeByUnit(item.pft_tar_yr))
          }}</span>
        </div>
        <div class="value-item">
          <span class="label">完成值</span>
          <span class="value">{{
            formatNumForMonitor(numChangeByUnit(item.pft_act))
          }}</span>
        </div>
      </div>
    </div>
    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '85%' }"
      class="modal"
    >
    <div class="modal-title">{{ details.entp_nm }}-完成进度说明</div>

    <div v-if="details.tar_ach_comm">
      <div class="content-box" v-if="details.tar_ach_comm">{{ details.tar_ach_comm }}</div>
      <div class="close-btn">
        <div class="close-btn-inner" @click="show = false">关闭</div>
      </div>
    </div>
    <div class="modal-no-data" v-else>
      <img src="../../../../assets/img/ranking/second_task_no_data.png" alt="" />
      <span>暂无进度说明</span>
    </div>
      
    </van-popup>
  </div>
</template>

<script>
import IndexBlueBorder from "@/assets/img/monitor/index-blue-border.png";
import IndexBlue from "@/assets/img/monitor/index-blue.png";
import IndexGray from "@/assets/img/monitor/index-gray.png";
import IndexRed from "@/assets/img/monitor/index-red.png";
import { formatNumForMonitor, numChangeByUnit } from "@/utils";

export default {
  props: {
    data: [],
  },
  inject:{
    father_data:{
      type:Object,
      default:()=>{
        return {}
      }
    }
  },
  data() {
    return {
      indexBG: IndexBlue,
      show: false,
      details: {},
    };
  },
  methods: {
    formatNumForMonitor,
    numChangeByUnit,
    handleItemClick(item) {
      this.show = true;
      this.details = item;
    },
  },
};
</script>

<style lang="scss" scoped>
.list-container {
  padding: 0rem 0.3rem;
  background: #ffffff;
  box-shadow: 5px 6px 20px 0px rgba(112, 112, 112, 0.3);
  border-radius: 0.16rem;
  margin: 0 .3rem;

  .list-item {
    padding: 0.5rem 0 0.3rem 0;
    border-bottom: 1px solid #e8e8e8;

    &:last-child {
      border-bottom: 0;
    }

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        min-width: 50%;
        max-width: 50%;
        display: flex;
        align-items: center;
        padding-left: 0.76rem;
        position: relative;

        .index {
          width: 0.5rem;
          height: 0.54rem;
          font-size: 0.29rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          background: url(../../../../assets/img/monitor/index-blue.png) center
            no-repeat;
          background-size: contain;
          position: absolute;
          left: 0rem;
        }

        .name {
          margin: 0 0.1rem 0 0;
          font-size: 0.35rem;
        }

        .question {
        }
      }

      .right {
        min-width: 50%;
        max-width: 50%;
        display: flex;
        justify-content: flex-end;

        .value {
          font-size: .33rem;

          &.unfinished {
            color: #32CD91;
          }

          &.finished {
            color: #EC3E3E;
          }
        }
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.3rem;
      padding-left: 0.76rem;
    }

    .value-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:first-child {
        .label {
          min-width: 1.3rem;
        }

        .value {
          min-width: 1.1rem;
        }
      }

      &:last-child {
        .label {
          min-width: 1.2rem;
        }

        .value {
          min-width: 1.6rem;
        }
      }

      .label {
        font-size: 0.25rem;
        color: #666;
      }

      .value {
        display: inline-block;
        flex: 1;
        font-size: 0.37rem;
        text-align: right;
      }
    }
  }
}

.modal {
  border-top-right-radius: .2rem;
  border-top-left-radius: .2rem;
  padding: 0.4rem 0.36rem;

  .modal-title {
    font-size: 0.33rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    &::before {
      content: " ";
      width: 0.14rem;
      height: 0.14rem;
      border-radius: 100%;
      background: #000;
      margin-right: 0.16rem;
    }
  }

  .content-box {
    background: #ecf2fb;
    border-radius: 0.06rem;
    padding: 0.3rem;
    font-size: 0.31rem;
    word-break: break-all;
    text-align: justify;
  }

  .close-btn {
    width: 90%;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    bottom: .5rem;

    .close-btn-inner {
      width: 100%;
      background-image: linear-gradient(90deg, #6baaff 0%, #3b7afd 100%);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 0.86rem;
      font-size: 0.31rem;
      border-radius: 4em;
    }
  }

  .modal-no-data {
    height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.29rem;

    img {
      width: 2.6rem;
      height: auto;
      margin-bottom: 0.6rem;
    }
  }
}
</style>
