<template>
  <div v-if="show" class="container" @touchmove.stop="onMove" @click.stop="onClick" @scroll="onScroll">
    <van-loading type="spinner" color="#1989fa" />
  </div>
</template>

<script>
export default {
  name: 'monitorLoading',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onMove(e) {
      e.preventDefault();
      e.stopPropagation();
    },
    onClick(e) {
      e.preventDefault();
      e.stopPropagation();
    },
    onScroll(e) {
      e.preventDefault();
      e.stopPropagation();
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0);
  z-index: 100000000000000;
}
</style>