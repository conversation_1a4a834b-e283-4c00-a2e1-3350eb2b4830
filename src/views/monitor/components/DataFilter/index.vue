<template>
  <div class='filter-container'>
    <div class='status-filter'>
      <div class='left'>
        <div class='status-item' :class='status.length == 2 ? `actived` : ``' @click="changeStatus(['1', '2'])">全部</div>
        <div class='status-item' :class='status.length == 1 && status[0] == "1" ? `actived` : ``' @click="changeStatus(['1'])">已完成</div>
        <div class='status-item' :class='status.length == 1 && status[0] == "2" ? `actived` : ``' @click="changeStatus(['2'])">未完成</div>
      </div>
      <div class='right'>
        <span>
          单位：亿元
        </span>
        <span class="desc">
          {{ desc }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isAll: false
  },
  data() {
    return {
      status: ['1', '2'],
      desc: ''
    }
  },
  methods: {
    changeStatus(nextStatus) {
      this.status = nextStatus
      this.$emit('change', this.status)
    },
    reset() {
      this.status = ['1', '2']
      this.$emit('change', this.status)
    }
  },
  mounted() {
    this.$emit('change', this.status)
  },
  watch: {
    status: {
      immediate: true,
      deep: true,
      handler() {
        if (this.status.length > 1) {
          this.desc = this.isAll ? '按完成值降序' : '按完成度降序'
        }else {
          if (this.status[0] ==1) {
            this.desc = '按完成度降序'
          }else {
            this.desc = '按完成度升序'
          }
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .filter-container {
    .status-filter {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;
        gap: .2rem;

        .status-item {
          min-width: 1.2rem;
          height: .6rem; 
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: .28rem;
          // box-shadow: 0px 0px 3px 0px rgba(212,212,212,1);
          box-shadow: 0px 2px 2px 0px rgba(156,156,156,0.3);
          border-radius: 5px;
          background: #fff;
          padding: 0 .1rem;

          &.actived {
            font-weight: 700;
            color: #fff;
            background: #267af9;
            box-shadow: 0px 2px 4px 0px rgba(156,156,156,0.5);
            // box-shadow: 0px 0px 6px 0px rgba(212,212,212,1);
          }
        }
      }

      .right {
        font-size: .26rem;
        display: flex;
        flex-direction: column;
      }
    }
  }
</style>