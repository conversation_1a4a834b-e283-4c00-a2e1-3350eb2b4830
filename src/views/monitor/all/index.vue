<template>
  <div class="all-container">
    <div class="search-box">
      <div class="search">
        <img v-if="!focus && !inputValue" class="search-icon" src="../../../assets/img/monitor/search.png">
        <input type="text" placeholder="企业名称" @focus="focus = true" @blur="focus = false" v-model="inputValue" />
        <div class="tail">
          <!-- <div class="delete" @click="handleDelete">x</div> -->
          <van-icon v-if="inputValue" name="cross" class="delete" @click="handleDelete"/>
          <div class="search-btn" @click="handleSearch()">搜索</div>
        </div>
      </div>
    </div>
    
    <div class="list-item" @click="show = true">
      <div class="title">
        <div class="left">
          <div class="index" />
          <div class="name">集团整体</div>
          <div class="question"></div>
        </div>
        <div class="right">
          <div class="value-item">
            <span class="label">完成度</span>
            <span
              class="value"
              :class="all.tar_ach_flg == 1 ? 'finished' : 'unfinished'"
            >
              {{
                all.pft_tar_yr_pct
                  ? formatNumForMonitor(all.pft_tar_yr_pct * 100, 1, "percent")
                  : "-"
              }}
            </span>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="value-item">
          <span class="label">{{father_data.delopedOver?"考核目标":"预算建议值"}}</span>
          <span class="value">{{
            formatNumForMonitor(numChangeByUnit(all.pft_tar_yr || "-"))
          }}</span>
        </div>
        <div class="value-item">
          <span class="label">完成值</span>
          <span class="value">{{
            formatNumForMonitor(numChangeByUnit(all.pft_act || "-"))
          }}</span>
        </div>
      </div>
    </div>

    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '85%' }"
      class="all-modal"
    >
      <div class="modal-title">集团整体-完成进度说明</div>

      <div v-if="all.tar_ach_comm">
        <div class="content-box" v-if="all.tar_ach_comm">{{ all.tar_ach_comm }}</div>
        <div class="close-btn">
          <div class="close-btn-inner" @click="show = false">关闭</div>
        </div>
      </div>
      <div class="modal-no-data" v-else>
        <img src="../../../assets/img/ranking/second_task_no_data.png" alt="" />
        <span>暂无进度说明</span>
      </div>
    </van-popup>

    <div class="top">
      <p class="target-desc">
        总共{{allNum}}户企业，{{ finishedNum }}户达成进度（超{{formatNumForMonitor(target * 100, 1)}}%）。
      </p>
      <data-filter ref="filter" :isAll="true" @change="handleStatusChange" />
    </div>
    <List :data="data" :target="target" />

    <loading :show="loading"/>
  </div>
</template>

<script>
import { getIndexData } from "@/http/api";
import { debounce } from "@/utils";
import { formatNumForMonitor, numChangeByUnit } from "@/utils";

import Loading from '../components/Loading/index.vue'
import DataFilter from "../components/DataFilter/index.vue";
import List from "../components/List/index.vue";

export default {
  components: {
    DataFilter,
    List,
    Loading
  },
  inject:{
    father_data:{
      type:Object,
      default:()=>{
        return {}
      }
    }
  },
  props: {
    dt: {
      type: String,
      default: "",
    },
    target: {
      type: Number,
      default: 0,
    },
    month: {
      type: Number,
      default: 0,
    },
    resetScroll: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      show: false,
      focus: false,
      inputValue: "",
      filter: {
        entp_name: "",
        tar_ach_flg: [],
      },
      all: {},
      allNum: '',
      finishedNum: '',
      data: [],
    };
  },
  methods: {
    formatNumForMonitor,
    numChangeByUnit,
    resetInput() {
      this.inputValue = ''
      this.filter = {
        ...this.filter,
        entp_name: ""
      }
    },
    resetStatus() {
      this.$refs.filter.reset()
    },
    handleDelete() {
      this.filter = {
        ...this.filter,
        entp_name: "",
      };
      this.inputValue = "";
      this.updateData();
    },
    handleSearch() {
      this.filter = {
        ...this.filter,
        entp_name: this.inputValue,
      };
      this.updateData();
    },
    handleStatusChange(tar_ach_flg) {
      this.filter = {
        ...this.filter,
        tar_ach_flg,
      };
      this.$nextTick(() => {
        this.updateData();
        this.resetScroll()
      })
    },
    updateData: debounce(async function () {
      if (!this.dt) return;
      this.loading = true
      const { entp_name, tar_ach_flg } = this.filter
      const data = await getIndexData({
        dt: this.dt,
        entp_lvl: "1",
        entp_name,
        tar_ach_flg,
        mapperInterface: ["enptall"],
        moreChooseType: "1",
      });
      const cecData = await getIndexData({
        dt: this.dt,
        entp_lvl: "0",
        mapperInterface: ["enptall"],
        moreChooseType: "0",
      });
      this.data = data.data.enptall;
      this.all = cecData.data.enptall[0];

      if (tar_ach_flg.length > 1) {
        this.finishedNum = data.data.enptall.reduce((prev, next) => prev + (next.tar_ach_flg == 1 ? 1 : 0), 0)
        this.allNum = data.data.enptall.length
      }
      this.loading = false
    }),
  },
};
</script>

<style lang="scss" scoped>
.all-container {
  padding-bottom: .3rem;

  .search-box {
    padding: 0 .3rem;
    padding-top: .3rem;
    position: sticky;
    top: 0;
    padding-bottom: .3rem;
    background: url(../../../assets/img/monitor/bg.png) no-repeat;
    background-size: 100% 4500px;
    background-position-y: calc(-44px - .3rem);
    z-index: 1000;

    .search {
      box-sizing: border-box;
      height: 0.8rem;
      border: 1px solid rgb(149, 153, 204);
      box-shadow: 0px 4px 4px 0px rgba(112, 112, 112, 0.13);
      border-radius: 1rem;
      font-size: .32rem;
      display: flex;
      gap: .2rem;
      align-items: center;
      justify-content: space-between;
      padding-left: 0.3rem;
      padding-right: 0.16rem;

      .search-icon {
        width: .4rem;
        height: .38rem;
      }

      input {
        width: 3.5rem;
        font-size: .3rem;
        caret-color: #2273f7 !important;
      }

      .tail {
        display: flex;
        align-items: center;
        font-size: 0.3rem;
        gap: 0.1rem;

        .delete {
          // width: .3rem;
          // height: .3rem;
          // width: 0.4rem;
          // height: 0.38rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 100%;
        }

        .search-btn {
          width: 1.3rem;
          height: 0.6rem;
          display: flex;
          justify-content: center;
          align-items: center;
          background-image: linear-gradient(90deg, #6baaff 0%, #3b7afd 100%);
          border-radius: 28px;
          font-size: 0.28rem;
          font-weight: 700;
          color: #fff;
        }
      }

      // &::before {
      //   display: inline-block;
      //   content: "1";
      //   color: transparent;
      //   width: 0.4rem;
      //   height: 0.38rem;
      //   background: url(../../../assets/img/monitor/search.png) no-repeat center;
      //   background-size: contain;
      //   margin-right: 0.1rem;
      // }

      input {
        flex: 1;
        color: #000;
        caret-color: #000;
      }
    }
  }

  .top {
    position: sticky;
    background: #fff;
    top: 1.4rem;
    z-index: 1000;
    padding: .3rem;
    padding-top: .1rem;
    background: url(../../../assets/img/monitor/bg.png) no-repeat;
    background-size: 100% 4500px;
    background-position-y: -44px;
  }

  &>.list-item {
    padding: 0.5rem 0 0.3rem 0;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.16);
    border-radius: 0.16rem;
    padding: 0.3rem;
    margin-top: 0.3rem;
    margin-bottom: 0.2rem;
    margin: 0 .3rem;

    &:last-child {
      border-bottom: 0;
    }

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        min-width: 50%;
        max-width: 50%;
        display: flex;
        align-items: center;
        padding-left: 0.76rem;
        position: relative;

        .index {
          width: 0.7rem;
          height: 0.23rem;
          font-size: 0.32rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          background: url(../../../assets/img/monitor/cec.png) center no-repeat;
          background-size: contain;
          position: absolute;
          left: 0rem;
        }

        .name {
          margin: 0 0.1rem 0 0;
          font-size: 0.38rem;
        }

        .question {
        }
      }

      .right {
        min-width: 50%;
        max-width: 50%;
        display: flex;
        justify-content: flex-end;

        .value {
          font-size: 0.36rem;

          &.unfinished {
            color: #32cd91;
          }

          &.finished {
            color: #ec3e3e;
          }
        }
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.3rem;
      padding-left: 0.76rem;
    }

    .value-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:first-child {
        .label {
          min-width: 1.3rem;
        }

        .value {
          min-width: 1.1rem;
        }
      }

      &:last-child {
        .label {
          min-width: 1.2rem;
        }

        .value {
          min-width: 1.6rem;
        }
      }

      .label {
        font-size: 0.25rem;
        color: #666;
      }

      .value {
        display: inline-block;
        flex: 1;
        font-size: 0.37rem;
        text-align: right;
      }
    }
  }

  .filter-container {
    // margin-bottom: 0.3rem;
    // margin-top: 0.5rem;
  }

  .list-container {
  }

  .target-desc {
    font-size: 0.26rem;
    margin-top: .2rem;
  }
}

.all-modal {
  border-top-right-radius: .2rem;
  border-top-left-radius: .2rem;
  padding: 0.4rem 0.36rem;

  .modal-title {
    font-size: 0.33rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    &::before {
      content: " ";
      width: 0.14rem;
      height: 0.14rem;
      border-radius: 100%;
      background: #000;
      margin-right: 0.16rem;
    }
  }

  .content-box {
    background: #ecf2fb;
    border-radius: 0.06rem;
    padding: 0.3rem;
    font-size: 0.31rem;
    word-break: break-all;
    text-align: justify;
  }

  .close-btn {
    width: 90%;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    bottom: .5rem;

    .close-btn-inner {
      width: 100%;
      background-image: linear-gradient(90deg, #6baaff 0%, #3b7afd 100%);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 0.86rem;
      font-size: 0.31rem;
      border-radius: 4em;
    }
  }

  .modal-no-data {
    height: calc(100% - 1.12rem - 1rem - 0.86rem - 0.8rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.29rem;

    img {
      width: 2.6rem;
      height: auto;
      margin-bottom: 0.6rem;
    }
  }
}
</style>
