<template>
  <div class="loss-container">
    <div class="top">
      <p class="target-desc">{{ allNum }}家亏损大户合计目标{{ allTarget }}亿，完成{{ allCurrent }}亿，{{ finishedNum }}户企业达成进度（超{{ formatNum(target * 100, 1) }}%）。</p>
      <data-filter ref="filter" @change="handleStatusChange" />
    </div>
    <List :data="data" :target="target" />
    <loading :show="loading"/>
  </div>
</template>
<script>
import { getIndexData } from "@/http/api";
import { debounce, formatNum, formatNumForMonitor, numChangeByUnit } from "@/utils";

import Loading from '../components/Loading/index.vue'
import DataFilter from "../components/DataFilter/index.vue";
import List from "../components/List/index.vue";

export default {
  components: {
    DataFilter,
    List,
    Loading
  },
  props: {
    dt: {
      type: String,
      default: "",
    },
    target: {
      type: Number,
      default: 0,
    },
    month: {
      type: Number,
      default: 0,
    },
    resetScroll: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      filter: {
        tar_ach_flg: [],
      },
      data: [],
      allNum: '',
      allTarget: '',
      allCurrent: '',
      finishedNum: ''
    };
  },
  methods: {
    formatNum,
    resetStatus() {
      this.$refs.filter.reset()
    },
    handleStatusChange(tar_ach_flg) {
      this.filter = {
        ...this.filter,
        tar_ach_flg,
      };
      this.updateData();
      this.resetScroll()
    },
    updateData: debounce(async function () {
      if (!this.dt) return;
      this.loading = true
      const { tar_ach_flg } = this.filter
      const data = await getIndexData({
        dt: this.dt,
        tar_ach_flg,
        mapperInterface: ["enptdef"],
        moreChooseType: "1",
      });
      this.data = data.data.enptdef;

      if (tar_ach_flg.length > 1) {
        this.allTarget = formatNumForMonitor(numChangeByUnit(data.data.enptdef.reduce((prev, next) => prev + Number(next.pft_tar_yr), 0)))
        this.allCurrent = formatNumForMonitor(numChangeByUnit(data.data.enptdef.reduce((prev, next) => prev + Number(next.pft_act), 0)))
        this.allNum = data.data.enptdef.length
        this.finishedNum = data.data.enptdef.reduce((prev, next) => prev + (next.tar_ach_flg == 1 ? 1 : 0), 0)
      }
      this.loading = false
    }),
  },
};
</script>

<style lang="scss" scoped>
.loss-container {
  .top {
    position: sticky;
    background: #fff;
    top: 0;
    z-index: 1000;
    padding: .3rem;
    padding-bottom: .1rem;
    background: url(../../../assets/img/monitor/bg.png) no-repeat;
    background-size: 100% 4500px;
    background-position-y: -44px;
  }

  .filter-container {
    margin-bottom: 0.3rem;
    margin-top: 0.2rem;
  }

  .list-container {
  }

  .target-desc {
    font-size: 0.26rem;
  }
}
</style>
