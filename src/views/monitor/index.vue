<template>
  <div class="container">
    <van-tabs v-model="actived">
      <van-tab title="利润大户" name="profits"
        ><profits v-if="dt" ref="profits" :target="target" :month="month" :dt="dt" :resetScroll="resetScroll"
      /></van-tab>
      <van-tab title="亏损大户" name="loss"
        ><loss v-if="dt" ref="loss" :target="target" :month="month" :dt="dt" :resetScroll="resetScroll"
      /></van-tab>
      <van-tab title="全部企业" name="all"
        ><all v-if="dt" ref="all" :target="target" :month="month" :dt="dt" :resetScroll="resetScroll"
      /></van-tab>
    </van-tabs>
  </div>
</template>

<script>
import dayjs from "dayjs";

import { getIndexLatestMonth ,getIndexData} from "@/http/api";
import { setDocumentTitle } from '@/utils/document'

import profits from "./profits/index.vue";
import loss from "./loss/index.vue";
import all from "./all/index.vue";

export default {
  components: {
    profits,
    loss,
    all,
  },
  provide(){
    return{
      father_data:this.$data
    }
    
  },
  data() {
    return {
      actived: "profits",
      dt: "",
      month: 0,
      target: 0,
      latestMonth: '',
      delopedOver:false
    };
  },
  mounted() {
    this.actived = this.$route.query.active || 'profits'
    this.changeTitle()
  },
  methods: {
    getLatestMonth() {
      return new Promise((resolve) => {
        if (this.latestMonth) {
          resolve(this.latestMonth)
        }else {
          getIndexLatestMonth().then((res) => {
            resolve(res)
            this.latestMonth = res
          })
        }
      })
    },
    changeTitle() {
      this.getLatestMonth().then((res) => {
        if (res.data.busdate) {
          this.dt = res.data.busdate[0].year_month;
          this.month = dayjs(this.dt).month() + 1
          setDocumentTitle(`1${this.month==1?'':'-'+this.month}月利润考核指标监测`);
          this.target = (this.month) / 12;
          this.finEnterprise()

        }
      });
    },
    updateData() {
      this.$nextTick(() => {
        const refMap = {
          profits: this.$refs.profits,
          loss: this.$refs.loss,
          all: this.$refs.all,
        };
        if (refMap[this.actived].resetInput) {
          refMap[this.actived].resetInput()
        }
        refMap[this.actived].resetStatus();
        this.$nextTick(() => {
          refMap[this.actived].updateData();
        })
      });
    },
    resetScroll() {
      const dom = document.getElementsByClassName("van-tabs__content")[0]
      dom.scrollTop = 0
    },
    finEnterprise() {
      let params = { "dt": this.dt, "switch": "考核目标确认", "mapperInterface": ["switch"], "moreChooseType": "0" }
      getIndexData(params).then(res => {
        if (res.code == 200) {
          this.delopedOver = res.data.switch.value=='Y'
        }
      })
    }
  },
  watch: {
    actived: {
      immediate: false,
      handler() {
        this.updateData();
        this.$router.replace({
          path: `${this.$route.path}?active=${this.actived}`
        })
        this.$nextTick(() => {
          this.changeTitle()
        })
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: calc(100vh - 5vh - env(safe-area-inset-top) - 0.2rem);
  overflow: auto;
  background: url(../../assets/img/monitor/bg.png) no-repeat top;
  background-size: 100% 4500px;

  ::v-deep .van-tabs__nav {
    background: transparent;
  }

  ::v-deep .van-tabs .van-tabs__content {
    padding-bottom: .3rem;
    // height: calc(100% - 44px);
    height: calc(100vh - 5vh - env(safe-area-inset-top) - 0.2rem - 44px);
    overflow: auto;
  }

  ::v-deep .van-tabs .van-tab {
    font-size: 0.36rem;
  }

  ::v-deep .van-tabs .van-tabs__line {
    width: 1rem;
    height: 0.07rem;
    background-color: #267af9;
  }
}
</style>
