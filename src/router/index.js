import Vue from "vue";
import Router from "vue-router";

import { reportViewDuration } from '@/utils/track'
import { setDocumentTitle } from '@/utils/document'
import { getUserId } from '@/utils/index'
import { getPermission, getModuleInfo } from '@/http/api'
import { Toast } from 'vant'

let originPush = Router.prototype.push; //备份原push方法

Router.prototype.push = function (location, resolve, reject) {
  if (resolve && reject) {
    //如果传了回调函数，直接使用
    originPush.call(this, location, resolve, reject);
  } else {
    //如果没有传回调函数，手动添加
    originPush.call(
      this,
      location,
      () => { },
      () => { }
    );
  }
};

Vue.use(Router);

const scrollBehavior = function scrollBehavior (to, from) {
  if (from.path.indexOf("moreData") != -1 || from.path.indexOf("workersDetails") != -1) {
    let savedPosition = JSON.parse(sessionStorage.getItem("savedPosition"));
    sessionStorage.removeItem("savedPosition")
    return savedPosition; //如果不是教育页面返回滚动
  } else if(to.path.indexOf("workersDetails") != -1 && from.path.indexOf("employeeDetails")!=-1) {
    let savedPosition = JSON.parse(sessionStorage.getItem("savedPositionDct"));
    return savedPosition; //如果不是教育页面返回滚动
  } else {
    return { x: 0, y: 0 };
  }
};

const router = new Router({
  mode: "history",
  scrollBehavior,
  base: "hr_mobile", // 打包时需要将注释去掉
  routes: [
    {
      path: "/",
      name: "home",
      component: resolve => require(["@/views/home/<USER>"], resolve),
      meta: {
        title: "管理驾驶舱",
        keepAlive: true
      }
    },
    {
      path: "/test",
      name: "test",
      component: resolve => require(["@/views/test/SelectFilterTest"], resolve),
    },
    {
      path: "/home",
      name: "home",
      component: resolve => require(["@/views/home/<USER>"], resolve),
      meta: {
        title: "管理驾驶舱",
        keepAlive: true
      }
    },
    {
      path: "/hrIndex",
      name: "hrIndex",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/index.vue"),
      meta: {
        title: "人才概况",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/hrIndexTwo",
      name: "hrIndexTwo",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/hrIndexTwo.vue"),
      meta: {
        title: "集团人才概况",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/eduMsg",
      name: "eduMsg",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/eduMsg.vue"),
      meta: {
        title: "教育信息",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/workMsg",
      name: "workMsg",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/workMsg.vue"),
      meta: {
        title: "工作信息",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/managerMsg",
      name: "managerMsg",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/managerMsg.vue"),
      meta: {
        title: "管理岗位",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/technologyMsg",
      name: "technologyMsg",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/technologyMsg.vue"),
      meta: {
        title: "科技岗位",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/skillMsg",
      name: "skillMsg",
      component: () => import(/* webpackChunkName: "hrPicture" */ "@/views/hrPicture/skillMsg.vue"),
      meta: {
        title: "技能岗位",
        showBottomNav: true,
        keepAlive: true
      }
    },
    {
      path: "/moreData",
      name: "moreData",
      component: () => import(/* webpackChunkName: "components" */ "@/components/moreData"),
      meta: {
        title: "",
        keepAlive: false,
        showBottomNav: true,
      }
    },
    {
      path: "/workersDetails",
      name: "workersDetails",
      component: () => import(/* webpackChunkName: "components" */ "@/components/workersDetails"),
      meta: {
        title: "",
        keepAlive: false
      }
    },
    {
      path: "/employeeDetails",
      name: "employeeDetails",
      component: () => import(/* webpackChunkName: "components" */ "@/components/employeeDetails"),
      meta: {
        title: "博士职工名单",
        keepAlive: false
      }
    },
    {
      path: "/zbBoard",
      name: "zbBoard",
      component: () => import(/* webpackChunkName: "zbBoard" */ "@/views/zbBoard/index"),
      meta: {
        title: "经营业绩红黑榜",
        keepAlive: false
      }
    },
    {
      path: "/zbBoardSecond",
      name: "zbBoardSecond",
      component: () => import(/* webpackChunkName: "zbBoardSecond" */ "@/views/zbBoardSecond/index"),
      meta: {
        title: "经营业绩排行榜",
        keepAlive: false
      }
    },
    {
      path: "/economyReport",
      name: "economyReport",
      component: () => import(/* webpackChunkName: "economyReport" */ "@/views/economyReport/index"),
      meta: {
        title: "经济运行报告",
        keepAlive: false
      }
    },
    {
      path: "/pdfPreview",
      name: "pdfPreview",
      component: () => import(/* webpackChunkName: "economyReport" */ "@/views/economyReport/components/pdfPreview"),
      meta: {
        title: "查看报告",
        keepAlive: false
      }
    },
    {
      path: "/assetsMap",
      name: "assetsMap",
      component: () => import(/* webpackChunkName: "assetsMap" */ "@/views/assetsMap/index"),
      meta: {
        title: "资产总览",
        keepAlive: false
      }
    },
    {
      path: "/jjyxReport",
      name: "jjyxReport",
      component: () => import(/* webpackChunkName: "jjyxReport" */ "@/views/jjyxReport/index"),
      meta: {
        title: "1-10月主要指标",
        keepAlive: false
      }
    },
    {
      path: "/liangjin",
      name: "liangjin",
      component: () => import(/* webpackChunkName: "liangjin" */ "@/views/liangjin/index"),
      meta: {
        title: "1-10月两金监测",
        keepAlive: false
      }
    },
    {
      path: "/siku",
      name: "siku",
      component: () => import(/* webpackChunkName: "siku" */ "@/views/siku/index"),
      meta: {
        title: "司库管理",
        keepAlive: false
      }
    },
    {
      path: "/viewReport",
      name: "viewReport",
      component: () => import(/* webpackChunkName: "viewReport" */ "@/views/viewReport/index"),
      meta: {
        title: "查看报告",
        keepAlive: false
      }
    },
    {
      path: "/cecBoard",
      name: "cecBoard",
      component: () => import(/* webpackChunkName: "cecBoard" */ "@/views/cecBoard/index"),
      meta: {
        title: "1-10月CEC指标看板",
        keepAlive: false
      }
    },
    {
      path: "/tgMonitor",
      name: "tgMonitor",
      component: () => import(/* webpackChunkName: "tgMonitor" */ "@/views/tgMonitor/index"),
      meta: {
        title: "两金监测",
        keepAlive: false
      }
    },
    {
      path: "/monitor",
      name: "monitor",
      component: () => import(/* webpackChunkName: "monitor" */ "@/views/monitor/index"),
      meta: {
        title: "利润考核指标监测",
        keepAlive: false
      }
    },
    {
      path: "/upload",
      name: "upload",
      component: () => import(/* webpackChunkName: "upload" */ "@/views/upload/index"),
      meta: {
        title: "文件上传",
        keepAlive: false
      }
    },
    {
      path: "/loginIAM",
      name: "loginIAM",
      component: () => import(/* webpackChunkName: "auth" */ "@/views/auth/loginIAM.vue"),
      meta: {
        title: "用户认证",
        keepAlive: false,
        requiresAuth: false // 不需要认证，避免循环
      }
    },
    {
      path: "/noPermission",
      name: "noPermission",
      component: () => import(/* webpackChunkName: "common" */ "@/views/common/noPermission.vue"),
      meta: {
        title: "暂无权限",
        keepAlive: false,
        requiresAuth: false
      }
    }
  ]
});
export default router;
// 在现有的router.beforeEach中添加认证逻辑
import { checkAuthStatus } from '@/utils/auth'

router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    setDocumentTitle(to.meta.title);
  }

  // 记录页面访问时长
  reportViewDuration(to, from, {})

  // 认证逻辑
  if (to.path === '/loginIAM') {
    // 检查是否已经存在userId，如果存在则直接跳转到目标页面
    const authStatus = checkAuthStatus()
    if (authStatus.hasUserld) {
      console.log('路由守卫 - 发现已存在的userId，直接跳转到目标页面')
      
      // 处理redirectUrl参数
      let redirectUrl = to.query.redirectUrl || '/home'
      const { redirectUrl: _, ...otherParams } = to.query
      
      // 如果redirectUrl包含完整URL，提取路径部分
      if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
        try {
          const url = new URL(redirectUrl)
          redirectUrl = url.pathname + url.search + url.hash
        } catch (error) {
          console.error('URL解析失败:', error)
          redirectUrl = '/home'
        }
      }
      
      // 移除 /hr_mobile 前缀，因为路由配置中已经设置了 base: "hr_mobile"
      if (redirectUrl.startsWith('/hr_mobile/')) {
        redirectUrl = redirectUrl.replace('/hr_mobile', '')
      } else if (redirectUrl === '/hr_mobile') {
        redirectUrl = '/'
      }
      
      console.log('路由守卫 - 处理后的redirectUrl:', redirectUrl)
      console.log('路由守卫 - 其他参数:', otherParams)
      
      // 直接跳转到目标页面，不显示loginIAM页面
      next({
        path: redirectUrl,
        query: otherParams,
        replace: true
      })
      return
    }
    
    // 没有userId，正常进入loginIAM页面进行认证
    next()
    return
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    const authStatus = checkAuthStatus()
    
    if (!authStatus.hasUserld) {
      // 没有userld，重定向到loginIAM页面处理
      const redirectUrl = to.fullPath
      console.log('路由守卫 - 重定向URL:', redirectUrl)
      console.log('路由守卫 - 目标路由query:', to.query)
      
      // 构建传递给loginIAM的参数
      const loginQuery = {
        redirectUrl: redirectUrl,
        ...to.query // 保留所有原有的query参数
      }
      
      console.log('路由守卫 - 传递给loginIAM的参数:', loginQuery)
      
      // 跳转到loginIAM页面，让loginIAM页面处理IAM重定向
      next({
        path: '/loginIAM',
        query: loginQuery
      })
      return
    }
  }

  // 新增：模块权限检查
  const protectedRoutes = {
    '/zbBoard': 'indi',
    '/zbBoardSecond': 'phb', 
    '/monitor': 'lrjc',
    '/tgMonitor': 'lj',
    '/hrIndex': 'hr',
    '/assetsMap': 'zc', 
    '/siku': 'siku',
    '/viewReport': 'ckbg'
  }

  if (protectedRoutes[to.path]) {
    try {
      const hasPermission = await checkModulePermission(protectedRoutes[to.path], to.query.companyId)
      if (!hasPermission) {
        // 保持原目标页面的标题
        if (to.meta.title) {
          setTimeout(() => {
            setDocumentTitle(to.meta.title)
          })
        }
        next({
          path: '/noPermission',
          replace: true
        })
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      // 保持原目标页面的标题
      if (to.meta.title) {
        setTimeout(() => {
          setDocumentTitle(to.meta.title)
        })
      }
      next({
        path: '/noPermission',
        replace: true
      })
      return
    }
  }

  next()
});

// 权限检查函数
async function checkModulePermission(moduleId, companyId = '99') {
  try {
    const userId = await getUserId()
    if (!userId) {
      return false
    }

    if (companyId === '99') {
      // CEC集团总部，使用原来的getPermission逻辑
      const res = await getPermission(userId)
      if (res && res.code === 200 && res.data && res.data.permissions) {
        // 查看报告需要动态判断权限（后端不再返回ckbg）
        if (moduleId === 'ckbg') {
          // 首先检查是否有 indi 权限
          if (!res.data.permissions.includes('indi')) {
            return false
          }
          
          // 然后根据首页逻辑判断是否有查看报告权限
          const { getAllCompanyInfoList } = await import('@/http/api')
          const companyRes = await getAllCompanyInfoList()
          if (companyRes && companyRes.data && companyRes.data.cecLanxinUserRbCompanyList) {
            const companyInfoList = companyRes.data.cecLanxinUserRbCompanyList
            
            // 找到用户所在的企业
            const userCompanies = companyInfoList.filter(item => 
              item.userIds && item.userIds.includes(userId)
            )
            
            // 只有全集团(cpyTag="01")的用户才能查看报告
            return userCompanies.some(item => item.cpyTag === "01")
          }
          return false
        }
        return res.data.permissions.includes(moduleId)
      }
    } else {
      // 其他企业，使用模块权限逻辑
      const res = await getModuleInfo(userId, companyId)
      if (res && res.code === 200 && res.data && res.data.moduleInfo) {
        return res.data.moduleInfo.some(module => module.id === moduleId)
      }
    }
    
    return false
  } catch (error) {
    console.error('模块权限检查失败:', error)
    return false
  }
}
