// ====== 方法1：把所有请求的地址封装成一个方法，好处就是有个归总 ======
//引入request.js文件
import request from "./request";

// 测试接口
export function getMapData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data //发送请求要配置的参数 无参数的情况下也可以不写
  });
}


// 教育信息接口
export function getEduData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getEducateData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data //发送请求要配置的参数 无参数的情况下也可以不写
  });
}

// 通用后台接口
export function getHrData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getHrData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data //发送请求要配置的参数 无参数的情况下也可以不写
  });
}

// 菜单权限
export function getUserId(code) {
  return request({
    url: "/blade-visual/lanxin/getUserId", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    params: {
      code
    } //发送请求要配置的参数 无参数的情况下也可以不写
  });
}

export function getUserInfo(code) {
  return request({
    url: "/blade-visual/lanxin/getUserDetailInfo",
    method: "POST",
    params: {
      code
    }
  })
}
export function getPermission(userId) {
  return request({
    url: "/blade-visual/lanxin/getPermission", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    params: {
      userId
    } //发送请求要配置的参数 无参数的情况下也可以不写
  });
}

// IAM认证相关接口
export function getUserIdByTicket(ticket, redirectUrl) {
  return request({
    url: "/blade-visual/getUserIdByTicket",
    method: "GET",
    params: {
      ticket: ticket,
      redirectUrl: redirectUrl
    }
  });
}

export function getIamTicket() {
  return request({
    url: "/blade-visual/iamTicket",
    method: "GET"
  });
}

export function validateUserld(userld) {
  return request({
    url: "/blade-visual/validateUserld",
    method: "POST",
    params: {
      userld: userld
    }
  });
}
export function  getIndiorgList() {
  return request({
    url: "/blade-visual/lanxin/indiorgList", // 这个地址是去掉公共地址剩下的地址
    method: "GET", // 请求方式 支持多种方式  get post put delete 等等
  });
}
// 指标看板
export function getKbData(data) {
  return request({
    url: "/blade-visual/kanban/getData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data
  });
}
export function getSecKbData(data) {
  return request({
    url: "/blade-visual/sec/kanban/getData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data
  });
}
// 指标看板-二级企业名单
export function getIndiOrgList() {
  return request({
    url: "/blade-visual/lanxin/indiOrgList", // 这个地址是去掉公共地址剩下的地址
    method: "GET", // 请求方式 支持多种方式  get post put delete 等等
  });
}
// 两金检测，集团整体
export function getLJData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getLJData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data
  });
}
// 两金监测，指标排名，红榜
export function getLJRankData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getTopList",
    method: "POST",
    data
  });
}
// 两金监测，指标排名，红榜（期初值）
export function getLJRankQcData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getTopQcList",
    method: "POST",
    data
  });
}
// 两金监测，指标排名，黑榜
export function getLJRankDescData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getLowList",
    method: "POST",
    data
  });
}
// 两金监测，指标排名，黑榜（期初值）
export function getLJRankDescQcData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getLowQcList",
    method: "POST",
    data
  });
}
// 经济运行
export function getJJYXData(data) {
  return request({
    url: "/blade-visual/hrHomeApp/getJJYXData", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data
  });
}

// 两金总览月份
export function getLjMonth() {
  return request({
    url: "/blade-visual/cec/ljMonthM", // 这个地址是去掉公共地址剩下的地址
    method: "GET", // 请求方式 支持多种方式  get post put delete 等等
    params: {orgId:'99'}
  });
}

// 指标看板-经济运行月份
export function getJjMonth() {
  return request({
    url: "/blade-visual/cec/jjmonth", // 这个地址是去掉公共地址剩下的地址
    method: "GET", // 请求方式 支持多种方式  get post put delete 等等
  });
}

// 所有企业的详细信息
export function getAllCompanyInfoList() {
  return request({
    url: "/blade-visual/lanxin/getCompanyOrgDataList",
    method: "POST",
  });
}

// 所有三级企业的详细信息
export function getAllThirdCompanyInfoList(data = {}) {
  return request({
    url: "/blade-visual/permission/getCompanyOrgDataList",
    method: "POST",
    data
  });
}

// 获取公司信息列表
export function getCompanyInfo(userId) {
  return request({
    url: "/blade-visual/permission/getCompanyInfo",
    method: "POST",
    data: {
      userId
    }
  });
}

// 获取模块信息列表
export function getModuleInfo(userId, companyId) {
  return request({
    url: "/blade-visual/permission/getModuleInfo",
    method: "POST",
    data: {
      userId,
      companyId
    }
  });
}

// 当前最新有数据的月
export function getLatestMonth() {
  return request({
    url: "/blade-visual/kanban/getReportDate",
    method: "POST",
    data: {
      mapperInterface: ["kanbanym"],
      moreChooseType: "0"
    }
  });
}

// 当前最新有数据的月【利润指标】
export function getIndexLatestMonth() {
  return request({
    url: "/blade-visual/finEnterprise/getData",
    method: "POST",
    data: {
      mapperInterface: ["busdate"],
      moreChooseType: "0"
    }
  });
}
// 指标看板-经济运行查看报告接口，用来判断是否隐藏显示
export function getReportStatus(month) {
  return request({
    url: "/blade-visual/cec/jjPdf", // 这个地址是去掉公共地址剩下的地址
    method: "GET", // 请求方式 支持多种方式  get post put delete 等等
    params: {
      month:month
    }
  });
}
// 文件上传
export function uploadFile(data) {
  return request({
    url: "/blade-visual/lanxin/uploadBusOss", // 这个地址是去掉公共地址剩下的地址
    method: "POST", // 请求方式 支持多种方式  get post put delete 等等
    data: data
  });
}

// 查看报告列表
export const getReportList = (data) => {
  return request({
    url: "/blade-visual/lanxin/queryOssDataList",
    method: "POST",
    data
  })
}

// 下载报告
export const getReport = (fileName) => {
  return request({
    url: "/blade-visual/lanxin/downloadBusOss",
    method: "GET",
    responseType: 'blob',
    params: {
      fileName
    }
  })
}

// 利润指标接口
export const getIndexData = (data) => {
  return request({
    url: "/blade-visual/finEnterprise/getData",
    method: "POST",
    data
  })
}

// 埋点上报
export const viewReport = (data) => {
  return request({
    url: "/blade-visual/lanxinUserVisitCollector/save",
    method: "POST",
    data
  })
}
export const getSummary = (data) => {
  return request({
    url: "/blade-visual/hrHomeApp/getSummary",
    method: "POST",
    data
  })
}


export const getReceivable = (data) => {
  return request({
    url: "/blade-visual/hrHomeApp/getReceivable",
    method: "POST",
    data
  })
}



export const getInventory = (data) => {
  return request({
    url: "/blade-visual/hrHomeApp/getInventory",
    method: "POST",
    data
  })
}
//获取所有的的两金监测二级企业列表
export const getCompanyAll = (data) => {
  return request({
    url: "/blade-visual/hrHomeApp/getCompany",
    method: "POST",
    data
  })
}
//获取有权限的两金监测二级企业列表
export const getCompanyOrgInfo = (data) => {
  return request({
    url: "/blade-visual/hrHomeApp/getCompanyOrgInfo",
    method: "POST",
    data
  })
}
// 页面使用方法
{
  /* <script>
// 1.引入需要的接口
import { getMapData } from "../http/api";
export default {
  name: "Home",
  components: {},
  methods: {
      //2.直接使用 .then 是请求成功的回调 .catch是请求失败的回调
      getUser(){
          getMapData('有参数就填这里，没有参数就空着').then((res) => {
            console.log(res);
          }).catch((err) => {
              console.log(err);
          });
      }
  }
};
</script> */
}
