<template>
  <div id="app">
    <self-header />
    <div class="app-content">
      <bottom-nav v-if="$route.meta.showBottomNav || $route.name=='moreData'"/>
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive"/>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" />
    </div>

  </div>
</template>

<script>
import bottomNav from '@/components/layout/bottomNav.vue';
import selfHeader from '@/header.vue'
import { setState } from '@/utils/cache'
import { getUserId } from '@/utils'

export default {
  name: 'App',
  components: {
    bottomNav,
    selfHeader
  },
  mounted(){
    // 现有逻辑
    getUserId()
    window.addEventListener('pageshow', function(event) {
      if(event.persisted) location.reload();
    });
    
    // 首次进入时处理路由参数中的companyId
    this.handleRouteCompanyId();
  },
  methods: {
    // 处理路由中的companyId参数
    handleRouteCompanyId() {
      const route = this.$route;
      
      // 从路由查询参数中获取companyId
      if (route.query && route.query.companyId) {
        setState('selectedCompanyId', route.query.companyId);
      }
    }
  }
}
</script>

<style>
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  height: 100vh;
  width: 100vw;
  /* padding-top: env(safe-area-inset-top);
  box-sizing: border-box; */
  overflow: hidden;
}

.app-content {
  width: 100%;
  height: calc(100% - 5vh - env(safe-area-inset-top) - 0.2rem);
  position: relative;
  overflow: auto;
}
</style>
