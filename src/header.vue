<template>
  <div class="container">
    <div class="left" @click="handleBack">
      <van-icon name="arrow-left" />
    </div>
    <span class="title">{{ title }}</span>
    <div class="right"></div>
  </div>
</template>

<script>
import { addOverser } from '@/utils/document'
import { reportViewDuration } from '@/utils/track'

export default {
  name: 'header',
  data() {
    return {
      title: document.title
    }
  },
  mounted() {
    addOverser((title) => {
      this.title = title
    })
  },
  methods: {
    closeWindow(fromPath) {
      setTimeout(() => {
        try {
          const toPath = this.$route.fullPath
          if (fromPath === toPath) {
            reportViewDuration({
              path: 'outside'
            }).finally(() => {
              lx.ui.closeWindow();
            })
          }
        }catch (e) {
          console.error(e)
        }
        
      }, 200)
    },
    handleBack() {
      this.$router.back()
      this.closeWindow(this.$route.fullPath)
    }
  }
}
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    min-height: 5vh;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.1rem 0.3rem;
    font-size: 0.35rem;
    font-weight: 500;
    padding-top: calc(env(safe-area-inset-top) + 0.1rem);
    box-sizing: border-box;
    position: relative;
    z-index: 1000000;
  }
</style>