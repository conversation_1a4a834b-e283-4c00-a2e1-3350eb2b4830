// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import App from "./App";
import router from "./router";
import "@/plugins/vant";
// 按需引入echarts核心模块
import * as echarts from 'echarts/core';
// 引入需要的图表类型
import { BarChart, PieChart, PictorialBarChart } from 'echarts/charts';
// 引入需要的组件
import {
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent
} from 'echarts/components';
// 引入渲染器
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  PieChart,
  PictorialBarChart,
  CanvasRenderer
]);

import Vant from "vant";
import "vant/lib/index.css";
import "@/assets/styles/index.css"; // global css
import store from "@/store/index.js";
import { Toast } from "vant";
// import Vconsole from "vconsole"
// new Vconsole()
import Loading from '@/components/overlayLoading/index.js'

Vue.use(Toast);

// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
// import * as echarts from 'echarts/core';
// // 引入柱状图图表，图表后缀都为 Chart
// import { BarChart, PieChart, PictorialBarChart } from 'echarts/charts';
// // 引入提示框，标题，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
// import {
//   TitleComponent,
//   LegendComponent,
//   TooltipComponent,
//   GridComponent,
//   DatasetComponent,
//   TransformComponent
// } from 'echarts/components';
// // 标签自动布局、全局过渡动画等特性
// import { LabelLayout, UniversalTransition } from 'echarts/features';
// // 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
// import { CanvasRenderer } from 'echarts/renderers';

// // 注册必须的组件
// echarts.use([
//   TitleComponent,
//   TooltipComponent,
//   LegendComponent,
//   GridComponent,
//   DatasetComponent,
//   TransformComponent,
//   BarChart,
//   PieChart,
//   PictorialBarChart,
//   LabelLayout,
//   UniversalTransition,
//   CanvasRenderer
// ]);

Vue.prototype.$echarts = echarts;
Vue.config.productionTip = false;
//接口封装使用

Vue.use(Vant);
Vue.use(Loading)
/* eslint-disable no-new */
new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>"
});
