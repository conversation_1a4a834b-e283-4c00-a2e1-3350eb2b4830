# SelectFilter 样式统一指南

## 样式优化内容

### 1. 统一选中内容展示位置
- **修改前**: SelectFilter 选中内容左对齐，级联选择器右对齐
- **修改后**: 统一改为右对齐，保持视觉一致性

### 2. 级联选择器蓝色主题
- **标题颜色**: 使用蓝色 `#1989fa`
- **选中状态**: tab 和选项的选中状态使用蓝色
- **下划线**: tab 下划线使用蓝色

### 3. 触发器样式统一
- **布局**: 左侧标签 + 右侧值 + 箭头图标
- **间距**: 统一的内边距和外边距
- **字体**: 统一的字体大小和颜色
- **交互**: 统一的点击反馈效果

## 具体修改

### SelectFilter 组件
```scss
.trigger-value {
  text-align: right; // 改为右对齐
}
```

### 级联选择器样式
```scss
// 自定义触发器，与 SelectFilter 保持一致
.cascader-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0.4rem;
  background: #fff;
  border-radius: 0.12rem;
  border: 1px solid #ebedf0;
}

// 蓝色主题
::v-deep .van-cascader {
  .van-cascader__title {
    color: #1989fa;
  }
  
  .van-tab--active {
    color: #1989fa;
  }
  
  .van-tabs__line {
    background-color: #1989fa;
  }
  
  .van-cascader__option--selected {
    color: #1989fa;
  }
}
```

## 视觉效果

### 修改前问题
1. 选中内容位置不统一
2. 级联选择器使用红色主题
3. 触发器样式不一致

### 修改后效果
1. ✅ 选中内容统一右对齐
2. ✅ 级联选择器使用蓝色主题
3. ✅ 触发器样式完全一致

## 使用建议

1. **保持一致性**: 所有筛选器组件都使用相同的触发器样式
2. **颜色规范**: 使用项目统一的蓝色主题 `#1989fa`
3. **布局规范**: 标签左对齐，值右对齐，箭头在最右侧
4. **交互反馈**: 统一的点击和激活状态样式

## 扩展性

这套样式规范可以应用到其他类似的筛选器组件：
- 日期选择器
- 时间选择器  
- 多选筛选器
- 自定义筛选器

通过统一的样式规范，确保整个应用的视觉一致性和用户体验。
