# 级联选择器使用案例

## 概述

在 SelectFilterTest 页面中，我们展示了三种不同的级联选择器使用方式：

1. **基础 SelectFilter 组件** - 单级选择
2. **vant2 Cascader 组件** - 多级级联选择
3. **SelectFilter 组合使用** - 多个 SelectFilter 实现级联效果

## 1. 基础 SelectFilter 组件

```vue
<select-filter
  v-model="selectedArea"
  :options="areaOptions"
  label="地区"
  placeholder="请选择所在地区"
  title="选择地区"
  @change="handleAreaChange"
/>
```

**特点：**
- 单级选择
- 自定义触发器样式
- 基于 vant2 Picker 组件

## 2. vant2 Cascader 组件

```vue
<van-cell title="省市选择" :value="cascaderText" is-link @click="showCascader = true" />
<van-popup v-model="showCascader" round position="bottom">
  <van-cascader
    v-model="cascaderValue"
    title="请选择所在地区"
    :options="cascaderOptions"
    @close="showCascader = false"
    @finish="onCascaderFinish"
  />
</van-popup>
```

**数据格式：**
```javascript
cascaderOptions: [
  {
    text: '浙江省',
    value: '330000',
    children: [
      { text: '杭州市', value: '330100' },
      { text: '宁波市', value: '330200' }
    ]
  }
]
```

**特点：**
- 多级级联选择
- 原生 vant2 组件
- 支持无限层级

## 3. SelectFilter 组合使用

```vue
<select-filter
  v-model="selectedProvince"
  :options="provinceOptions"
  label="省份"
  @change="handleProvinceChange"
/>
<select-filter
  v-model="selectedCity"
  :options="cityOptionsForProvince"
  label="城市"
  :disabled="!selectedProvince"
  @change="handleCityChange"
/>
```

**实现逻辑：**
```javascript
computed: {
  cityOptionsForProvince() {
    if (!this.selectedProvince) return []
    return this.provinceCityMap[this.selectedProvince] || []
  }
},
methods: {
  handleProvinceChange(value, option) {
    this.selectedCity = '' // 重置下级选择
  }
}
```

**特点：**
- 多个独立的 SelectFilter 组件
- 上级选择影响下级选项
- 更灵活的布局控制

## 使用场景对比

| 场景 | 推荐方案 | 优势 |
|------|----------|------|
| 简单单级选择 | SelectFilter | 样式统一，交互简单 |
| 多级级联选择 | vant2 Cascader | 原生支持，性能好 |
| 复杂业务逻辑 | SelectFilter 组合 | 灵活性高，可定制 |
| 表单场景 | SelectFilter 组合 | 便于验证和布局 |

## 测试页面访问

访问 `/test` 路由可以查看完整的测试案例，包括：
- 基础用法演示
- 禁用状态展示
- 级联选择器案例
- 组合使用示例
- 事件日志记录

## 注意事项

1. **依赖组件**: 确保已在 `vant.js` 中引入 `Cascader` 组件
2. **数据格式**: Cascader 需要 `children` 字段，SelectFilter 需要 `text` 和 `value` 字段
3. **状态管理**: 级联选择时注意重置下级选择状态
4. **性能优化**: 大量数据时考虑懒加载或虚拟滚动
