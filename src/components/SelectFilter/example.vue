<template>
  <div class="example-page">
    <div class="example-section">
      <h3>基础用法</h3>
      <select-filter
        v-model="selectedArea"
        :options="areaOptions"
        label="地区"
        placeholder="请选择所在地区"
        title="选择地区"
        @change="handleAreaChange"
      />
      <p class="result">选中值: {{ selectedArea }}</p>
    </div>

    <div class="example-section">
      <h3>禁用状态</h3>
      <select-filter
        v-model="selectedCity"
        :options="cityOptions"
        label="城市"
        placeholder="请选择城市"
        title="选择城市"
        :disabled="true"
      />
    </div>

    <div class="example-section">
      <h3>自定义样式</h3>
      <select-filter
        v-model="selectedType"
        :options="typeOptions"
        label="类型"
        placeholder="请选择类型"
        title="选择类型"
        @change="handleTypeChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
      <p class="result">选中值: {{ selectedType }}</p>
    </div>
  </div>
</template>

<script>
import SelectFilter from '../SelectFilter.vue'

export default {
  name: 'SelectFilterExample',
  components: {
    SelectFilter
  },
  data() {
    return {
      selectedArea: 'hangzhou',
      selectedCity: '',
      selectedType: '',
      areaOptions: [
        { text: '杭州', value: 'hangzhou' },
        { text: '宁波', value: 'ningbo' },
        { text: '温州', value: 'wenzhou' },
        { text: '嘉兴', value: 'jiaxing' },
        { text: '湖州', value: 'huzhou' },
        { text: '绍兴', value: 'shaoxing' },
        { text: '金华', value: 'jinhua' },
        { text: '衢州', value: 'quzhou' },
        { text: '舟山', value: 'zhoushan' },
        { text: '台州', value: 'taizhou' },
        { text: '丽水', value: 'lishui' }
      ],
      cityOptions: [
        { text: '北京', value: 'beijing' },
        { text: '上海', value: 'shanghai' },
        { text: '广州', value: 'guangzhou' },
        { text: '深圳', value: 'shenzhen' }
      ],
      typeOptions: [
        { text: '类型一', value: 'type1' },
        { text: '类型二', value: 'type2' },
        { text: '类型三', value: 'type3' }
      ]
    }
  },
  methods: {
    handleAreaChange(value, option) {
      console.log('地区改变:', value, option)
    },
    handleTypeChange(value, option) {
      console.log('类型改变:', value, option)
    },
    handleConfirm(value, option) {
      console.log('确认选择:', value, option)
    },
    handleCancel() {
      console.log('取消选择')
    }
  }
}
</script>

<style lang="scss" scoped>
.example-page {
  padding: 0.4rem;
  background: #f7f8fa;
  min-height: 100vh;
}

.example-section {
  margin-bottom: 0.6rem;
  
  h3 {
    font-size: 0.36rem;
    color: #323233;
    margin-bottom: 0.3rem;
  }
  
  .result {
    margin-top: 0.2rem;
    font-size: 0.28rem;
    color: #646566;
    padding: 0.2rem;
    background: #fff;
    border-radius: 0.08rem;
  }
}
</style>
