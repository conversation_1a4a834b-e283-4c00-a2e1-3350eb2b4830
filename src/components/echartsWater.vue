<template>
  <div class="barBox">
    <div class="echart" ref="echarts" :style="{ float: 'left', width: '1.95rem', height: '1.95rem'  }"></div>
    <div v-show="toChild && toChild.length<1" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import { toPoint } from '@/utils/chart';
export default {
  name: "EchartsWater",
  props: {
    toChild: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  /**
     * 深度监听 图表生成之后 传定时刷新数据进来 由于数据不能动态传值，所以子组件使用深度监听来监控数据变化
     */
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    toChild: {
      deep: true,
      handler(newVal) {
        this.getByrsEcharts(newVal) //浏览器刷新
      }
    },

  },
  mounted() {
    if(this.toChild){
       this.getByrsEcharts(this.toChild) //默认进来获取父传子的数据先请求一遍接口
    }
  },
  methods: {
    //环形图的开始
    getByrsEcharts(data) {
      console.log(data);
      if (data.length) {
        data = data[0]['value']; //处理watch监听不到深层数据
        data = [toPoint(data)]; //"13.4%"数据格式转换成[0.134]
        var option = {
          legend: {
            show: false,
            left: 0,
            bottom: 0,
            itemWidth: 10,
            itemHeight: 10,
            width: 50,
            textStyle: {
              color: '#949899'
            },
            // formatter: (name) => {
            //   let label = null;
            //   switch (name) {
            //     case '已用': label = `${name} (${this.useMemoryGb.toFixed(1)} GB)`; break;
            //     case '可用': label = `${name} (${(this.totalMemoryGb - this.useMemoryGb).toFixed(1)} GB)`; break;
            //   }
            //   return label;
            // }
          },
          series: [
            {
              name: '已用',
              type: 'liquidFill',
              outline: {
                show: true, //是否显示轮廓 布尔值
                borderDistance: 4, //外部轮廓与图表的距离 数字
                itemStyle: {
                  borderWidth: 1,  //边框的宽度
                  borderColor: '#6694FF',
                  //   shadowBlur: 5, //外部轮廓的阴影范围 一旦设置了内外都有阴影
                  //shadowColor: '#000' //外部轮廓的阴影颜色 
                }
              },
              itemStyle: {
                opacity: 1,//波浪的透明度
                shadowBlur: 10,//波浪的阴影范围
                shadowColor: 'red',//阴影颜色
                normal: {
                  shadowColor: 'rgba(0,0,0,0)',//阴影颜色
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#6694FF',
                    },
                    {
                      offset: 1,
                      color: '#B7BCD9',
                    },
                  ]),

                },
              },
              //   data: [0.3, 0.3],
              data: data,
              backgroundStyle: {
                color: '#fff',//图表的背景颜色
                borderWidth: 1,//图表的边框宽度
                borderColor: '#6694FF',//图表的边框颜色
                // itemStyle: {
                //   shadowBlur: 100,//设置无用
                //   shadowColor: '#6694FF',//设置无用
                //   opacity: 1 //设置无用
                // }
              },
              //   emphasis: {
              //     itemStyle: {
              //       opacity: 1 //鼠标经过波浪颜色的透明度
              //     }
              //   },
              //   color: ['rgba(102,148,255,0.9)', 'rgba(102,148,255,0.3)'],//水波的颜色 对应的是data里面值
              shape: 'circle',//水填充图的形状 circle默认圆形  rect圆角矩形  triangle三角形  diamond菱形  pin水滴状 arrow箭头状  还可以是svg的path
              center: ['50%', '50%'],//图表相对于盒子的位置 第一个是水平的位置 第二个是垂直的值 默认是[50%,50%]是在水平和垂直方向居中 可以设置百分比 也可以设置具体值
              radius: '80%', //图表的大小 值是圆的直径 可以是百分比 也可以是具体值 100%则占满整个盒子 默认是40%; 百分比下是根据宽高最小的一个为参照依据
              //   amplitude: 3,   //振幅 是波浪的震荡幅度 可以取具体的值 也可以是百分比 百分比下是按图标的直径来算
              //   waveLength: '30%',//波的长度 可以是百分比也可以是具体的像素值  百分比下是相对于直径的 取得越大波浪的起伏越小
              //   phase: 0,//波的相位弧度 默认情况下是自动
              //   period: (value, index) => {//控制波的移动速度 可以是函数 也可以是数字 两个参数  value 是data数据里面的值 index 是data值的索引

              //     return index * 1000;
              //   },
              //   direction: 'left',//波移动的速度 两个参数  left 从右往左 right 从左往右
              //   waveAnimation: true, //控制波动画的开关  值是布尔值 false 是关闭动画 true 是开启动画 也是默认值
              //   animationEasing: 'linear',//初始动画
              //   animationEasingUpdate: 'quarticInOut',//数据更新的动画效果
              //   animationDuration: 3000, //初始动画的时长，支持回调函数，可以通过每个数据返回不同的 delay 时间实现更绚丽的初始动画效果
              //   animationDurationUpdate: 300, //数据更新动画的时长
              label: {
                show: true,
                color: '#3061D3',
                formatter: (data) => {
                  // console.log(data, '另哈');
                  return (data.value * 100).toFixed(1) + '%'
                },
                insideColor: '#fff',
                // textShadowColor: '#00A156',
                textShadowBlur: 5,
                fontWeight: '400',
                fontSize: '13px',
                position: ['50%', '80%'],
                // position: 'inside',
                align: 'center',
                baseline: 'middle'
              }
            },

          ]
        }
      }
      //   const byrsBar = echarts.init(document.getElementById(`barIdName`))// 图标初始化
      const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
      byrsBar.clear();
      setTimeout(() => {
        if (option) {
          byrsBar.setOption(option)// 渲染页面
          // 随着屏幕大小调节图表
          window.addEventListener('resize', () => {
            byrsBar.resize()
          })
        }
      }, 500);
    },
  },
  created() {

  }
}
</script>
<style lang="scss" scoped>
  .barBox {
    position: relative;
    width: 1.95rem;
    height: 1.95rem;
    .title {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      .desc {
        font-size: 24px;
        color: #fff;
        font-weight: normal;
      }
      span {
        color: #7ac8ff;
        font-size: 16px;
      }
    }
    // .echart {
    //   padding: 20px 20px 15px;
    // }
    .noData {
      color: #fff;
      position: absolute;
      // left: 42%;
      // margin: 60px auto;
    }
  }
</style>