<template>
  <div class="titleBox">
    <div class="tTop">
      <div class="text">
        {{title}}
        <slot name="subTitle"></slot>
      </div>
      <!-- <div class="unit" v-if="unit">单位：{{ unit }}</div> -->
    </div>
    <!-- <item-tip :type="'card'" :show="show" @handleClose="show=false" :title="title" :content="text" /> -->
  </div>
</template>

<script>
import itemTip from '@/components/itemTip.vue'
export default {
  name: 'publicTitle',
  components: {
    itemTip
  },
  props: {
    unit: {
      type: String,
      default: "人"
    },
    title: {

    },
    text: {
      type: [Array],
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      show: false,
    }
  },
  methods: {
    tipBtn() {
      this.show = true;
    },
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
.titleBox {
  margin-top: 0.37rem;
  background-color: rgba(0,0,0,0);
  background: url("../assets/img/titleBg.png") no-repeat;
  background-position: top;
  background-size: cover;
  height: 1.01rem;
  line-height: 1.07rem;
  .tTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .text {
      font-size: 0.36rem;
      color: #181c26;
      padding-left: 0.3rem;
      padding-top: 0.02rem;
      position: relative;
      font-weight: 600;
      .tips {
        width: 0.4rem;
        height: 0.4rem;
        position: absolute;
        right: -25px;
        top: .19rem;
      }
      // &::before {
      //   content: "";
      //   position: absolute;
      //   top: 50%;
      //   left: 0;
      //   transform: translateY(-50%);
      //   width: 0.06rem;
      //   height: 0.36rem;
      //   background-color: #6d9fff;
      //   border-radius: 6px;
      // }
    }
    .unit {
      font-size: 0.3rem;
      color: #666666;
      padding-right: .3rem;
    }
  }
}
</style>