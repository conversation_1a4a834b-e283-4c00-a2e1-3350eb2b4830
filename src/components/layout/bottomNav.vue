<template>
  <div id="bottom-nav">
    <van-tabbar v-model="active">
      <van-tabbar-item @click="handleClick(0)">
        <template #icon="props">
          <img :src="props.active ? navLeftActive : navLeft" :style="{height: '0.4rem'}"/>
        </template>
        <div :style="{visibility: !showPopover1 ? 'hidden' : ''}" class="nav-popover nav-popover1">
          <p v-for="(item,index) in action1" :key="index" @click.stop="handleToGo(item,item.to, 0)">
            <span>{{ item.text }}</span>
          </p>
        </div>
        人力资源全貌
      </van-tabbar-item>
      <van-tabbar-item @click="handleClick(1)">
        <template #icon="props">
          <img :src="props.active ? navRightActive : navRight" :style="{height: '0.4rem'}"/>
        </template>
        <div :style="{visibility: !showPopover2 ? 'hidden' : ''}" class="nav-popover nav-popover2">
          <p v-for="(item,index) in action2" :key="index" @click.stop="handleToGo(item,item.to, 1)">
            <span>{{ item.text }}</span>
          </p>
        </div>
        各岗位人员情况
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import navLeft from "@/assets/img/navLeft.png";
import navLeftActive from "@/assets/img/navLeftActive.png";
import navRight from "@/assets/img/navRight.png";
import navRightActive from "@/assets/img/navRightActive.png";

export default {
  name: 'BottomNav',
  props: {},
  data() {
    return {
      navLeft: navLeft,
      navLeftActive: navLeftActive,
      navRight: navRight,
      navRightActive: navRightActive,
      active: 0,
      lastActive: 0,
      showPopover1: false,
      showPopover2: false,
      action1: [
        {text: '人才概况', to: '/hrIndex'},
        {text: '教育信息', to: '/eduMsg'},
        {text: '工作信息', to: '/workMsg'},
        { text: '人员查询', to: 'rpt-m.cec.com.cn/smartbi/vision/share.jsp?resid=I8a8b9090018bf538f538a661018c0070e8013177&hiddenParamPanel=true' }
      ],
      action2: [
        {text: '管理岗位', to: '/managerMsg'},
        {text: '科技岗位', to: '/technologyMsg'},
        {text: '技能岗位', to: '/skillMsg'},
      ],
       ownOrgName:"全集团",//是否是二级有企业单位进来
    }
  },
  watch: {
    showPopover1(val) {
      if(val) {
        let el = document.querySelector(".nav-popover1");
        let elHeight = el.offsetHeight + 5;
        el.style.top = `-${elHeight}px`
      }
    },
    showPopover2(val) {
      if(val) {
        let el = document.querySelector(".nav-popover2");
        let elHeight = el.offsetHeight + 5;
        el.style.top = `-${elHeight}px`
      }
    }
  },
  methods: {
    handleClick(index) {
      if (!index && this.showPopover1 || index && this.showPopover1) {
        this.showPopover1 = false;
        if (index && !this.showPopover1) this.showPopover2 = true;
      } else if (!index && this.showPopover2 || index && this.showPopover2) {
        this.showPopover2 = false;
        if (!index && !this.showPopover2) this.showPopover1 = true;
      } else if (!index && !this.showPopover1) {
        this.showPopover1 = true;
      } else if (index && !this.showPopover2) {
        this.showPopover2 = true;
      }

      this.active = this.lastActive
    },
    handleToGo(item, path, active) {
      this.active = active
      this.lastActive = active
      //为解决每次进入到首页，切换的含非全日制会在缓存里，刷新页面还是含非全日制的数据
      sessionStorage.setItem("stdyForm", "全日制");
      // if (this.$route.path === path) return false; //判断当前路由是不是和导航一致  一致不刷新
      if (item.text == '人员查询') {
        let orgName = sessionStorage.getItem("ownOrgName")
        lx.ui.openApp({
          type: 'workbench',//
          appId: '15868416-11583488',
          // url: 'http://rpt-m.cec.com.cn/smartbi/vision/share.jsp?resid=I8a8b9090018b64df64df5052018bb9a6dd7c29d0',
          url: 'http://rpt-m.cec.com.cn/smartbi/vision/openresource.jsp?resid=I8a8b9090018bf538f538a661018c0070e8013177&hiddenParamPanel=true' +
            '&paramsInfo=[{"name":"二级企业","value":"'+orgName+'","displayValue":"'+orgName+'","stanbyValue":[["'+orgName+'","'+orgName+'"]]}]',
          // url: 'http://rpt-m.cec.com.cn/smartbi/vision/index.jsp',
          success: function (res) {

          },
          fail: function (err) {
            console.log("err", err)
            Toast.fail(err);
          },
        })
      } else {
        let lastIndex = window.location.href.lastIndexOf('/');
        let origin = window.location.href.substr(0, lastIndex);
        window.location.href = `${origin}${path}`;
      }

    },
    bodyClick(e) {
      const el = document.querySelector('#bottom-nav');
      if (el && !el.contains(e.target)) {
        this.showPopover1 = false;
        this.showPopover2 = false;
      }
    }
  },
  mounted() {
    //从sessionStorage中获取是二级单位还是全集团
    this.ownOrgName = sessionStorage.getItem("ownOrgName")
    if( this.ownOrgName!='全集团'){
      this.action1.unshift({text: '集团人才概况', to: '/hrIndexTwo'},)
    }
    if( this.ownOrgName=='全集团'){
      this.action1.pop()
    }

    // 导航栏active显示
    if (this.action1.some(item => {
      return item.to == this.$route.path
    })) {
      this.active = 0;
      this.lastActive = 0;
    } else if (this.action2.some(item => {
      return item.to == this.$route.path
    })) {
      this.active = 1;
      this.lastActive = 1;
    }
  },
  created() {
    document.addEventListener('click', this.bodyClick)
  },
  beforeDestroy() {
    document.addEventListener('click', this.bodyClick)
  }
}
</script>

<style lang="scss" scoped>
// #bottom-nav {
//   position: relative;
//   z-index: 1;
// }
::v-deep .van-tabbar-item {
  font-size: 0.3rem;
  position: relative;

  .van-icon {
    font-size: 0.5rem;
    margin-bottom: 0.1rem;
  }
}

::v-deep .van-tabbar.van-tabbar--fixed {
  height: 1.3rem;
  z-index: 15;
}

.van-tabbar-item__text {
  font-size: 0.28rem;
}

::v-deep .van-tabbar-item__icon {
  margin-bottom: 0.15rem;
}

.van-tabbar-item__text .nav-popover {
  position: absolute;
  // top: -3.4rem;
  transform: translateX(-10px);
  background: #fff;
  padding: 10px 14px;
  box-shadow: 10px 6px 16px 0px rgba(74, 103, 157, 0.1);
  border-radius: 6px 6px 0px 0px;
  text-align: center;
}

.van-tabbar-item__text .nav-popover p {
  display: block;
  padding: 5px;
  border-bottom: 1px solid #cccccc;
  font-size: 0.3rem;
  line-height: 2;
}

.van-tabbar-item__text .nav-popover p > span {
  display: block;
  padding: 5px;
}

.van-tabbar-item__text .nav-popover p:hover span {
  background: #eeeeee;
}

.van-tabbar-item__text .nav-popover p:nth-of-type(1) {
  padding-top: unset;
}

.van-tabbar-item__text .nav-popover p:nth-last-of-type(1) {
  border-bottom: unset;
  padding-bottom: unset;
}

.van-tabbar-item--active {
  color: unset;
}
</style>
