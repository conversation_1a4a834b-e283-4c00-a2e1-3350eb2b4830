<template>
  <div :style="{width: '100%',height: 'calc(100vh - 1.3rem)',display: 'flex',flexDirection: 'column'}">
    <div class="content">
      <!-- 内容哈 -->
      <div class="mainBox">
        <div class="titleContainer">
          <public-titles :title="moduleTitle" :text="['指按企业，统计职工的分布情况。']">
            <template v-slot:subTitle>
                <span :style="{color: '#666666',marginLeft: '0.1rem'}">(合计{{total}}人)</span>
            </template>
          </public-titles>
          <div class="doduleBox" style="padding-top:0.15rem">
            <div class="tableBox">
              <div class="kakaBox">
                <van-sticky>
                <div class="title">
                  <ul>
                    <li>序号</li>
                    <li>姓名</li>
                    <li>所属二级企业</li>
                    <li>单位名称</li>
                    <li>现任职务</li>
                  </ul>
                </div>
                </van-sticky>
                <div class="contentBox">
                  <ul v-for="(item,index) in data" :key="index">
                    <li>{{index + 1}}</li>
                    <li v-if="moduleTitle=='博士职工名单'" :style="{color: '#4481F6'}" @click="jumpDetailPage(item)">{{item.emp_nm}}</li>
                    <li v-else>{{item.emp_nm}}</li>
                    <li>{{item.scd_entp_nm}}</li>
                    <li>{{item.cor_entp_nm}}</li>
                    <li>{{item.dty}}</li>
                  </ul>

                </div>
              </div>
            </div>
            <div :style="{marginTop:'0.5rem',marginBottom:'0.2rem'}">
              <p :style="{color: '#969799',textAlign:'center'}" v-show="loading">
                <van-loading size="24px" v-show="loading">加载中...</van-loading>
              </p>
              <p :style="{fontSize: '0.3rem',color: '#969799',textAlign:'center'}" v-show="finished && !loading">已经到底了</p>
              <div class="load-btn">
                <van-button size="small" color="#3E7BFA" @click="onLoad" v-show="!finished && !loading">加载更多</van-button>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import publicTitles from '@/components/publicTitles.vue'
import { setDocumentTitle } from '@/utils/document'
export default {
  name: 'moreData',
  components: {
    publicTitles
  },

  data() {
    return {
      loading: false,
      data: [],
      eduData: [],
      show: true,
      moduleTitle: ""
    }
  },
  watch: {
    dataObj: {
      deep: true,
      immediate: true,
      handler: function (val) {
        setDocumentTitle(this.$route.query.title);
        if (val && val.length > 10) {
          this.data = val.filter((item, index) => {
            return index < 10;
          })
        } else {
          this.data = val;
        }
      }
    }
  },
  computed: {
    dataObj() {
      if (this.$route.query.type == 'eduData') {
        return this.eduData;
      } else {
        return this.$store.state[this.$route.query.type][this.$route.query.params];
      }
    },
    total() {
      return this.dataObj.length;
    },
    finished() {
      return this.data.length == this.total
    }
  },
  methods: {
    onLoad() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.data = this.dataObj;
      }, 1000)
    },
    jumpDetailPage(row){
      let scrollX = window.scrollX;
      let scrollY = document.documentElement.scrollTop || document.body.scrollTop;
      sessionStorage.setItem('savedPositionDct', JSON.stringify({ x: scrollX, y: scrollY }));
      this.$router.push({path: '/employeeDetails',query: {unqId: row.unq_id}})
    }
  },
  mounted() {
    this.moduleTitle = this.$route.query.moduleTitle;
    if(sessionStorage.getItem('savedPositionDct')) {
      this.data = this.dataObj;
      setTimeout(()=>{
        sessionStorage.removeItem("savedPositionDct")
      },0)
    }
  },
}
</script>

<style  lang="scss" scoped>
.load-mask {
  background: unset;
  ::v-deep .van-loading__spinner {
    // width: unset;
  }
}
.mainBox {
  .tableBox {
    border: 1px solid #cccccc;
    border-radius: 0.08rem 0.08rem 0px 0px;
    font-size: 0.28rem;
    color: #222222;
    width: 6.4rem;
    ::v-deep .van-sticky--fixed{
      width: 6.4rem;
     margin: 0 auto;
     .title {
       border-radius: 0;
     }
    }
    .title {
      background: #ebf0fa;
      border-radius: 0.08rem 0.08rem 0px 0px;
      ul {
        padding: 0.15rem 0.12rem;
        li {
          color: #666666;
          position: relative;
          &:nth-child(2) {
            //   background-color: green;
            width: 0.84rem;
            &::after {
              right: -0.04rem;
            }
          }
          &:last-child {
            &::after {
              display: none;
            }
          }
          &::after {
            content: "";
            position: absolute;
            top: 0.08rem;
            right: -0.12rem;
            width: 1px;
            height: 0.22rem;
            background-color: #999999;
          }
        }
      }
    }
    .contentBox {
      ul {
        padding: 0.15rem 0.15rem;
        border-bottom: 1px solid #cccccc;
        li {
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-word;
        }
      }
    }
    ul {
      display: flex;
      justify-content: space-between;
      li {
        text-align: center;
        &:nth-child(1) {
          //   background-color: red;
          width: 0.56rem;
        }
        &:nth-child(2) {
          //   background-color: green;
          width: 0.84rem;
        }
        &:nth-child(3) {
          //   background-color: red;
          width: 1.68rem;
        }
        &:nth-child(4) {
          //   background-color: green;
          width: 1.12rem;
        }
        &:nth-child(5) {
          //   background-color: red;
          width: 1.12rem;
        }
      }
      &:last-child {
        border-bottom: 0 !important;
      }
    }
  }
}
::v-deep .van-sticky {
  .van-tabs {
    .van-tabs__wrap {
      height: 0.7rem;
    }
    .van-tabs__nav {
      margin: unset;
      border: unset;
      background: unset;
      height: 0.7rem;
      .van-tab {
        color: #222222;
        font-size: 0.3rem;
        height: 0.7rem;
        border: unset;
        background: #fff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        // border-radius: 0.1rem 0px 0px 0.1rem;
      }
      .van-tab:nth-last-of-type(1) {
        // border-radius: 0px 0.1rem 0.1rem 0px;
      }
      .van-tab--active {
        background: #3e7bfa;
        color: #fff;
      }
    }
  }
}

.discribe {
  padding-bottom: unset;
}

.content {
  flex: 1;
  margin: 0.13rem 0.25rem 0.4rem;
  //   background: #fff;
  //   border-radius: 3px;
  //   box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  box-sizing: border-box;
  //   overflow: hidden;
  display: flex;
  flex-direction: column;
  padding-bottom: 0.5rem;
}
.sh-tab2 {
  margin-top: 0.3rem;
  ::v-deep .van-tabs__wrap {
    height: 0.7rem;
  }
  ::v-deep .van-tabs__nav {
    border: unset;
    height: 0.7rem;
    .van-tab {
      font-size: 0.3rem;
      flex: unset;
      height: 0.7rem;
      color: #999999;
      background: #ffffff;
      box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
      border-radius: 0.1rem 0px 0px 0.1rem;
      border: 1px solid #999999;
      padding: 0.1rem;
      box-sizing: border-box;
    }
    .van-tab:nth-last-of-type(1) {
      margin-left: -1px;
      border-radius: 0px 0.1rem 0.1rem 0px;
    }
    .van-tab--active {
      color: #3e7bfa;
      border: 1px solid #3e7bfa;
    }
  }
}

.sh-tab3 {
  position: fixed;
  right: calc(0.2rem - 2px);
  z-index: 999;
  ::v-deep .van-tabs__wrap {
    height: auto;
    .van-tabs__nav {
      border: unset;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      height: 3rem;
      word-wrap: break-word;
      margin: unset;
      .van-tab {
        font-size: 0.3rem;
        color: #999999;
        background: #ffffff;
        box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
        border-radius: 0.1rem 0.1rem 0px 0px;
        border: 1px solid #999999;
        box-sizing: border-box;
        padding: 0 0.1rem;
        .van-tab__text {
          display: flex;
          width: 20px;
          padding: 0 14px;
          height: 100%;
          line-height: 2;
          word-break: break-all;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          text-align: center;
        }
      }
      .van-tab:nth-last-of-type(1) {
        margin-top: -1px;
        border-radius: 0px 0px 0.1rem 0.1rem;
      }
      .van-tab--active {
        color: #3e7bfa;
        border: 1px solid #3e7bfa;
      }
    }
  }
}

.main_container {
  flex: 1;
  display: flex;
  position: relative;
  margin-top: 0.2rem;
  padding-left: 0.4rem;
  padding-right: 0.7rem;
  padding-bottom: 0.4rem;
  box-sizing: border-box;
  overflow: hidden;
}

.commin-ul {
  flex: 1;
  padding-right: 0.1rem;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  .commin-li {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 0.3rem;
    line-height: 0.7rem;
    box-sizing: border-box;
    span {
      font-size: 0.3rem;
    }
    span:nth-of-type(1) {
      min-width: 12px;
      color: #222222;
    }
    span:nth-of-type(2) {
      text-indent: 12px;
      color: #222222;
      // flex: 5;
      max-width: 60vw;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    span:nth-of-type(3) {
      flex: 1;
      text-align: right;
      color: #373f6d;
    }
  }
}
.load-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateX(0.26rem);
  ::v-deep .van-button {
    box-shadow: 2px 3px 10px 0px rgba(83, 95, 115, 0.29);
    border-radius: 3px;
    padding: 0.1rem;
    box-sizing: border-box;
    height: 0.72rem;
    width: 2.1rem;
    .van-button__content {
      font-size: 0.32rem;
    }
  }
}

/* ::-webkit-scrollbar ,兼容chrome和safari浏览器 */
.commin-ul::-webkit-scrollbar {
  display: none;
}
/* 兼容火狐 */
.commin-ul {
  scrollbar-width: none;
}

/* 兼容IE10+ */
.commin-ul {
  -ms-overflow-style: none;
}

::v-deep .commin-li {
  position: relative;
}
::v-deep .data_title_all {
  border: 1px solid black;
  position: absolute;
  left: 12px;
  top: 25px;
  z-index: 99;
  font-size: 12px;
  line-height: 12px;
  padding: 7px;
  white-space: nowrap; //强制一行展示
  background-color: rgb(255, 255, 255);
  color: #222222;
  border-radius: 3px;
}
</style>
