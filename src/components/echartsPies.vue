<template>
  <div class="barBox">
    <div class="echart" ref="echarts" :style="{ float: 'left', width: '100%', height: echartsWidth,'marginTop':'0.18rem'  }"></div>
    <div v-show="toChild1.length==0" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import { findLargest } from '@/utils/chart';
export default {
  name: "echartsPie",
  components: {
  },
  props: {
    title: "",
    toChild: {
      type: Array,
      default: () => {
        return []
      }
    },
    pieColorList: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  data() {
    return {
      chart: null,
      labelLargest: 0,
      echartsFontSize: '13px',
      echartsWidth: '290px',
      toChild1: this.toChild,
      radiusSize: ['28%', '49%'],
      titleTop:'33%'
    }
  },
  mounted() {
    //判断是IOS还是安卓系统
    // const u = navigator.userAgent
    // const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
    // if (isiOS) {
    //   this.echartsFontSize = '13px'
    //   this.radiusSize = ['28%', '49%']
    // } else {
    //   this.echartsFontSize = '12px'
    //   this.radiusSize = ['25%', '40%']
    // }
    //获取移动设置的分辨率
    let diveceWidth = document.documentElement.clientWidth
    if (diveceWidth > 405) {
      this.echartsWidth = '330px'
      this.echartsFontSize = '13px'
      this.radiusSize = ['28%', '49%']
      this.titleTop='33%'
    } else {
      this.echartsWidth = '350px'
      this.echartsFontSize = '12px'
      this.radiusSize = ['24%', '35%']
      this.titleTop='35%'
    }
  },
  /**
     * 深度监听 图表生成之后 传定时刷新数据进来 由于数据不能动态传值，所以子组件使用深度监听来监控数据变化
     */
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    toChild: {
      deep: true,
      handler(newVal, oldVal) {
        this.getByrsEcharts(newVal) //浏览器刷新
        this.toChild1 = newVal
      }
    },
    title: {
      deep: true,
      handler(newVal, oldVal) {
        if(this.chart) {
          this.title = newVal
        }
      }
    },

  },
  methods: {
    //环形图的开始
    getByrsEcharts(data) {
      if (data && data.length > 0) {
        // this.labelLargest = findLargest(data.map(item => { return item.name }));
        //这步操作是为了，当数据为0时，数据标签不显示
        data && data.forEach(element => {
          if(element.value=='0'){
            element.value=null
          }
        });
        
        var option = {
          animation: true,
          tooltip: {
            show: true,
            extraCssText: 'z-index:2',
          },
          grid: {
            top: "3%",
            left: "0%",
            right: '0%',
            bottom: 0,
            containLabel: true,
          },
          // 用来标志图表数据，data字段的数组需要对应每个柱条的名称，鼠标hover到最顶部的legend标志，可以标志对应的图，点击legend标志会隐藏对应的图
          legend: {
            x: 'center',
            y: 'bottom',
            itemWidth: 16,
            itemHeight: 16,
            itemGap: 20,
            // icon: "rect",
            textStyle: {
              fontSize: 15,
              color: '#222222'
            },
            formatter: (params, index) => {
              let label = params.length < this.labelLargest ? params.padEnd(this.labelLargest, '\u3000') : params;
              return [`${label}`].join('\n')
            },
          },
          title: {
            text: this.title,
            left: 'center',
            top: this.titleTop,
            textStyle: {
              fontSize: 16
            }
          },
          series: [
            {
              color: this.pieColorList,
              // name: 'Access From',
              type: 'pie',
              radius: this.radiusSize,
              center: ['50%', '40%'],
              minAngle: 10,  // 最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
              avoidLabelOverlap: true,   // 是否启用防止标签重叠策略
              // avoidLabelOverlap: false,
              animationDuration: 2000,
              animationEasing: "cubicInOut",
              bottom: '10%',
              label: {
                show: true,
                // formatter: '{b}\n{d}%',
                formatter: function (params) {
                  return params.name + '\n' + (params.value * 1).toLocaleString();
                },
                // textStyle: {
                fontSize: this.echartsFontSize,
                color: '#222222',
                // }
              },
              labelLine: {
                show: true,
                length: 10,//调整标签线的位置长短
                length2: 10
              },
              data: data
            }
          ]
        }
      }

      //   const byrsBar = echarts.init(document.getElementById(`barIdName`))// 图标初始化
      const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
      byrsBar.clear();
      setTimeout(() => {
        if (option) {
          byrsBar.setOption(option)// 渲染页面
          // 随着屏幕大小调节图表
          window.addEventListener('resize', () => {
            byrsBar.resize()
          })
        }
      }, 500);
    },

  },
  created() {

  }
}
</script>
<style lang="scss" scoped>
.barBox {
  width: 100%;
  overflow: hidden;
  // background: url("/img/renzi/renziFormBg.png");
  // background-size: 100% 100%;
  position: relative;
  top: 3px;
  .title {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .desc {
      font-size: 24px;
      color: #fff;
      font-weight: normal;
    }
    span {
      color: #7ac8ff;
      font-size: 16px;
    }
  }
  // .echart {
  //   padding: 20px 20px 15px;
  // }
  .noData {
    color: #fff;
    position: absolute;
    // left: 42%;
    // margin: 60px auto;
  }
}
</style>


<style lang="scss" scoped>
</style>