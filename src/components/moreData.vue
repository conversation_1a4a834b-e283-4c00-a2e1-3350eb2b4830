<template>
	<div :style="{width: '100%',height: 'calc(100vh - 1.3rem)',display: 'flex',flexDirection: 'column'}">
        <van-sticky :offset-top="0">
            <van-tabs class="sh-tab1" type="card" @click="onClick1" v-show="isEdu" :active="propsObject.stdyForm">
                <van-tab title="全日制" :name="'全日制'"></van-tab>
                <van-tab title="含非全日制" :name="'含非全日制'"></van-tab>
            </van-tabs>
        </van-sticky>

        <div class="content">
            <div class="tTop">
                <div class="moreTitle">
                    <div class="name">{{moduleTitle}}</div>
                    <div class="unit">单位：人</div>
                </div>
                <div class="discribe" v-if="desc && sxmoreList=='' && consldEntpFlg==''">
                    <div class="detail">
                    {{desc}}
                    </div>
                </div>
            </div>
            <van-tabs class="sh-tab2" type="card" @click="onClick2" v-show="isEdu" :active="propsObject.educate">
                <van-tab title="博士研究生" :name="'博士研究生'"></van-tab>
                <van-tab title="硕士研究生及以上" :name="'硕士研究生及以上'"></van-tab>
            </van-tabs>
            <!-- 内容 -->
            <div class="main_container">
                <van-tabs class="sh-tab3" type="card" @click="onClick3" v-show="isEdu" :active="propsObject.ratio">
                    <van-tab title="人数" :name="'人数'"></van-tab>
                    <van-tab title="占比" :name="'占比'"></van-tab>
                </van-tabs>
                <ul class="commin-ul">
                    <li v-for="(item,index) in data" 
                    :key="index" 
                    class="commin-li" 
                    v-tooltip
                    :data-name="item.pur_org_no">
                        <span>{{index + 1}}</span>
                        <span>{{item.pur_org_no}}</span>
                        <span v-if="!isEdu">{{Number(item.pur_mth_amt).toLocaleString()}}</span>
                        <span v-else-if="isEdu && propsObject.ratio == '人数' && !loading">{{Number(item.pur_mth_amt).toLocaleString()}}</span>
                        <span v-else-if="isEdu && propsObject.ratio == '占比' && !loading">{{item.pur_mth_amt}}%</span>
                    </li>
                    <p :style="{color: '#969799',lineHeight: '50px',textAlign:'center'}" v-show="loading">
                        <van-loading size="24px" v-show="loading">加载中...</van-loading>
                    </p>
                    <p :style="{fontSize: '0.3rem',color: '#969799',lineHeight: '50px',textAlign:'center'}" v-show="finished && !loading">已经到底了</p>
                    <div class="load-btn">
                        <van-button size="small" color="#3E7BFA" @click="onLoad" v-show="!finished && !loading">加载更多</van-button>
                    </div>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import { contentTitle } from '@/utils/chart';
import { getHrData } from "@/http/api";
import { setDocumentTitle } from '@/utils/document'
export default {
    name: 'moreData',
    directives: {
        tooltip: {
            inserted: (el, binding, vnode) =>{
                // 1.手指触摸屏幕
                let name = el.getAttribute('data-name');
                el.ontouchstart =  (e) => {
                    if(name.length > 10){
                        const itemDom = document.createElement("p");
                        itemDom.setAttribute('class', 'data_title_all');
                        itemDom.textContent = name;
                        el.appendChild(itemDom);
                        // 3.手指离开屏幕
                        document.ontouchend = (e) => {
                            if(el.lastElementChild.className == 'data_title_all') {
                                setTimeout(() => {
                                    el.removeChild(el.lastElementChild)
                                },1000)
                            }
                            document.ontouchmove = document.ontouchend = null;
                        }
                    }
                }
            }
        }
    },
    data() {
        return {
            loading: false,
            data: [],
            eduData: [],
            show: true,
            desc: "",
            sxmoreList:"",//这个是取是否在上一页面点击筛选后才进的更多的页面，如果筛选后点击到查看更多页面，就把描述（查看更多的话术）隐藏
            consldEntpFlg:"",//这个是取是否在上一页面点击筛选控股情况后才进的更多的页面，如果筛选后点击到查看更多页面，就把描述（查看更多的话术）隐藏
            propsObject: {
                "stdyForm": "全日制", // 0 1是否为全日制
                "educate": "博士研究生", // 学历（0：博士研究生；1：硕士及以上）
                "ratio": "人数", // 类型（0：人数；1：占比）
                "moreChooseType": "0",	//  0:全集团；1：按业务板块；2：按二级单位
                "moreList": "",// 业务板块或二级单位
            },
            moduleTitle:""
        }
    },
    watch: {
        dataObj: {
            deep: true,
            immediate: true,
            handler: function(val) {
                setDocumentTitle(this.$route.query.title);
                if(val && val.length > 15) {
                    this.data = val.filter((item,index) => {
                        return index < 15;
                    })
                } else {
                    this.data = val;
                }    
            }
        }
    },
    computed: {
        isEdu() {
            return [this.$route.query.type] == 'eduData'
        },
        dataObj() {
            if(this.$route.query.type == 'eduData') {
                return this.eduData;
            } else {
                return this.$store.state[this.$route.query.type][this.$route.query.params];
            } 
        },
        total() {
            return this.dataObj.length;
        },
        finished() {
            return this.data.length == this.total
        }
    },
    methods: {
        onClick1(name, title) {
            this.propsObject['stdyForm'] = name;
            this.initData();
        },
        onClick2(name, title) {
            this.propsObject['educate'] = name;
            this.initData();
        },
        onClick3(name, title) {
            this.propsObject['ratio'] = name;
            this.initData();
        },
        onLoad() {
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
                this.data = this.dataObj;
            },1000)
        },
        //初始化数据
        initData() {
            let params = {
                mapperInterface: ["employeeDegree_Educate", "employeeDegreePost_Educate", "employeeDegreeSeniority_Educate", "employeeHightDegree_Educate", "employeeHightDegreeRatio_Educate", "employeeSchool_Educate", "employeeSpecialized_Educate"],
                page: "Educate"
            }
            params = { ...params, ...this.propsObject }
            this.loading = true;
            getHrData(params).then((res) => {
            if (res && res.code == 200) {
                if(this.propsObject.ratio == '人数') {
                    this.eduData = res.data[this.$route.query.params];
                } else {
                    this.eduData = res.data['employeeHightDegreeRatio_Educate']   
                }
                this.loading = false;
            }
            }).catch((err) => {
                this.loading = false;
                console.log(err);
            });
        },
    },
    mounted() {
        if(this.isEdu) {
            let params = JSON.parse(sessionStorage.getItem('propsObject'));
            this.propsObject = params;
            this.initData();
        }
        this.desc = JSON.parse(sessionStorage.getItem('desc'));
        this.moduleTitle=  this.$route.query.moduleTitle;
        this.sxmoreList =  this.$route.query.moreList;
        this.consldEntpFlg = this.$route.query.consldEntpFlg;
    },
}
</script>

<style  lang="scss" scoped>
.load-mask {
    background: unset;
    ::v-deep .van-loading__spinner {
        // width: unset;
    }
}
.moreTitle{
    display:flex;
     padding: 0.5rem 0.3rem 0;
     justify-content: space-between;
     align-items: center;
     .name{
         font-size: .36rem;
         color: #222;
         font-weight: 600;
     }
}
.unit {
    text-align: right;
    font-size: 0.3rem;
    color: #666666;
   
    box-sizing: border-box;
}

::v-deep .van-sticky {
    .van-tabs {
        .van-tabs__wrap {
            height: 0.7rem;
        }
        .van-tabs__nav {
            margin: unset;
            border: unset;
            background: unset;
            height: 0.7rem;
            .van-tab {
                color: #222222;
                font-size: 0.3rem;
                height: 0.7rem;
                border: unset;
                background: #fff;
                box-shadow: 5px 6px 20px 0px rgba(74,103,157,0.1);
                // border-radius: 0.1rem 0px 0px 0.1rem;
            }
            .van-tab:nth-last-of-type(1) {
                // border-radius: 0px 0.1rem 0.1rem 0px;
            }
            .van-tab--active {
                background: #3e7bfa;
                color: #fff;
            }
        }
    }
} 

.discribe {
    padding-bottom: unset;
}

.content {
    flex: 1;
    margin: .4rem .25rem .4rem;
    background: #fff;
    border-radius: 3px;
    box-shadow: 5px 6px 20px 0px rgba(74,103,157,0.1);
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.sh-tab2 { 
    margin-top: 0.3rem;
    ::v-deep .van-tabs__wrap {
        height: 0.7rem;
    }
    ::v-deep .van-tabs__nav {
        border: unset;
        height: 0.7rem;
        .van-tab {
            font-size: 0.3rem;
            flex: unset;
            height: 0.7rem;
            color: #999999;
            background: #FFFFFF;
            box-shadow: 5px 6px 20px 0px rgba(74,103,157,0.1);
            border-radius: 0.1rem 0px 0px 0.1rem;
            border: 1px solid #999999;
            padding: 0.1rem;
            box-sizing: border-box;
        }
        .van-tab:nth-last-of-type(1) {
            margin-left: -1px;
            border-radius: 0px 0.1rem 0.1rem 0px;
        }
        .van-tab--active {
            color: #3E7BFA;
            border: 1px solid #3E7BFA;
        }
    }
}

.sh-tab3 {
    position: fixed;
    right: calc(0.2rem - 2px);
    z-index: 999;
    ::v-deep .van-tabs__wrap {
        height: auto;
        .van-tabs__nav {
            border: unset;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            height: 3rem;
            word-wrap: break-word;
            margin: unset;
            .van-tab {
                font-size: 0.3rem;
                color: #999999;
                background: #FFFFFF;
                box-shadow: 5px 6px 20px 0px rgba(74,103,157,0.1);
                border-radius: 0.1rem 0.1rem 0px 0px;
                border: 1px solid #999999;
                box-sizing: border-box;
                padding: 0 0.1rem;
                .van-tab__text {
                    display: flex;
                    width: 20px;
                    padding: 0 14px;
                    height: 100%;       
                    line-height: 2;
                    word-break: break-all;
                    justify-content: center;
                    align-items: center;
                    box-sizing: border-box;
                    text-align: center;
                }
            }
            .van-tab:nth-last-of-type(1) {
                margin-top: -1px;
                border-radius: 0px 0px 0.1rem 0.1rem;
            }
            .van-tab--active {
                color: #3E7BFA;
                border: 1px solid #3E7BFA;
            }
        }
    }
}

.main_container {
    flex: 1;
    display: flex;
    position: relative;
    margin-top: 0.2rem;
    padding-left: 0.4rem;
    padding-right: 0.7rem;
    padding-bottom: 0.4rem;
    box-sizing: border-box;
    overflow: hidden;
}

.commin-ul {
    flex: 1;
    padding-right: 0.1rem;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 100px;

    .commin-li {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 0.3rem;
        line-height: 0.7rem;
        box-sizing: border-box;
        span {
            font-size: 0.3rem;
        }
        span:nth-of-type(1) {
            min-width: 12px;
            color: #222222;
        }
        span:nth-of-type(2) {
            text-indent: 12px;
            color: #222222;
            // flex: 5;
            max-width: 60vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        span:nth-of-type(3) {
            flex: 1;
            text-align: right;
            color: #373F6D;
        }
    }
}
.load-btn {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translateX(0.26rem);
    ::v-deep .van-button {
        box-shadow: 2px 3px 10px 0px rgba(83,95,115,0.29);
        border-radius: 3px;
        padding: 0.1rem 0.5rem;
        box-sizing: border-box;
        .van-button__content {
            font-size: 0.25rem; 
        }
    }
}

/* ::-webkit-scrollbar ,兼容chrome和safari浏览器 */
.commin-ul::-webkit-scrollbar {
    display: none;
}
/* 兼容火狐 */
.commin-ul {
    scrollbar-width: none;
}

/* 兼容IE10+ */
.commin-ul {
    -ms-overflow-style: none;
}

::v-deep .commin-li{
    position: relative;
}
::v-deep .data_title_all {
    border: 1px solid black;
    position: absolute;
    left: 12px;
    top: 25px;
    z-index: 99;
    font-size: 12px;
    line-height: 12px;
    padding: 7px;
    white-space: nowrap; //强制一行展示
    background-color: rgb(255, 255, 255);
    color: #222222;
    border-radius: 3px;
    max-width: 80vw;
    white-space: normal;
    word-break: break-all;
}
</style>
