<template>
    <div class="employee_box">
        <div class="employee_card">
            <div class="employee_card_ct">
                <div class="employee_card_r1">
                    <span>{{ appForm.emp_nm }}</span>
                    <span>{{ appForm.scd_entp_nm }}</span>
                </div>
                <div class="employee_card_r2">
                    <span>{{ appForm.dty}}</span>
                </div>
                <div class="employee_card_r2">
                    <span>是否全日制博士：{{ appForm.ftm_hgt_dct_deg_flg }}</span>
                </div>
                <div class="employee_card_r3">
                    <span>博士毕业院校与专业</span>
                    <div class="employee_card_r4">
                        <span>{{ appForm.dct_deg_sch_nm }}</span>
                    </div>
                    <div class="employee_card_r4">
                        <span>{{ appForm.dct_deg_grdt_profn }}</span>
                    </div>
                </div>  
                <div class="employee_card_r3">
                    <span>全日制本科毕业院校与专业</span>
                    <div class="employee_card_r4">
                        <span>{{ appForm.ftm_hgt_edu_unv_dpm_grdt_sch_nm }}</span>
                    </div>
                    <div class="employee_card_r4">
                        <span>{{ appForm.ftm_hgt_edu_unv_dpm_grdt_profn }}</span>
                    </div>
                </div>  
            </div>
            <div class="employee_card_bt">{{ appForm.entp_ful_nm }}</div>
        </div>
    </div>
</template>

<script>
import { getHrData } from "@/http/api";
export default {
    name: 'EmployeeDetails',
    data() {
        return {
            appForm: {
                emp_nm: "-", //姓名
                dty: "-", //职务
                scd_entp_nm: "-", //单位
                ftm_hgt_dct_deg_flg: "-",//是否全日制
                dct_deg_sch_nm: "-", //博士毕业院校
                dct_deg_grdt_profn: "-", //博士所学专业
                ftm_hgt_edu_unv_dpm_grdt_sch_nm: "-", //全日制毕业院校
                ftm_hgt_edu_unv_dpm_grdt_profn: "-",  //本科所学专业
                entp_ful_nm: "-", //所属公司全称
            }
        }
    },
    mounted() {
        let params = {
            mapperInterface: ["doctorTitle_App_Home"],
            unq_id: this.$route.query.unqId
        }
        let dctDetailParams = JSON.parse(sessionStorage.getItem("dctDetailParams"));
        params = Object.assign(dctDetailParams,params);
        getHrData(params).then(res => {
            if(res.code == 200) {
                if(res.data && res.data.doctorTitle_App_Home) {
                    this.appForm = res.data.doctorTitle_App_Home[0]
                }
            }
        })
    }
}

</script>

<style  lang="scss" scoped>
.employee_box {
    padding: 0.28rem;
    box-sizing: border-box;
    width: 100%;
    height: 100vh;
    overflow: auto;
    background: #F7F8F9;
    .employee_card {
        background: url("../assets/img/employeeDetails/db.png") no-repeat;
        background-size: cover;
        border-radius: 0.1rem;
        .employee_card_ct {
            padding: 0.28rem 0.28rem 0;
            box-sizing: border-box;
            .employee_card_r1 {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #222222;
                span:nth-of-type(1) {
                    font-size: 0.36rem;
                    font-weight: 1000;
                }
                span:nth-of-type(2) {
                    font-size: 0.31rem;
                    font-weight: 500;
                }
            }
            .employee_card_r2 {
                margin: 0.28rem 0;
                span {
                    font-size: 0.28rem;
                    color: #222222;
                }
            }
            .employee_card_r3 {
                margin: 0.28rem 0;
                & > span {
                    font-size: 0.23rem;
                    color: #4F70E7;
                    position: relative;
                    padding-left: 0.23rem;
                }
                & > span::before {
                    content: '';
                    display: inline-block;
                    border-radius: 50%;
                    border: 0.065rem solid #4F70E7;
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
            .employee_card_r4 {
                margin: 0.14rem 0;
                span {
                    font-size: 0.28rem;
                    color: #222222;
                }
            }
        }
 
        .employee_card_bt {
            padding: 0.1rem 23% 0.1rem 0.31rem;
            background: url("../assets/img/employeeDetails/bottom-blue.png") 100% 100% no-repeat;
            background-size: 100% 100%;
            color: #fff;
            font-size: 0.31rem;
            border-radius: 0 0 0.1rem 0.1rem;
        }
    }
}

</style>