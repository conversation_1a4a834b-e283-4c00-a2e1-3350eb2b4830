<template>
  <div class="select-filter">
    <!-- 触发器 -->
    <div class="select-trigger" :class="{ disabled: disabled }" @click="handleTriggerClick">
      <div class="trigger-content">
        <span class="trigger-label">{{ label }}</span>
        <span class="trigger-value" :class="{ placeholder: !selectedText }">
          {{ selectedText || placeholder }}
        </span>
      </div>
      <van-icon name="arrow" class="trigger-arrow" />
    </div>

    <!-- 选择器弹层 -->
    <van-popup
      v-model="showPicker"
      position="bottom"
      round
      :close-on-click-overlay="true"
      @close="handleCancel"
    >
      <van-picker
        :columns="pickerColumns"
        :title="title"
        :default-index="defaultIndex"
        show-toolbar
        @confirm="handleConfirm"
        @cancel="handleCancel"
        @change="handlePickerChange"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'SelectFilter',
  props: {
    // 当前选中的值
    value: {
      type: [String, Number],
      default: ''
    },
    // 选项数组
    options: {
      type: Array,
      default: () => [],
      validator: (options) => {
        return options.every(option => 
          typeof option === 'object' && 
          option.hasOwnProperty('text') && 
          option.hasOwnProperty('value')
        )
      }
    },
    // 左侧标签文字
    label: {
      type: String,
      default: ''
    },
    // 占位文字
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 弹出层标题
    title: {
      type: String,
      default: '请选择'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showPicker: false,
      currentIndex: -1,
      tempValue: ''
    }
  },
  computed: {
    // 转换为 picker 需要的格式
    pickerColumns() {
      return this.options.map(option => ({
        text: option.text,
        value: option.value
      }))
    },
    // 默认选中的索引
    defaultIndex() {
      if (!this.value) return 0
      const index = this.options.findIndex(option => option.value === this.value)
      return index >= 0 ? index : 0
    },
    // 当前选中项的显示文字
    selectedText() {
      if (!this.value) return ''
      const selectedOption = this.options.find(option => option.value === this.value)
      return selectedOption ? selectedOption.text : ''
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.tempValue = newVal
      }
    }
  },
  methods: {
    // 点击触发器
    handleTriggerClick() {
      if (this.disabled) return
      this.showPicker = true
      this.tempValue = this.value
    },
    // picker 选择改变
    handlePickerChange(picker, value, index) {
      this.currentIndex = index
      this.tempValue = value.value
    },
    // 确认选择
    handleConfirm(value, index) {
      const selectedOption = this.options[index]
      if (selectedOption) {
        this.$emit('input', selectedOption.value)
        this.$emit('change', selectedOption.value, selectedOption)
        this.$emit('confirm', selectedOption.value, selectedOption)
      }
      this.showPicker = false
    },
    // 取消选择
    handleCancel() {
      this.showPicker = false
      this.tempValue = this.value
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.select-filter {
  width: 100%;
}

.select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0.4rem;
  background: #fff;
  border-radius: 0.12rem;
  border: 1px solid #ebedf0;
  cursor: pointer;
  transition: all 0.3s;

  &:active {
    background: #f7f8fa;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:active {
      background: #fff;
    }
  }
}

.trigger-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.trigger-label {
  font-size: 0.32rem;
  color: #323233;
  margin-right: 0.24rem;
  white-space: nowrap;
}

.trigger-value {
  font-size: 0.32rem;
  color: #323233;
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.placeholder {
    color: #c8c9cc;
  }
}

.trigger-arrow {
  font-size: 0.32rem;
  color: #c8c9cc;
  margin-left: 0.16rem;
  transition: transform 0.3s;
  flex-shrink: 0;
}

// 深度选择器样式
::v-deep .van-popup {
  .van-picker {
    .van-picker__toolbar {
      .van-picker__cancel,
      .van-picker__confirm {
        font-size: 0.32rem;
      }
      
      .van-picker__title {
        font-size: 0.36rem;
        font-weight: 500;
      }
    }
    
    .van-picker__columns {
      .van-picker-column__item {
        font-size: 0.34rem;
      }
    }
  }
}
</style>
