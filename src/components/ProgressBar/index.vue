<template>
  <div class="container">
    <div class="box" :class="disabled ? 'dsiabled-box' : ''">
      <div class="inner" :style="style"/>
    </div>
    <span class="percent">{{ percent }}</span>
  </div>
</template>

<script>
import { Decimal } from 'decimal.js'

export default {
  name: 'ProgressBar',
  data() {
    return {
      percent: `${(!isNaN(this.value) && !this.disabled) ? `${new Decimal(this.value).mul(new Decimal(100))}%` : '-'}`,
      style: {
        width: `${this.disabled ? 0 : (this.value * 100)}%`
      }
    }
  },
  props: {
    value: Number,
    disabled: Boolean
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 0.24rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .box {
    width: calc(100% - 0.66rem);
    height: 100%;
    background-color: #d5e6fe;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &.dsiabled-box {
      background-color: #ccc;
    }

    .inner {
      height: 100%;
      border-top-right-radius: 0.06rem;
      border-bottom-right-radius: 0.06rem;
      background: linear-gradient(90deg, #6BAAFF, #3B7AFD);
    }
  }

  .percent {
    width: 0.66rem;
    margin-left: 0.2rem;
  }
}
</style>