<template>
  <div :style="{position: 'relative'}">
    <div :id="id" style="width: calc(100vw - 1.23rem); height: 301px"></div>
    <div v-show="!data.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EchartBarX',
  props: {
    id: {
      type: String,
      default: ""
    },
    width: {
      type: String,
      default: ""
    },
    barItemColor: {
      type: String,
      default: ""
    },
    fontSize:{
       type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    isMinFont: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        var that = this
        this.chartData = []
        this.name = []
        this.categories = []
        if (data) {
          for (var i = 0; i < data.length && i < 12; i++) {
            if (this.name.indexOf(data[i]['pur_yr']) == -1) {
              this.name.push(data[i]['pur_yr'])
            }
          }
          for (var i = 0; i < data.length && i < 12; i++) {
            if (this.name[0] == data[i]['pur_yr']) {
              this.categories.push(data[i]['pur_org_no'])
              this.chartData.push(data[i]['pur_mth_amt'])
            }
          }
          that.initEcharts();
        }
      }
    },
  },
  data() {
    return {
      name: [],
      categories: [],
      chartData: [],
       barFontSize:this.fontSize ? this.fontSize:'12px'
    }
  },
  methods: {
    initEcharts() {
      // 在组件中直接使用this.$echarts来进行操作
      var myChart = this.$echarts.init(document.getElementById(this.id));
      // 指定图表的配置项和数据
      var option = {
        title: {
          show: false,
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText:'z-index:2',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          top: '10%',
          left: '2%',
          right: '2%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: this.categories,
          axisLine: {
            show: this.chartData.length ? true : false,
            lineStyle: {
              color: '#cccccc',
              width: 1
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true, // 不显示坐标轴上的文字
            interval: 0, // x轴间距
            // textStyle: {
            color: '#373F6D',
            fontSize: this.barFontSize,
            // },
            formatter: (params, index) => {
              // 数据小于5 不做换行展示
              if (this.chartData.length <= 5) return params;
              // x轴换行显示
              var newParamsName = "";
              var paramsNameNumber = params.length;
              var provideNumber = 2; //一行显示几个字
              var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
              if (paramsNameNumber > provideNumber) {
                for (var p = 0; p < rowNumber; p++) {
                  var tempStr = "";
                  var start = p * provideNumber;
                  var end = start + provideNumber;
                  if (p == rowNumber - 1) {
                    tempStr = params.substring(start, paramsNameNumber);
                  } else {
                    tempStr = params.substring(start, end) + "\n";
                  }
                  newParamsName += tempStr;
                }
              } else {
                newParamsName = params;
              }
              return newParamsName;
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: false,
          },
          splitLine: { //修改背景线条样式
            show: true,//是否展示 
            lineStyle: {
              type: 'dashed',
              color: "rgba(0,0,0,0.1)"
            }
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: this.width,
            showBackground: false,
            backgroundStyle: {
              color: "#F2F6FD"
            },
            data: this.chartData,
            // itemStyle: {
            //   normal: {
            color: this.barItemColor,
            label: {
              show: true,        // 开启显示
              distance: 5,     // 条柱之间的距离
              position: 'top',    // 右侧right显示
              // textStyle: {        // 数值样式
              color: '#373F6D',
              fontSize: this.isMinFont ? 10 : 13,
              // },
              formatter: (params, index) => {
                const reg = /(\d)(?=(?:\d{3})+$)/g;
                return params.data.toString().replace(reg, '$1,');
              }
            },
            itemStyle: {
              barBorderRadius: [4, 4, 0, 0]
            },
            // }
            // }
          }
        ]
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.clear();
      myChart.setOption(option, true);
    },
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
</style>
