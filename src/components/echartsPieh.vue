<template>
  <div class="barBox">
    <div class="echart" ref="echarts" :style="{ float: 'left', width: '100%', height: echartsWidth}"></div>
    <div v-show="toChild1.length==0" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  name: "echartsPie",
  components: {
  },
  props: {
    title: "",
    bannerValue: "",
    toChild: {
      type: Array,
      default: () => {
        return []
      }
    },
    pieColorList: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  data() {
    return {
      chart: null,
      labelLargest: 0,
      echartsFontSize: '13px',
      echartsWidth: '100px',
      toChild1: this.toChild,
      // radiusSize: ['48%', '79%'],
      titleTop: '33%',
      polarRadius:'165%'

    }
  },
  mounted() {
    // console.log(this.toChild, '昂昂昂');
    this.getByrsEcharts(this.toChild)
    //判断是IOS还是安卓系统
    // const u = navigator.userAgent
    // const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
    // if (isiOS) {
    //   this.echartsFontSize = '13px'
    //   this.radiusSize = ['28%', '49%']
    // } else {
    //   this.echartsFontSize = '12px'
    //   this.radiusSize = ['25%', '40%']
    // }
    // console.log(this.bannerValue,'嘿咐');
    
  },
  /**
     * 深度监听 图表生成之后 传定时刷新数据进来 由于数据不能动态传值，所以子组件使用深度监听来监控数据变化
     */
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    toChild: {
      deep: true,
      handler(newVal, oldVal) {
        this.getByrsEcharts(newVal) //浏览器刷新
        this.toChild1 = newVal
        // console.log(newVal, '哈嘿哈');
      }
    },
    title: {
      deep: true,
      handler(newVal, oldVal) {
        if (this.chart) {
          this.title = newVal
        }
      }
    },

  },
  methods: {
    //环形图的开始
    getByrsEcharts(data) {
      //获取移动设置的分辨率
    let diveceWidth = document.documentElement.clientWidth
    
    if (diveceWidth > 405) {
      console.log('diveceWidth1',diveceWidth);
      this.echartsWidth = '100px'
      this.echartsFontSize = '13px'
      this.radiusSize = ['48%', '89%']
      this.titleTop = '33%'
      this.polarRadius='165%'
    } else {
      console.log('diveceWidth2',diveceWidth);
      this.echartsWidth = '100px'
      this.echartsFontSize = '12px'
      this.radiusSize = ['44%', '85%']
      this.titleTop = '35%'
      this.polarRadius='135%'
    }
      // 进度百分比
      var value = this.bannerValue
      if (data && data.length > 0) {
        // this.labelLargest = findLargest(data.map(item => { return item.name }));
        //这步操作是为了，当数据为0时，数据标签不显示
        data && data.forEach(element => {
          if (element.value == '0') {
            element.value = null
          }
        });

        var option = {
          animation: true,
          tooltip: {
            show: false,
            extraCssText: 'z-index:2',
          },
          title: [{
            text: `{value|${value}}{title|%}`,
            left: 'center',
            top: '43%',
            textStyle: {
              color: '#fff',
              rich:{
                  title:{
                    fontSize: 10,
                    // paddingBottom:8
                  },
                  value:{
                    fontSize: 14,
                    fontWeight:550,
                  }
              },
            }
          }, {
            text: '达成率',
            left: 'center',
            top: '65%',
            textStyle: {
              color: '#fff',
              fontSize: 11,
              fontWeight: 'normal',
            }
          }],
          angleAxis: {
            show: false,
            // 后面的180表示圆环的跨越的角度， 如max设置为100*360/270,startAngle设置为-135
            max: 100 * 360 / 180,
            type: 'value',
            startAngle: 180,
            splitLine: {
              show: false
            }
          },
          // 修改环形的宽度
          barMaxWidth: 14.5,
          radiusAxis: {
            show: false,
            type: 'category'
          },
          polar: {
            // 设置环形的位置
            center: ['50%', '66%'],
            // 设置环形大小
            radius: this.polarRadius
          },
          series: [{
            type: 'bar',
            data: [
              {
                value: value,
                itemStyle: {
                        normal: { // 渐变色操作
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#FFFFFF' },  // 起始颜色
                                { offset: 1, color: 'rgba(255,255,255,0.34)' }])  // 结束颜色
                        }
                    },
              }
            ],
            barGap: '-100%',
            coordinateSystem: 'polar',
            z: 3
          }, {
            type: 'bar',
            data: [{
              value: 100,
              itemStyle: {
                // 这里是环形底色
                color: '#4996ee',
                borderWidth: 0
              }
            }],
            barGap: '-100%',
            coordinateSystem: 'polar',
            z: 1
          }]
        }
      }

      //   const byrsBar = echarts.init(document.getElementById(`barIdName`))// 图标初始化
      const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
      byrsBar.clear();
      setTimeout(() => {
        if (option) {
          byrsBar.setOption(option)// 渲染页面
          // 随着屏幕大小调节图表
          window.addEventListener('resize', () => {
            byrsBar.resize()
          })
        }
      }, 500);
    },

  },
  created() {

  }
}
</script>
<style lang="scss" scoped>
.barBox {
  width: 100%;
  overflow: hidden;
  // background: url("/img/renzi/renziFormBg.png");
  // background-size: 100% 100%;
  position: relative;
  top: 3px;
  .title {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .desc {
      font-size: 24px;
      color: #fff;
      font-weight: normal;
    }
    span {
      color: #7ac8ff;
      font-size: 16px;
    }
  }
  // .echart {
  //   padding: 20px 20px 15px;
  // }
  .noData {
    color: #fff;
    position: absolute;
    // left: 42%;
    // margin: 60px auto;
  }
}
</style>


<style lang="scss" scoped>
</style>