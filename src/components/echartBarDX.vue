<template>
  <div :style="{position: 'relative',minHeight:'300px'}">
    <div v-show="data.length" :id="id" style="width: calc(100vw - 1.2rem); height: 301px"></div>
    <div v-show="!data.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EchartBarSup',
  props: {
    id: {
      type: String,
      default: ""
    },
    barItemColor: {
      type: Array,
      default: () => {
        return []
      }
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        this.chartData = []
        this.name = []
        this.categories = []
        this.stack = []
        this.seriesOption = []
        if (data) {
          data = data.filter(item => {
            return item['pur_yr'] != '总数' //过滤总数
          })
          for (var i = 0; i < data.length && i < 10; i++) {
            if (this.name.indexOf(data[i]['pur_yr']) == -1) {
              this.name.push(data[i]['pur_yr'])
            }
            this.stack.push('pur_yr')
          }
          for (var i = 0; i < this.name.length; i++) {
            let tmpCategories = [];
            let tmpChartData = [];
            for (let j = 0; j < data.length; j++) {
              if (this.name[i] == data[j]['pur_yr']) {
                tmpCategories.push(data[j]['pur_org_no'])
                tmpChartData.push(data[j]['pur_mth_amt'])
              }
            }
            this.categories.push(...tmpCategories);
            this.chartData.push(tmpChartData)
          }
          this.categories = Array.from(new Set(this.categories));
          for (let i = 0; i < this.name.length; i++) {
            let obj = {
              name: this.name[i],
              type: 'bar',
              barWidth: "22px",
              showBackground: false,
              backgroundStyle: {
                color: "#F2F6FD"
              },
              data: this.chartData[i],
              color: this.barItemColor[i],
              label: {
                show: true,       // 开启显示
                distance: 5,     // 条柱之间的距离
                position: 'top',    // 右侧right显示
                // textStyle: {        // 数值样式
                color: '#373F6D',
                fontSize: '13px',
                // },
                formatter: (params, index) => {
                  const reg = /(\d)(?=(?:\d{3})+$)/g;
                  //此操作是用来让柱子上的数据太长重叠
                  let space = ''
                  for (let index = 0; index < 4; index++) {
                    space += '\xa0'

                  }
                  if (params.seriesName == '含非全日制') {
                    return space + params.data.toString().replace(reg, '$1,');
                  } else {
                    return params.data.toString().replace(reg, '$1,') + space;
                  }

                }
              },
              itemStyle: {
                barBorderRadius: [2, 2, 0, 0]
              },
            }
            this.seriesOption.push(obj)
          }
          this.initEcharts()
        }
      }
    }
  },
  data() {
    return {
      name: [],
      categories: [],
      chartData: [],
      stack: [],
      seriesOption: []
    }
  },
  methods: {
    initEcharts() {
      // 在组件中直接使用this.$echarts来进行操作
      var myChart = this.$echarts.init(document.getElementById(this.id));
      // 指定图表的配置项和数据
      var option = {
        title: {
          show: false,
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`
            if (params) {
              let str = `<div>${params[0].name}</div>`
              for (let i = 0; i < params.length; i++) {
                str += `<div class='data5'>${circle}${this.barItemColor[i]}"></span>${params[i]['seriesName']}：${(params[0] ? (params[i].data * 1).toLocaleString() : '0')}</div>`
              }
              return str
            }
          }
        },
        legend: {
          x: 'center',
          y: 'bottom',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 20,
          data: this.name.filter((item, index) => { return index < 4 }),
          textStyle: {
            fontSize: 15,
            color: '#333333',
          },
          // selected: {
          //   '其他': false,
          //   '总数': false
          // }
        },
        grid: {
          top: '10%',
          left: '0%',
          right: '3%',
          bottom: 60
        },
        xAxis: {
          type: 'category',
          data: this.categories,
          axisLine: {
            lineStyle: {
              color: '#c9c9c9',
              width: 1
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true, // 不显示坐标轴上的文字
            interval: 0, // x轴间距
            // textStyle: {
            color: '#373F6D',
            fontSize: '13px',
            // },
            formatter: (params, index) => {
              // 数据小于5 不做换行展示
              if (this.chartData.length <= 5) return params;
              // x轴换行显示
              var newParamsName = "";
              var paramsNameNumber = params.length;
              var provideNumber = 2; //一行显示几个字
              var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
              if (paramsNameNumber > provideNumber) {
                for (var p = 0; p < rowNumber; p++) {
                  var tempStr = "";
                  var start = p * provideNumber;
                  var end = start + provideNumber;
                  if (p == rowNumber - 1) {
                    tempStr = params.substring(start, paramsNameNumber);
                  } else {
                    tempStr = params.substring(start, end) + "\n";
                  }
                  newParamsName += tempStr;
                }
              } else {
                newParamsName = params;
              }
              return newParamsName;
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: false,
          },
          splitLine: { //修改背景线条样式
            show: true,//是否展示 
            lineStyle: {
              type: 'dashed',
              color: "rgba(0,0,0,0.1)"
            }
          }
        },
        series: this.seriesOption
      };

      // if (this.stack.length) {
      //   //求和
      //   var series = option["series"];
      //   var fun = function (params) {
      //     var datavalue = 0;
      //     for (var i = 0; i < series.length; i++) {
      //       datavalue += Number(series[i].data[params.dataIndex]);
      //     }
      //     return datavalue;
      //   }

      //   if (series[series.length - 1]) {
      //     series[series.length - 1]["label"]["show"] = true;
      //       series[series.length - 1]["label"]["formatter"] = fun;
      //   }
      //   //点击图例求和，没开启的可以忽略下面代码
      //   myChart.on("legendselectchanged", function (obj) {
      //     var b = obj.selected,
      //       d = [];
      //     for (var key in b) {
      //       if (b[key]) {
      //         for (var i = 0, l = series.length; i < l; i++) {
      //           var changename = series[i]["name"];
      //           if (changename == key) {
      //             d.push(i);
      //           }
      //         }
      //       }
      //     }
      //     var fun = function (params) {
      //       var datavalue = 0;
      //       for (var i = 0; i < d.length; i++) {
      //         for (var j = 0; j < series.length; j++) {
      //           if (d[i] == j) {
      //             datavalue += Number(series[j].data[params.dataIndex]);
      //           }
      //         }
      //       }
      //       return datavalue
      //     }
      //     for (var i = 0; i < series.length; i++) {
      //       series[i]["label"]["show"] = false;
      //     }
      //     for (var i = series.length - 1; i >= 0; i--) {
      //       var name = series[i]["name"];
      //       if (obj["selected"][name]) {
      //         series[i]["label"]["formatter"] = fun
      //         series[i]["label"]["show"] = true
      //         break;
      //       }
      //     }
      //   })
      // }
      // 使用刚指定的配置项和数据显示图表。
      myChart.clear();
      myChart.setOption(option, true);
    },
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
</style>
