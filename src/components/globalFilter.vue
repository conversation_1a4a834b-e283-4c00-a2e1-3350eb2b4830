<template>
  <div class="globalFilter">
    <div class="filterBtnBox" @touchmove="handleTouchMove">
      <!-- 筛选 -->
      <div class="filterBtn" @click="filterBtn" v-drag>筛选</div>
    </div>
    <van-popup v-model="open" position="top" round :style="{ height: '90%'}" @close="handleClose">
      <div :style="{height: '100%', display: 'flex', flexDirection: 'column'}">
        <div class="box">
          <div class="title">全部筛选</div>
          <!-- 并表 -->
          <div class="container">
            <h2 class="mb30 mt10">按控股情况</h2>
            <div class="content">
              <ul class="dropUl">
                <!-- <li v-for="(item, index) in joinTableList" :key="index" :class="{selected:index==current}" @click="toggleSelection4(item,index)"> -->
                <li v-for="(item, index) in joinTableList" :key="index" :class="{selected:index==current}" @click="toggleSelection4(item,index)">
                  <span :class="item && item.length>6 ? 'moreTextStyle':''">{{ item }}</span>
                </li>
              </ul>
            </div>
          </div>
          <!-- 业务版块 -->
          <div class="container" v-if="this.haveCompanyList && this.haveCompanyList.length > 0">
            <h2>业务版块</h2>
            <div class="content">
              <h4 class="mb30">五大主业</h4>
              <ul>
                <li v-for="(item) in ywFiveList" :key="item.value" :label="item.value" :value="item.id" :class="{ 'selected': isSelected(item.id) }" @click="toggleSelection(item.id)">
                  {{ item.value }}
                </li>
                <!-- <el-option v-for="item in options" :key="item.value" :label="item.value" :value="item.id">
                </el-option> -->
              </ul>
            </div>
            <div class="content">
              <h4 class="mb30">六大支撑</h4>
              <ul>
                <li v-for="(item) in ywSixList" :key="item.value" :label="item.value" :value="item.id" :class="{ 'selected': isSelected1(item.id) }" @click="toggleSelection1(item.id)">
                  {{ item.value }}
                </li>
              </ul>
            </div>
          </div>
          <!-- 所属企业 -->
          <div class="container" v-if="this.haveCompanyList && this.haveCompanyList.length > 0">
            <h2 class="mb30 mt10"><span>所属企业</span>
              <span v-if="this.haveCompanyList && this.haveCompanyList.length > 9" @click="showAll = !showAll">
                <strong>{{word}}</strong>
                <img v-if="word=='展开'" src="../assets/img/down.png" alt="">
                <img v-if="word=='收起'" src="../assets/img/up.png" alt="">
              </span>
            </h2>
            <div class="content">
              <ul class="dropUl">
                <li v-for="(item, index) in showHandleList" :key="index" :class="{ 'selected': isSelected2(item) }" @click="toggleSelection2(item,index)">
                  <span :class="item && item.length>6 ? 'moreTextStyle':''">{{ item }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- 省级行政区 -->
          <div class="container" v-if="this.$route && this.$route.path=='/hrIndex'">
            <h2 class="mb30 mt10"><span>省级行政区</span>
              <span v-if="this.areaList.length > 9" @click="showAllArea = !showAllArea">
                <strong>{{wordArea}}</strong>
                <img v-if="wordArea=='展开'" src="../assets/img/down.png" alt="">
                <img v-if="wordArea=='收起'" src="../assets/img/up.png" alt="">
              </span>
            </h2>
            <div class="content">
              <ul class="dropUl">
                <li v-for="(item, index) in showAreaList" :key="index" :class="{selected:index==n}" @click="toggleSelection3(item,index)">
                  <span :class="item && item.length>6 ? 'moreTextStyle':''">{{ item }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="btnBox">
          <span class="reset" @click="reset">重置</span>
          <span class="look" @click="submit">查看</span>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
let isScroll = false;
export default {
  name: 'globalFilter',
  directives: {
    drag: {
      inserted: function (el) {
        // 1.手指触摸屏幕
        el.ontouchstart = function (e) {
          let bottomHeight = document.querySelector('#bottom-nav > .van-tabbar--fixed').clientHeight;
          let disx = e.targetTouches[0].pageX - el.offsetLeft
          let disy = e.targetTouches[0].pageY - el.offsetTop
          let maxTop = document.body.clientHeight - el.offsetHeight - bottomHeight;
          let maxLeft = document.body.clientWidth - el.offsetWidth
          // 2.手指持续移动
          document.ontouchmove = function (e) {
            isScroll = true;
            let boxHeight = document.querySelector('.container').offsetHeight;
            document.querySelector('.filterBtnBox').style.height = `${boxHeight}px`;
            document.querySelector('.filterBtnBox').style.position = `fixed`;
            document.querySelector('.filterBtnBox').style.top = `0`;
            document.querySelector('.filterBtnBox').style.zIndex = `9999999`;
            document.querySelector('.filterBtnBox').style.overflow = `hidden`;
            document.querySelector('.filterBtnBox').style.backgroundColor = `transparent`;
            document.querySelector('body').style.overflowY = 'hidden';
            let left = e.targetTouches[0].pageX - disx
            let top = e.targetTouches[0].pageY - disy
            // 设置上下左右边界
            if (top <= 0) {
              top = 0
            } else if (top > maxTop) {
              top = `${maxTop}px`
            } else {
              top = `${top}px`
            }
            if (left <= 0) {
              left = 0
            } else if (left > maxLeft) {
              left = `${maxLeft}px`
            } else {
              left = `${left}px`
            }
            el.style.top = top
            el.style.left = left
          }
          // 3.手指离开屏幕
          document.ontouchend = function (e) {
            isScroll = false
            document.querySelector('.filterBtnBox').style.position = ``;
            document.querySelector('.filterBtnBox').style.height = `auto`;
            document.querySelector('.filterBtnBox').style.zIndex = ``;
            document.querySelector('.filterBtnBox').style.overflow = ``;
            document.querySelector('body').style.overflowY = 'auto';
            document.ontouchmove = document.ontouchend = null
          }
        }
      }
    }
  },
  data() {
    return {
      n: -1,
      current: -1,
      open: false,//是否显示筛选
      showAll: false,//控制所属企业展开收起
      showAllArea: false,//控制所属企业展开收起
      show: true,
      joinTableList: ["控股企业", "参股企业"],//并表数据
      setlectJoinTable: [],//并表选中的数据
      selectFiveList: [],//五大主业选中的数据
      ywFiveList: [{
        id: '01',
        value: '计算产业'
      },
      {
        id: '02',
        value: '集成电路'
      },
      {
        id: '03',
        value: '数据应用'
      },
      {
        id: '04',
        value: '高新电子'
      },
      {
        id: '05',
        value: '网络安全'
      }],//五大主业数据
      selectSixList: [],//六大支撑选中的数据
      ywSixList: [{
        id: '06',
        value: '供应链服务'
      },
      {
        id: '07',
        value: '资产管理'
      },
      {
        id: '08',
        value: '产业金融'
      },
      {
        id: '09',
        value: '产业园区'
      },
      {
        id: '10',
        value: '电子制造'
      },
      {
        id: '11',
        value: '显示产业'
      },],//六大支撑数据
      selectCompanyList: [],//所属企业选中的数据
      //所属企业
      haveCompanyList: [],
      // haveCompanyList: ["中电信息",
      // "中电财务",
      // "中电熊猫",

      // "桑达股份",

      // "华大半导体",

      // "冠捷科技",

      // "中国振华",

      // "中国长城",

      // "深科技",

      // "中电金投",

      // "彩虹集团",

      // "中电金信",

      // "上海浦软",

      // "中国软件",

      // "中国瑞达",

      // "数据产业集团",

      // "数字广东",

      // "中电易联",

      // "中国信安（电子六所）",

      // "中电惠融",

      // "中电锦江",

      // "中电互联",

      // "长风科技",

      // "长城网际",

      // "海南创新院",

      // "星河电子",

      // "华电有限",

      // "中电蓝海",

      // "中电数据",

      // "元器件交易中心",

      // "中电投控",

      // "华大九天",
      // "奇安信",
      // "飞腾",

      // "全集团"],
      //省级行政区选中的数据
      selectAreaList: [],
      //省级行政区数据
      areaList: [
        "北京市",
        "天津市",
        "河北省",
        "山西省",
        "内蒙古自治区",
        "辽宁省",
        "吉林省",
        "黑龙江省",
        "上海市",
        "江苏省",
        "浙江省",
        "安徽省",
        "福建省",
        "江西省",
        "山东省",
        "河南省",
        "湖北省",
        "湖南省",
        "广东省",
        "广西壮族自治区",
        "海南省",
        "重庆市",
        "四川省",
        "贵州省",
        "云南省",
        "西藏自治区",
        "陕西省",
        "甘肃省",
        "青海省",
        "宁夏回族自治区",
        "新疆维吾尔自治区",
        "台湾省",
        "香港",
        "澳门",
      ],
      fiveList: [],
      sixList: [],
      companyList: [],
      provinceList: [],
      //此筛选所有参数
      propObj: {
        district: "",  // 区域名称（目前为省份名称）
        districtType: "G",	// 区域类型（G：全球；P：省）
        moreChooseType: sessionStorage.getItem("ownOrgName") != "全集团" ? "2" : "0",// 0:全集团；1：按业务板块；2：按二级单位
        moreList: sessionStorage.getItem("ownOrgName") != "全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        consldEntpFlg: "",//并表，默认传空即传全集团
      },
    }
  },
  computed: {
    //所属企业企业实现展开收起功能
    showHandleList() {
      if (this.showAll == false) {  //收起状态-显示“展示”        
        var showList = [];  //定义⼀个空数组        
        if (this.haveCompanyList.length && this.haveCompanyList.length > 9) {  //控制显⽰前四个          
          for (var i = 0; i < 9; i++) {
            showList.push(this.haveCompanyList[i])  //将数组的前4条存放到showList数组中        
          }
        } else {
          showList = this.haveCompanyList;  //个数足够显示，不需要再截取        
        }
        return showList;  //返回当前数组      
      } else {  // 展开状态-显示“收起”      
        return this.haveCompanyList;
      }
    },
    word() {
      if (this.showAll == false) {  //对⽂字进⾏处理        
        return '展开'
      } else {
        return '收起'
      }
    },
    //省级行政区实现展开收起功能
    showAreaList() {
      if (this.showAllArea == false) {  //收起状态-显示“展示”        
        var showList = [];  //定义⼀个空数组        
        if (this.areaList.length > 9) {  //控制显⽰前四个          
          for (var i = 0; i < 9; i++) {
            showList.push(this.areaList[i])  //将数组的前4条存放到showList数组中        
          }
        } else {
          showList = this.areaList;  //个数足够显示，不需要再截取        
        }
        return showList;  //返回当前数组      
      } else {  // 展开状态-显示“收起”      
        return this.areaList;
      }
    },
    wordArea() {
      if (this.showAllArea == false) {  //对⽂字进⾏处理        
        return '展开'
      } else {
        return '收起'
      }
    },
  },
  watch: {
    propObj: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.district == "" && newVal.districtType == "G" && newVal.consldEntpFlg == "" && newVal.moreChooseType == "0" && newVal.moreList == "") {
          $(".discribBox").show()
          console.log(newVal,'001');
        } else {
          $(".discribBox").hide()
          console.log(newVal,'002');
        }
      }
    },
  },
  methods: {
    handleTouchMove(e) {
      if (isScroll && e.cancelable) {
        e.preventDefault()
      }
    },
    filterBtn() {
      this.open = true
    },
    //关闭筛选
    handleClose() {
      this.open = false
    },
    reset() {
      document.body.scrollTop = document.documentElement.scrollTop = 0
      this.open = false
      //清空所有选项
      this.selectFiveList = []
      this.selectSixList = []
      this.selectCompanyList = []
      this.n = -1
      this.current = -1
      //清空所有选项
      this.propObj = {
        district: "",  // 区域名称（目前为省份名称）
        districtType: "G",	// 区域类型（G：全球；P：省）
        moreChooseType: sessionStorage.getItem("ownOrgName") != "全集团" ? "2" : "0",// 0:全集团；1：按业务板块；2：按二级单位
        moreList: sessionStorage.getItem("ownOrgName") != "全集团" ? `'${sessionStorage.getItem("ownOrgName")}'` : "", // 业务板块或二级单位名称
        consldEntpFlg: "",//并表，默认传空即传全集团
      }

      
      this.$emit('reset'); //传参父组件
    },
    submit() {
      document.body.scrollTop = document.documentElement.scrollTop = 0
      this.open = false;
      let fiveSixList = [];
      let companyList = [];
      if ((this.fiveList && this.fiveList.length > 0) || (this.sixList && this.sixList.length > 0)) {
        fiveSixList = this.fiveList.concat(this.sixList)
      } else if (this.selectCompanyList.length > 0) {
        companyList = this.selectCompanyList.map(item => { return `'${item}'` });
      }
      //此判断是用来判断,如果是二级单位进来页面进行其他筛选，点击查看，需要将对应参数传过去
      if (sessionStorage.getItem("ownOrgName") && sessionStorage.getItem("ownOrgName") != "全集团") {
        if (fiveSixList.length) {
          this.propObj.moreList = fiveSixList.join()
        } else {
          // debugger
          companyList[0] = `'${sessionStorage.getItem("ownOrgName")}'`
          this.propObj.moreList = companyList && companyList.join()
        }
      } else {
        this.propObj.moreList = fiveSixList.length ? fiveSixList.join() : companyList.join(); //fiveSixList.join()后台数据改为”01，02，03“
      }

      //此判断是用来判断，如果选择了业务版块，又取消完，那么moreChooseType应该传0
      if (fiveSixList.length > 0) {
        this.propObj.moreChooseType = "1"
      } else if (companyList.length > 0) {
        this.propObj.moreChooseType = "2"
      } else {
        this.propObj.moreChooseType = "0"
      }

      
      this.$emit('submit', this.propObj); //传参父组件
    },
    //****点击业务版块——五大业主
    toggleSelection(index) {
      this.propObj.moreChooseType = "1"
      //业务版块和所属企业互斥，因此点击业务版块，将所属企业的值置为空
      this.companyList = []
      this.selectCompanyList = []
      if (this.isSelected(index)) {
        // 如果项目已选中，则取消选中
        const selectedIndex = this.selectFiveList.indexOf(index);
        this.selectFiveList.splice(selectedIndex, 1);
      } else {
        // 如果项目未选中，则添加到选中列表中
        this.selectFiveList.push(index);
      }
      this.fiveList = this.selectFiveList
      console.log(this.selectFiveList, '业务版块——五大业主');

    },
    isSelected(index) {
      return this.selectFiveList.includes(index);

    },
    //****点击业务版块——六大支撑
    toggleSelection1(index) {
      this.propObj.moreChooseType = "1"
      //业务版块和所属企业互斥，因此点击业务版块，将所属企业的值置为空
      this.companyList = []
      this.selectCompanyList = []
      if (this.isSelected1(index)) {
        // 如果项目已选中，则取消选中
        const selectedIndex = this.selectSixList.indexOf(index);
        this.selectSixList.splice(selectedIndex, 1);
      } else {
        // 如果项目未选中，则添加到选中列表中
        this.selectSixList.push(index);
      }
      this.sixList = this.selectSixList
      console.log(this.selectSixList, '六大支撑');
    },
    isSelected1(index) {
      return this.selectSixList.includes(index);

    },
    //****点击所属企业
    toggleSelection2(item) {
      //业务版块和所属企业互斥，因此点击所属企业，将业务版块的值置为空
      this.fiveList = []
      this.sixList = []
      this.selectFiveList = []
      this.selectSixList = [];
      if (this.isSelected2(item)) {
        // 如果项目已选中，则取消选中
        const selectedIndex = this.selectCompanyList.indexOf(item);
        this.selectCompanyList.splice(selectedIndex, 1);
      } else {
        // 如果项目未选中，则添加到选中列表中
        this.selectCompanyList.push(item);
      }

      console.log(this.selectCompanyList, '所属企业');
    },
    isSelected2(index) {

      return this.selectCompanyList.includes(index);

    },
    //****点击省级行政区
    toggleSelection3(item, index) {
      if (index != this.n) {
        this.n = index;
        this.propObj.district = item
        this.propObj.districtType = 'P'
      } else {
        this.n = -1;
        this.propObj.district = ""
        this.propObj.districtType = 'G'
      }
      console.log(this.propObj.district, '省级行政区');
    },
    //点击并表
    toggleSelection4(item, index) {
      if (index != this.current) {
        this.current = index;
        if (item == '控股企业') {
          this.propObj.consldEntpFlg = "1"
        } else {
          this.propObj.consldEntpFlg = "0"
        }
      } else {
        this.current = -1;
        this.propObj.consldEntpFlg = ''
      }



      console.log(this.propObj.consldEntpFlg, '并表');
    },

    onSelect(item) {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      this.show = false;
      Toast(item.name);
    },
  },
  mounted() {
    //console.log(sessionStorage.getItem("ownOrgName"),'ownOrgName');
    if (sessionStorage.getItem("ownOrgName") == '全集团') {
      //console.log("orgList", sessionStorage.getItem("orgList"))
      this.haveCompanyList = sessionStorage.getItem("orgList") && sessionStorage.getItem("orgList").split(',')
      // console.log(this.haveCompanyList,'我是筛选页面的打印数据');
    } else {
      this.haveCompanyList = null
    }


  },
}
</script>

<style  lang="scss" scoped>
.globalFilter {
  // position: relative;
  // z-index: 999;
  // ::v-deep .van-overlay {
  //   z-index: 9998 !important;
  //   background-color: rgba(188, 189, 191, 0.63) !important;
  // }
  // ::v-deep .van-popup {
  //   z-index: 9999 !important;
  // }
  color: #222222;
  font-family: PingFangSC-Regular, PingFa;
  .box {
    padding: 0 0.3rem 1rem;
    flex: 1;
    overflow-y: scroll;
    // height: 80vh;
    // background-color: pink;
    // overflow-y: auto;
  }
  .title {
    font-size: 0.42rem;
    font-weight: 600;
    text-align: center;
    margin-top: 0.3rem;
    margin-bottom: 0.5rem;
  }
  h2 {
    font-size: 0.36rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;

    strong {
      color: #666666;
      font-weight: 500;
    }
    img {
      width: 0.4rem;
      height: 0.23rem;
    }
  }
  h4 {
    font-size: 0.32rem;
    font-weight: 400;
    color: #456bc4;
    margin: 0.3rem 0 0 0.08rem;
  }
  .content ul {
    margin-bottom: 0.3rem;
    overflow: hidden;
    li {
      width: 2.1rem;
      height: 0.72rem;
      text-align: center;
      // line-height: 0.72rem;
      background: #eeeeee;
      font-size: 0.32rem;
      display: flex;
      float: left;
      justify-content: center;
      align-items: center;
      border-radius: 36px;
      margin-right: 0.28rem;
      margin-bottom: 0.3rem;
      border: 1px solid #eeeeee;
      &:nth-child(3n) {
        margin-right: 0;
      }
      span {
        padding: 0.14rem 0;
      }
    }
    .moreTextStyle {
      // line-height: 0.4rem;
      // font-size: 0.24rem;
      transform: scale(0.7);
    }
  }
  .content .selected {
    background-color: #fff;
    border: 1px solid #3e7bfa;
    color: #3e7bfa;
  }
}
</style>