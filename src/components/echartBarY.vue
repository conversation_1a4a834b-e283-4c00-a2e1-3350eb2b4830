<template>
  <div :style="{position: 'relative',width: '100%'}">
    <div :id="id" style="width: 100%; height: 409px"></div>
    <div v-show="!data.length" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>

<script>
import { findLargest } from '@/utils/chart';
export default {
  name: 'EchartsBar',
  props: {
    id: {
      type: String,
      default: ""
    },
    barItemColor: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        var that =this
        this.chartData = []
        this.name = []
        this.categories = []
        if (data) {
          for (var i = 0; i < data.length && i < 10; i++) {
            if (this.name.indexOf(data[i]['pur_yr']) == -1) {
              this.name.push(data[i]['pur_yr'])
            }
          }
          for (var i = 0; i < data.length && i < 10; i++) {
            if (this.name[0] == data[i]['pur_yr']) {
              this.categories.push(data[i]['pur_org_no'])
              this.chartData.push({
                value: data[i]['pur_mth_amt'] || 0,
                itemStyle: {
                  color: this.barItemColor
                }
              })
            }
          }
          if (this.categories) this.labelLargest = findLargest(this.categories);
          that.initEcharts();
        }
      }
    },
  },
  data() {
    return {
      name: [],
      categories: [],
      chartData: [],
      labelLargest: 0
    }
  },
  methods: {
    initEcharts() {
      // 在组件中直接使用this.$echarts来进行操作
      var myChart = this.$echarts.init(document.getElementById(this.id));
      let maxNumber = 0;
      if(this.chartData.length) {
         maxNumber = this.chartData.reduce((a,b)=>Number(a)+Number(b.value),0);  //累加所有值
        let maxValue = Math.max(...this.chartData.map(item =>{return item.value}));
        if((maxValue / maxNumber) > 0.6 ) maxNumber = maxNumber * 3;
      }
      // 指定图表的配置项和数据
      var option = {
        title: {
          show: true,
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText:'z-index:2',
          position:['20%','20%'],
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            // console.log(params,'嘿哈');
              let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`

              if (params) {
                return `
                <div>${circle}#2382E9"></span> ${params[0].name} ${(params[0] ? (params[0].value * 1).toLocaleString() : '0')}</div>
              `
              }
          }
        },
        legend: {},
        grid: {
          top: '0%',
          left: '1%',
          right: '0%',
          bottom: '-6%',
          containLabel: true
        },
        xAxis: {
          show: false,
          max: maxNumber
        },
        yAxis: {
          data: this.categories,
          inverse: true, //如果数据数组倒置排序，加上此代码
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: { //修改背景线条样式
            show: false
          },
          offset: 1,
          axisLabel: {
            color: "#222222",
            fontWeight: '400',
            fontSize: '18px',
            // align: 'left',
            // margin: 140,
            formatter: (params, index) => {
              let label = "";
              if (this.labelLargest > 6) {
                if (params.length > 6) {
                  label = `${params.substr(0, 6)}...`
                } else {
                  label = `${params.padEnd(6, '\u3000')}`
                  label = `${label.padEnd(9, '\u0020')}`
                }
              } else if (this.labelLargest <= 6) {
                label = params.length < this.labelLargest ? params.padEnd(this.labelLargest, '\u3000') : params;
              }
              return [`{a|${index + 1}}  ${label}`].join('\n')
            },
            rich: {
              a: {
                color: "#373F6D",
                fontSize: '18px',
                align: 'left',
                width: 12,
              }
            }
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: "14px",
            showBackground: true,
            data: this.chartData,
            itemStyle: {
              barBorderRadius: [0, 4, 4, 0],
            },
            label: {
              show: true,        // 开启显示
              distance: 5,     // 条柱之间的距离
              position: 'right',    // 右侧right显示
              // textStyle: {        // 数值样式
              color: '#373F6D',
              fontSize: '18px',
              // },
              formatter: (params, index) => {
                const reg = /(\d)(?=(?:\d{3})+$)/g;
                if(params.value.indexOf('.')!=-1) {
                  return params.value.toString().replace(reg, '$1,') + '%'; //占比
                } else {
                  return params.value.toString().replace(reg, '$1,');
                }     
              }
            },
          }
        ]
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.clear();
      myChart.setOption(option, true);
    }
  },
  mounted() {

  },
}
</script>

<style  lang="scss" scoped>
</style>
