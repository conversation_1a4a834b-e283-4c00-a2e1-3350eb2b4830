<template>
  <div class="barBox" style="position:relative">
    <div v-if="!toChild1 || toChild1.length<1" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.32rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
    <p v-if="toChild1 && toChild1.length>0" :style="{textIndent: '0px',fontSize: '0.32rem',color: '#222222',fontWeight: 'unset',paddingTop:'0.15rem'}">{{label}}：{{value ? value : 0}}{{label=='平均年龄'?'岁':'年'}}</p>
    <div class="echart" ref="echarts" :style="{ width: '100%', height: '200px'  }"></div>
    
  </div>
</template>
<script>
import * as echarts from 'echarts';
import { findLargest, getCharCount, findMaxCommaNo, getStrCode} from '@/utils/chart';
export default {
  name: "pictorialBar",
  components: {
  },
  props: {
    toChild: {

    },
    colorBg: {

    },
    label: {
      type: String,
      default: "平均工龄"
    },
    value: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      chart: null,
      colorBg1: this.colorBg,
      labelLargest: 0,
      maxComma: 0,
      toChild1:this.toChild
    }
  },
  /**
    * 深度监听 图表生成之后 传定时刷新数据进来 由于数据不能动态传值，所以子组件使用深度监听来监控数据变化
    */
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    toChild: {
      deep: true,
      handler(newVal, oldVal) {
        this.getByrsEcharts(newVal) //浏览器刷新
        this.toChild1 = newVal
        this.$forceUpdate();
      }
    }
  },
  mounted() {

  },
  methods: {
    handleClick() {
      this.dialogVisible = true
    },
    // 柱状图的开始
    getByrsEcharts(data) {
      if (data.length > 0) {
        this.labelLargest = findLargest(data.map(item => { return item.pur_mth_amt }));
        this.maxComma = findMaxCommaNo(data.map(item => {return item.pur_mth_amt}));
        var categories1 = [];
        var data1 = [];
        for (var i = 0; i < data.length; i++) {
          categories1.push(data[i]["pur_org_no"]);
          data1.push(data[i]["pur_mth_amt"]);
        }
      }
      var option = {
          grid: {
            top: -2,
            left: "2%",
            right: "0%",
            bottom: -15,
            containLabel: true,
          },

          tooltip: {
            show: false,
            extraCssText:'z-index:2',
          },
          legend: {
            show: false,
            textStyle: {
              fontWeight: "normal",
              color: "#fff",
            },
            itemWidth: 1, // 图例标记的图形宽度。
            itemHeight: 1,
          },
          xAxis: {
            // offset: -50,
            show: false,
            axisLabel: {
              fontSize: "18px",
              color: "#5DB3DC",
              rotate: 0, // 文本旋转角度
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              //修改背景线条样式
              show: false,
            },
          },
          yAxis: [
            {
              //左侧Y轴数据
              inverse: true, //如果数据数组倒置排序，加上此代码
              data: categories1,
              // 时间越长动画越慢
              animationDuration: 2000,
              animationEasing: "cubicInOut",

              axisLabel: {
                fontSize: 18,
                color: "#fff",
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                //修改背景线条样式
                show: false,
              },
              position: 'left',//数据居左显示
              axisLabel: {
                color: "#222222",
                verticalAlign: "top",//// 文字垂直对齐方式，默认自动。
                margin: 20,//此数据和grid中的left结合使用
                textStyle:{
                  // align:'left'
                },
                left:0,
                formatter: (value) => {
                  let valueStrCode = getStrCode(value);
                  for(let i = 0; i < (11 - valueStrCode); i++ ) { // 按照”50岁及以上” 字符量 11控制所有图表label
                    value += '\u0020'
                  }
                  return [`{a|${value}}`]
                },
                rich: {
                  a: {
                    color: "#222222",
                    fontSize: '18px',
                    align: 'left',
                    width: 90
                  }
                }
              },
            },
            {
              //右侧Y轴数据
              inverse: true, //如果数据数组倒置排序，加上此代码
              data: data1,
              position: 'right',//数据居右显示
              axisLabel: {
                verticalAlign: "top",//// 文字垂直对齐方式，默认自动。
                margin: 20,
                textStyle: {
                  color: '#222222',
                  fontSize: '18px',
                  // align: 'right',
                },
                formatter: (value) => {
                  let gap = 0;
                  let prefix = "";
                  if (value) {
                    gap = this.labelLargest - value.length;
                    let preStr = "";
                    preStr = preStr.padStart(gap,'0');
                    value = `${preStr}${(value * 1).toLocaleString()}`;
                    let commaNo = getCharCount(value,',');  //计算位符号数量
                    if(this.maxComma > commaNo) { // 位符号差距补位
                      let gapv = value.length + (this.maxComma - commaNo);
                      gap += (this.maxComma - commaNo);
                      value = value.padStart(gapv,',')
                    }
                    prefix = value.substr(0,gap);
                  }
                  value = value.replace(prefix,'');
                  return `{a|${prefix}}${value}`
                },
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: '18px',
                  },
                }
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                //修改背景线条样式
                show: false,
              },
            },
          ],
          series: [
            {
              type: "pictorialBar",
              symbolRepeat: true,
              z: 2,
              symbolMargin: 2, //图形的两边间隔
              symbol: "roundRect",//'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'
              // symbolClip: true, //是否裁剪图形
              symbolSize: [5, 15],
              // symbolPosition: "start", //图形的定位位置。
              symbolOffset: [-8, 6], //图形相对于原本位置的偏移。
              //stack: "total",
              data: data1,
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              // barWidth: 20,
              itemStyle: {
                barBorderRadius: [60],
                normal: {
                  color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    {
                      offset: 0,
                      color: this.colorBg1,
                    },
                    {
                      offset: 1,
                      color: this.colorBg1,
                    },
                  ]),
                },
              },
            },
            {
              // full data
              type: 'pictorialBar',
              itemStyle: {
                opacity: 1
              },
              label: {
                show: false,
              },
              color: "#F2F6FD",
              symbolMargin: 5, //图形的两边间隔
              symbol: "roundRect",//'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'
              // symbolClip: true, //是否裁剪图形
              symbolSize: [5, 15],
              // symbolPosition: "start", //图形的定位位置。
              symbolOffset: [-8, 6], //图形相对于原本位置的偏移。
              //stack: "total",
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              symbolRepeat: 'fixed',
              data: data1,
              z: 1
            }
          ],
        }
      //   const byrsBar = echarts.init(document.getElementById(`barIdName`))// 图标初始化
      // const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
      // byrsBar.clear();
      setTimeout(() => {
       
          const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
          byrsBar.clear();
          byrsBar.setOption(option)// 渲染页面
          // 随着屏幕大小调节图表
          window.addEventListener('resize', () => {
            byrsBar.resize()
          })
       
      }, 500);

    }
  },
  created() {

  }
}
</script>
<style lang="scss"  scoped>
.barBox {
  width: 100%;
}
</style>

<style>
.pictorialBarBox {
  padding-bottom: 0.2rem!important;
}
</style>
