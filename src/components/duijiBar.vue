<template>
  <div class="barBox">
    <div class="echart" ref="echarts" style="width: calc(100vw - 1.23rem); height: 293px"></div>
    <div v-show="toChild && toChild.length<1" :style="{position:'absolute',left: '50%', top: '50%', transform: 'translate(-50%,-50%)'}">
      <img src="@/assets/img/noData.png" :style="{width: '100%'}">
      <p :style="{fontSize: '0.3rem',color: '#6E6D6D',textAlign: 'center',marginTop: '20px'}">无相关数据</p>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: "duijiBar",
  components: {
  },
  props: {
    toChild: {

    },
    title: {

    },
    isFirst: {
      default: false
    },
    isShow: {

    },
    rightCenter: {}

  },
  data() {
    return {
      toChild1: this.toChild,
      dialogVisible: false,
      chart: null,
    }
  },
  /**
    * 深度监听 图表生成之后 传定时刷新数据进来 由于数据不能动态传值，所以子组件使用深度监听来监控数据变化
    */
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    toChild: {
      deep: true,
      handler(newVal, oldVal) {
        this.getByrsEcharts(newVal)
      }
    }

  },

  mounted() {

  },
  methods: {
    handleClick() {
      this.dialogVisible = true
    },
    // 柱状图的开始
    getByrsEcharts(data) {
      if (data) {
        var bw = 12;
        var categories1 = [];
        var categories2 = [];
        var categories3 = [];
        var categories4 = [];
        var categories5 = [];
        var categories6 = [];
        var categories7 = [];
        var name = [];
        var data1 = [];
        var data2 = [];
        var data3 = [];
        var data4 = [];
        var data5 = [];
        var data6 = [];
        var data7 = [];
        var data8 = [];
        for (var i = 0; i < data.length; i++) {
          if (name.indexOf(data[i]["pur_yr"]) == -1) {
            name.push(data[i]["pur_yr"]);
          }
        }
        for (var i = 0; i < data.length; i++) {
          if (name[0] == data[i]["pur_yr"]) {
            categories1.push(data[i]["pur_org_no"]);
          } else if (name[1] == data[i]["pur_yr"]) {
            categories2.push(data[i]["pur_org_no"]);
          } else if (name[2] == data[i]["pur_yr"]) {
            categories3.push(data[i]["pur_org_no"]);
          } else if (name[3] == data[i]["pur_yr"]) {
            categories4.push(data[i]["pur_org_no"]);
          } else if (name[4] == data[i]["pur_yr"]) {
            categories5.push(data[i]["pur_org_no"]);
          } else if (name[5] == data[i]["pur_yr"]) {
            categories6.push(data[i]["pur_org_no"]);
          } else if (name[6] == data[i]["pur_yr"]) {
            categories7.push(data[i]["pur_org_no"]);
          }
        }

        for (var i = 0; i < data.length; i++) {
          if (name[0] == data[i]["pur_yr"]) {
            data1.push(data[i]["pur_mth_amt"]);
          } else if (name[1] == data[i]["pur_yr"]) {
            data2.push(data[i]["pur_mth_amt"]);
          } else if (name[2] == data[i]["pur_yr"]) {
            data3.push(data[i]["pur_mth_amt"]);
          } else if (name[3] == data[i]["pur_yr"]) {
            data4.push(data[i]["pur_mth_amt"]);
          } else if (name[4] == data[i]["pur_yr"]) {
            data5.push(data[i]["pur_mth_amt"]);
          } else if (name[5] == data[i]["pur_yr"]) {
            data6.push(data[i]["pur_mth_amt"]);
          } else if (name[6] == data[i]["pur_yr"]) {
            data7.push(data[i]["pur_mth_amt"]);
          } else if (name[7] == data[i]["pur_yr"]) {
            data8.push(data[i]["pur_mth_amt"]);
          }
        }
      }
      if (data && data.length > 0) {
        var option = {
          grid: {
            top: '10%',
            left: '0%',
            right: '0%',
            bottom: 45,
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
            extraCssText:'z-index:2',
            axisPointer: {
              type: "none",
              label: {
                show: false,
                backgroundColor: "#6a7985",
              },
            },
            formatter: function (params) {
              let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`

              if (params) {
                if (params[1] && params[1].data == '0') {
                  var noData1 = ''
                  noData1 = `style='display:none'`
                } if (params[2] && params[2].data == '0') {
                  var noData2 = '0'
                  // noData2 = `style='display:none'`
                }
                if (params[3] && params[3].data == '0') {
                  var noData3 = '0'
                  // noData3 = `style='display:none'`
                }
                if (params[4] && params[4].data == '0') {
                  var noData4 = '0'
                  // noData4 = `style='display:none'`
                }
                if (params[5] && params[5].data == '0') {
                  var noData5 = '0'
                  // noData5 = `style='display:none'`
                }
                return `
                <div>${params[0].name}</div>
                <div>总数：${(params[0] ? (params[0].data * 1).toLocaleString() : '暂无数据')}</div>
                <div class='data5' ${noData5}>${circle}#3C7FFE"></span>正高：${(params[5] && params[5].data != '0' ? (params[5].data * 1).toLocaleString() : '0')}</div>
                <div class='data4' ${noData4}>${circle}#75A1FF"></span>副高：${(params[4] && params[4].data != '0' ? (params[4].data * 1).toLocaleString() : '0')}</div>
                <div class='data3' ${noData3}>${circle}#B9CFFF"></span>中级：${(params[3] && params[3].data != '0' ? (params[3].data * 1).toLocaleString() : '0')}</div>
                <div class='data2' ${noData2}>${circle}#D1DDEB"></span>初级：${(params[2] && params[2].data != '0' ? (params[2].data * 1).toLocaleString() : '0')}</div> 
                <div class='data1' ${noData1}>${circle}#b5b5b5"></span>其他：${(params[1] && params[1].data != '0' ? (params[1].data * 1).toLocaleString() : '0')}</div> 
              `
              }
              //   var result = params[0].name + "<br>";
              //   var zbData = "";
              //   params.forEach(function (item) {
              //     let itemData = item.data ? (item.data * 1).toLocaleString() : "-";
              //     if (item.seriesName == "其他" && itemData == 0) {
              //       result += "";
              //     } else if (item.seriesName == "中级" && itemData == 0) {
              //       result += "";
              //     } else if (item.seriesName == "初级" && itemData == 0) {
              //       result += "";
              //     } else if (item.seriesName == "正高" && itemData == 0) {
              //       result += "";
              //     } else if (item.seriesName == "副高" && itemData == 0) {
              //       result += "";
              //     } else if (item.seriesName == "占比") {
              //       result += ` (${(item.data * 1).toFixed(1)}%)` + "<br>";
              //     } else {
              //       if (item.seriesName == "总数") {
              //         let dotHtml = item.marker;
              //         result += dotHtml + " " + item.seriesName + "：" + `${itemData}`;
              //       } else {
              //         let dotHtml = item.marker;
              //         result +=
              //           dotHtml + " " + item.seriesName + "：" + `${itemData}` + "<br>";
              //       }
              //     }
              //   });
              //   return result;
            },
          },
          legend: {
            data: [name[0], name[1], name[2], name[3]],
            textStyle: {
              fontWeight: "normal",
              fontSize: 15,
              color: '#333333',
            },
            x: 'center',
            y: 'bottom',
            itemWidth: 16,
            itemHeight: 16,
            itemGap: 20,
            // textStyle: {

          },
          xAxis: {
            type: 'category',
            data: categories1,
            axisLine: {
              lineStyle: {
                color: '#cccccc',
                width: 1
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true, // 不显示坐标轴上的文字
              interval: 0, // x轴间距
              // textStyle: {
              color: '#373F6D',
              fontSize: 13.5,
              // },
              formatter: (params, index) => {
                // 数据小于5 不做换行展示
                if (this.toChild && this.toChild.length <= 5) return params;
                // x轴换行显示
                var newParamsName = "";
                var paramsNameNumber = params.length;
                var provideNumber = 4; //一行显示几个字
                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
                if (paramsNameNumber > provideNumber) {
                  for (var p = 0; p < rowNumber; p++) {
                    var tempStr = "";
                    var start = p * provideNumber;
                    var end = start + provideNumber;
                    if (p == rowNumber - 1) {
                      tempStr = params.substring(start, paramsNameNumber);
                    } else {
                      tempStr = params.substring(start, end) + "\n";
                    }
                    newParamsName += tempStr;
                  }
                } else {
                  newParamsName = params;
                }
                return newParamsName;
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              show: false,
            },
            splitLine: { //修改背景线条样式
              show: true,//是否展示 
              lineStyle: {
                type: 'dashed',
                color: "rgba(0,0,0,0.1)"
              }
            }
          },
          series: [
            {
              name: name[5],
              type: "bar",
              stack: "y",
              data: data6,
              barWidth: bw,
              barGap: "-100%",
              label: {
                show: true,
                position: "top",
                color: "#373F6D",
                fontSize: 14,
                formatter: (res) => {
                  return (res.value && res.value * 1).toLocaleString();
                },
              },
              itemStyle: {
                normal: {
                  color: "rgba(1,1,1,0)",
                  barBorderRadius: [0, 0, 0, 0],
                },
              },
            },
            {
              name: name[6],
              // type: "bar",
              stack: "total",
              data: data7,
              barWidth: bw,
              type: "line",
              symbol: 'none',
              symbolSize: 0, // symbol的大小设置为0让线的小圆点不显示
              showSymbol: false, // 不显示symbol不显示
              itemStyle: {
                normal: {
                  color: "rgba(0,0,0,0)",
                  barBorderRadius: [0, 0, 0, 0],
                },
              },
              lineStyle: {
                width: 0, // 线宽是0不显示线
                color: 'rgba(0, 0, 0, 0)' // 线的颜色是透明的
              },
              emphasis: { // 鼠标经过时：
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)'
              }
            },
            {
              name: name[4],
              type: "bar",
              stack: "total",
              data: data5,
              barWidth: bw,
              itemStyle: {
                normal: {
                  color: "#b5b5b5",
                  barBorderRadius: [3, 3, 0, 0],
                },
              },
            },
            {
              name: name[3],
              type: "bar",
              stack: "total",
              data: data4,
              barWidth: bw,
              itemStyle: {
                normal: {
                  color: "#D1DDEB",
                  barBorderRadius: [0, 0, 0, 0],
                },
              },
            },
            {
              name: name[2],
              type: "bar",
              stack: "total",
              data: data3,
              barWidth: bw,
              itemStyle: {
                normal: {
                  color: "#B9CFFF",
                  barBorderRadius: [0, 0, 0, 0],
                },
              },
            },
            {
              name: name[1],
              type: "bar",
              stack: "total",
              data: data2,
              barWidth: bw,
              itemStyle: {
                normal: {
                  color: "#75A1FF",
                  barBorderRadius: [0, 0, 0, 0],
                },
              },
            },
            {
              name: name[0],
              type: "bar",
              stack: "total",
              data: data1,
              barWidth: bw,
              itemStyle: {
                normal: {
                  color: "#3C7FFE",
                  barBorderRadius: [4, 4, 0, 0],
                },
              },
            },


          ],
        }
      }

      //   const byrsBar = echarts.init(document.getElementById(`barIdName`))// 图标初始化
      const byrsBar = echarts.init(this.$refs.echarts)// 图标初始化
      byrsBar.clear();
      setTimeout(() => {
        if (option) {
          byrsBar.setOption(option)// 渲染页面
          // 随着屏幕大小调节图表
          window.addEventListener('resize', () => {
            byrsBar.resize()
          })
        }
      }, 500);

    }
  },
  created() {

  }
}
</script>
<style lang="scss" scoped>
</style>