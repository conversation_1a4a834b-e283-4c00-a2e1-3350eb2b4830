<template>
    <van-overlay :show="showOverlay" @click="showOverlay = false" lock-scroll>
        <div class="wrapper" @click.stop>
            <div class="block">
                <van-loading color="#0094ff" type="spinner" size="24" vertical>{{text}}</van-loading>
            </div>
        </div>
    </van-overlay>
</template>
<script>
export default {
    name: 'Loading',
    data() {
        return {
            showOverlay: false,
            text: '正在加载中，请稍候...'
        }
    }
}
</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(188, 189, 191, .2)
}

.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
</style>