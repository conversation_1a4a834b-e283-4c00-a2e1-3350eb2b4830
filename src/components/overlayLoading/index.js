/*
创建一个全局的遮罩层用来数据加载完成前的等待
使用方法：this.$loading.show(text="正在加载中，请稍候...")
this.$loading.hide()
@author: wukk
*/
import Vue from 'vue'
//引入模版
import LoadingComponent from '@/components/overlayLoading/index.vue'
const Loading = {
    install(Vue) {
      const Constructor = Vue.extend(LoadingComponent)
      const instance = new Constructor().$mount()
      
      document.body.appendChild(instance.$el)
      
      Vue.prototype.$loading = {
        show(text = '正在加载中，请稍候...') {
          instance.text = text
          instance.showOverlay = true
        },
        hide() {
          instance.showOverlay = false
        }
        

      }
    }
  }
  
export default Loading