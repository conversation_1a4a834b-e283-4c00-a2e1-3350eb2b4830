import { getUserId as _getUserId, getUserInfo as _getUserInfo } from "@/http/api";
import env from "@/http/env";
import { setState, getState } from '@/utils/cache'
import { getUserldCookie, setUserldCookie } from '@/utils/auth'

export const isEmpty = (num) => {
  return num === undefined || num == null
}

export const toFixed = (number, length = 1) => {
  if (isNaN(number)) return number
  const sign = number > 0 ? 1 : -1

  const decimal = 10 ** length
  const v = Math.round(Math.abs(number) * decimal) * sign
  return (v / decimal).toFixed(length)
}

export const formatNum = (num, length, type) => {
  if (isNaN(num)) return num
  const x = Math.abs(num) >= 1 ? 1 : (
    Math.abs(num) < 0.01 ? 3 : 2
  )

  const value = num ? toFixed(Number(num),
    length !== undefined ? length : x
  ) : num

  if (type === 'ratio' && num > 0) {
    return '+' + value
  }
  return (num < 0 && !String(value).includes('-')) ? '-' + value : value
}

export const formatNumForMonitor = (num, length, type) => {
  if (isNaN(num)) return num
  let len = length
  let flag = Math.abs(num) > 500

  if (type == 'percent') {
    num = flag ? (num > 0 ? 500 : -500) : num
    if (flag) {
      len = 0
    }
  }

  if (length == undefined) {
    switch (true) {
      case Math.abs(num) >= 0.1: {
        len = 1
        break
      }
      case Math.abs(num) < 0.1 && Math.abs(num) >= 0.01: {
        len = 2
        break
      }
      case Math.abs(num) < 0.01: {
        len = 3
        break
      }
    }
  }
  
  let value = num !== undefined ? toFixed(Number(num), len) : num

  if (type === 'ratio' && value > 0) {
    return '+' + value
  }

  if (type == 'percent') {
    value += '%'
    if (flag) return value + '+'
  }
  return value
}

export const numChangeByUnit = (num) => {
  if (isNaN(num)) return num
  return num / (10 ** 8)
}

export const getUserInfo = () => {
  const userInfoCache = getState("cec_hr_lx_userInfo")
  return new Promise((resolve, reject) => {
    if (userInfoCache) {
      resolve(userInfoCache)
      return
    }
    try {
      // 从cookie获取用户ID
      const userIdFromCookie = getUserldCookie()

      setState("cec_hr_lx_userInfo", {
        mobile: userIdFromCookie
      })
      resolve(userIdFromCookie)

      // lx.biz.getAuthCode({
      //   appId: env.appId,
      //   success: function (res) {
      //     _getUserInfo(res.authCode).then((res) => {
      //       const cache = JSON.stringify(res.data)
      //       res.data && setState("cec_hr_lx_userInfo", cache)
      //       resolve(cache)
      //     })
      //   },
      //   fail: (err) => {
      //     reject(err)
      //   }
      // })
    }catch (e) {
      reject(e)
      console.error(e)
    }
  })
}

export function getQueryParams() {
  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const result = {};

  params.forEach((value, key) => {
      // 处理重复参数，转为数组
      if (result[key]) {
          if (!Array.isArray(result[key])) {
              result[key] = [result[key]];
          }
          result[key].push(value);
      } else {
          result[key] = value;
      }
  });

  return result;
}

export const getUserId = () => {
  // 从cookie获取用户ID
  const userIdFromCookie = getUserldCookie()
  const query = getQueryParams()

  return new Promise((resolve) => {
    // 优先使用cookie中的用户ID
    if (userIdFromCookie) {
      console.log('从cookie获取到用户ID:', userIdFromCookie)
      resolve(userIdFromCookie)
      return
    }

    // 开发环境调试代码
    if (process.env.NODE_ENV == 'development') {
      const testId = '18600894210'
      console.log('开发环境使用测试用户ID:', testId)
      setUserldCookie(testId)
      resolve(testId)
    } else {
      // 若存在query参数，则直接使用query参数
      const queryUserId = query.userId;
      if (queryUserId) {
        console.log('从URL参数获取到用户ID:', queryUserId)
        setUserldCookie(queryUserId)
        resolve(queryUserId)
      } else {
        console.log('未找到用户ID，返回null')
        resolve(null)
      }
    }
  })
}

export function debounce(callback, delay = 500) {
  let timer = null

  return function(...rest) {
    timer && clearTimeout(timer)
    timer = setTimeout(() => {
        callback.apply(this, ...rest)
    }, delay)
  }
}