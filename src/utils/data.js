export function getEmployeePostHomeColors(data) { //
    let colors = []
    let cdata = [
      {name: "管理类", color: "#FFDE95"},
      {name: "科技类", color: "#A1A8FF"},
      {name: "技能类", color: "#61BFF5"},
      {name: "党群类", color: "#E95D56"},
      {name: "市场类", color: "#ACFAD8"},
      {name: "职能类", color: "#4E80F7"},
      {name: "其他", color: "#D4D4D4"}
    ]
    for(let i = 0; i < data.length; i++) {
      for(let j = 0; j < cdata.length; j++) {
        if(cdata[j]['name'] == data[i]['name']) {
            colors.push(cdata[j]['color'])
        }
      }
    }
    return colors
}


export function getEmployeeDegreeHomeColors(data) { //学历
    let colors = []
    let cdata = [
      {name: "博士研究生", color: "#5673EF"},
      {name: "硕士研究生", color: "#37C5CB"},
      {name: "大学本科", color: "#5398F7"},
      {name: "本科以下", color: "#6DC6FA"},
    ]
    for(let i = 0; i < data.length; i++) {
      for(let j = 0; j < cdata.length; j++) {
        if(cdata[j]['name'] == data[i]['name']) {
            colors.push(cdata[j]['color'])
        }
      }
    }
    return colors
}

export function getEmployeeGrade_SkillColors(data) { //技能
    let colors = []
    let cdata = [
      {name: "首席技师", color: "#FFD452"},
      {name: "特级技师", color: "#C0193D"},
      {name: "高级技师", color: "#9996FF"},
      {name: "技师", color: "#1F88FF"},
      {name: "高级工", color: "#74D8F4"},
      {name: "中级工", color: "#10CBD9"},
      {name: "初级工", color: "#B4CCFD"},
      {name: "学徒工", color: "#D4D4D4"}
    ]
    for(let i = 0; i < data.length; i++) {
      for(let j = 0; j < cdata.length; j++) {
        if(cdata[j]['name'] == data[i]['name']) {
            colors.push(cdata[j]['color'])
        }
      }
    }
    return colors
}

