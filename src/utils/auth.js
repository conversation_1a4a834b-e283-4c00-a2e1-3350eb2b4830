import { getQueryParams } from './index'

// Cookie管理
export const setUserldCookie = (userld, expires = 8) => {
  const date = new Date()
  date.setTime(date.getTime() + (expires * 60 * 60 * 1000)) // 8小时
  const expiresStr = `expires=${date.toUTCString()}`
  const domain = process.env.NODE_ENV === 'production' ? '; domain=.cec.com.cn' : ''
  document.cookie = `cec_hr_lx_userld=${userld}; ${expiresStr}; path=/; SameSite=Lax${domain}`
}

export const getUserldCookie = () => {
  const cookies = document.cookie.split(';')
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'cec_hr_lx_userld') {
      return value
    }
  }
  return null
}

export const clearUserldCookie = () => {
  const domain = process.env.NODE_ENV === 'production' ? '; domain=.cec.com.cn' : ''
  document.cookie = `cec_hr_lx_userld=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax${domain}`
}

// 认证状态检查
export const checkAuthStatus = () => {
  const userld = getUserldCookie()
  const query = getQueryParams()
  return {
    hasUserld: !!userld,
    hasTicket: !!query.ticket,
    userld: userld,
    ticket: query.ticket
  }
}



 