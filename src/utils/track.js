import { viewReport } from '@/http/api'
import { getUserInfo } from '@/utils'

let currentPath = null
let currentTitle = null
let startTime = null

const computePath = (routePath, query = {}) => {
  let queryStr = ''
  for (const key in query) {
    queryStr += `${key}=${query[key]}&`
  }
  let path = `${routePath}?${queryStr}`

  if (path[path.length - 1] == '&' || path[path.length - 1] == '?') {
    path = path.slice(0, -1)
  }

  return path
}

const getTitle = (path, title) => {
  switch(true) {
    case path.includes('zbBoard') && path.includes('active=0'): {
      return title + '-集团整体'
    }
    case path.includes('zbBoard') && path.includes('active=1'): {
      return title + '-指标排名'
    }
    case path.includes('zbBoard') && path.includes('active=2'): {
      return title + '-二级企业'
    }
    case path.includes('monitor') && path.includes('active=profits'): {
      return title + '-利润大户'
    }
    case path.includes('monitor') && path.includes('active=loss'): {
      return title + '-亏损企业'
    }
    case path.includes('monitor') && path.includes('active=all'): {
      return title + '-全部企业'
    }
    default: return title
  }
}

export const reportViewDuration = (to, from, params) => {
  return new Promise((resolve) => {
    // setTimeout(() => {
      try {
        const path = computePath(to.path, to.query)
        if (currentPath && (path !== currentPath)) {
          const endTime = Date.now()
          const duration = endTime - startTime
          const visitRouter = currentPath
          const visitInfo = currentTitle
          getUserInfo().then((userInfoCache) => {
            viewReport({
              ...JSON.parse(userInfoCache),
              visitRouter,
              visitInfo,
              dwellTime: duration / 1000
            }).then(() => {
              resolve(true)
            }).catch(() => {
              resolve(true)
            })
          }).catch(() => {
            resolve(true)
          })
        }
    
        startTime = Date.now()
        currentPath = path
        currentTitle = getTitle(path, document.title)
      }catch (e) {
        console.error(e)
        resolve(true)
      }
    // }, 500)
  })
}