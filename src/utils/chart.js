export function findLargest(arr){
    if(!arr.length){
      return 0;
    };
    let res = '';
    res = arr.reduce((acc, val) => {
      if(acc || val) {
        return acc.length >= val.length ? acc : val;
      }
    });
    return  res ? res.length : 0;
}

export function toPoint(percent){
  if(!percent) return 0
  var str=percent.replace("%","");
  str= str/100;
  return str;
}

export function findMaxCommaNo(arr){
  if(!arr.length){
    return 0;
  };
  let res = 0;
  for(let i = 0; i < arr.length; i++) {
    if(arr[i]) {
      let result = getCharCount(`${Number(arr[i]).toLocaleString()}`,',')
      if(result > res) res = result;
    }
  }
  return  res;
}

export const getCharCount = (str, char) => {
  var regex = new RegExp(char, 'g');
  var result = str.match(regex);
  var count = !result ? 0 : result.length;
  return count;
}

export function getStrCode(str) {
  var count = 0; 
  if (str) {
    let len = str.length; 
    for (var i = 0; i < len; i++) {
      if (str.charCodeAt(i) > 255) {
        count += 2;
      } else {
        count++;
      }
    }
    return count;
  } else {
    return 0;
  }
}

export function formatAmount(num,decimal = 1) {
  if(!num || num == '' || num == '-' || num == 'undefined' || num == 'null') return "-";
  if(num < 1000 && num >= 0){
    return num
  }
  return Number(num).toLocaleString('zh-CN', { style: 'currency', currency: 'CNY', minimumFractionDigits: decimal }).replaceAll('¥','')
}