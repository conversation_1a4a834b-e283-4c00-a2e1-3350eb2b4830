import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);
import createPersistedState from "vuex-persistedstate";

export const store = new Vuex.Store({
  state: {
    data: {}, //人才概况页面data
    ygXueLiObj: {},
    eduData: {}, //教育信息页面data
    workData: {}, //工作情况页面data
    manageData: {}, //管理信息页面Data
    technologyData: {}, //科技信息页面Data
    skillData: {}, //技能信息页面data
    companyClick: null,
    companyClickSecond: null,
    companyClickTG: null,
    moduleInfo: null, // 模块权限信息
  },
  mutations: {
    getData(state, val) {
      state.data = val;
    },
    getYgXueLiData(state, val) {
      state.ygXueLiObj = val;
    },
    getEduData(state, val) {
      state.eduData = val;
    },
    getWorkData(state, val) {
      state.workData = val;
    },
    getManageData(state, val) {
      state.manageData = val;
    },
    getTechnologyData(state, val) {
      state.technologyData = val;
    },
    getSkillData(state, val) {
      state.skillData = val;
    },
    getCompanyClick(state,val){
      state.companyClick=val
    },
    getCompanyClickSecond(state,val){
      state.companyClickSecond=val
    },
    getCompanyClickTG(state,val){
      state.companyClickTG=val
    },
    setModuleInfo(state, val) {
      state.moduleInfo = val;
      // 同时存储到 localStorage
      localStorage.setItem('moduleInfo', JSON.stringify(val));
    }
  },
  actions: {
    commitChangeData: ({ commit }, val) => commit("getData", val),
    commitChangeXueliData: ({ commit }, val) => commit("getYgXueLiData", val),
    commitChangeEduData: ({ commit }, val) => commit("getEduData", val),
    commitChangeWorkData: ({ commit }, val) => commit("getWorkData", val),
    commitChangeManageData: ({ commit }, val) => commit("getManageData", val),
    commitChangeTechnologyData: ({ commit }, val) => commit("getTechnologyData", val),
    commitChangeSkillData: ({ commit }, val) => commit("getSkillData", val),
    commitCompanyClick: ({ commit }, val) => commit("getCompanyClick", val),
    commitCompanyClickSecond: ({ commit }, val) => commit("getCompanyClickSecond", val),
    commitCompanyClickTG: ({ commit }, val) => commit("getCompanyClickTG", val),
    updateModuleInfo: ({ commit }, val) => commit("setModuleInfo", val),
  },
  plugins: [createPersistedState()]
});

export default store;
