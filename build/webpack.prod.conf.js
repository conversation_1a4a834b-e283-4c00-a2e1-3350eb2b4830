'use strict'
const path = require('path')
const utils = require('./utils')
const webpack = require('webpack')
const config = require('../config')
const merge = require('webpack-merge')
const baseWebpackConfig = require('./webpack.base.conf')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')

const env = require('../config/prod.env')

const webpackConfig = merge(baseWebpackConfig, {
  mode: 'production',
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.build.productionSourceMap,
      extract: true,
      usePostCSS: true
    })
  },
  devtool: config.build.productionSourceMap ? config.build.devtool : false,
  output: {
    path: config.build.assetsRoot,
    filename: utils.assetsPath('js/[name].[chunkhash].js'),
    chunkFilename: utils.assetsPath('js/[id].[chunkhash].js')
  },
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            warnings: false,
            drop_console: true, // 移除console
            drop_debugger: true, // 移除debugger
            pure_funcs: ['console.log', 'console.info', 'console.warn', 'console.error'], // 移除所有console
            dead_code: true, // 移除死代码
            drop_debugger: true, // 移除debugger
            global_defs: {
              '@alert': 'console.log'
            },
            passes: 2, // 多次压缩
            unsafe: true, // 启用不安全优化
            unsafe_comps: true, // 不安全比较优化
            unsafe_Function: true, // 不安全函数优化
            unsafe_math: true, // 不安全数学优化
            unsafe_proto: true, // 不安全原型优化
            unsafe_regexp: true, // 不安全正则优化
            unsafe_undefined: true // 不安全undefined优化
          },
          mangle: {
            safari10: true,
            toplevel: true, // 混淆顶级作用域中的变量名
            eval: true, // 混淆eval中的代码
            keep_fnames: false, // 不保留函数名
            reserved: ['$', 'jQuery'] // 保留的变量名
          },
          output: {
            comments: false, // 移除注释
            beautify: false, // 不美化输出
            semicolons: true, // 保留分号
            ascii_only: true // 只使用ASCII字符
          }
        },
        sourceMap: config.build.productionSourceMap,
        parallel: true,
        extractComments: false
      })
    ],
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 200000, // 降低最大文件大小
      cacheGroups: {
        // 第三方库
        vendor: {
          name: 'vendor',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // echarts单独打包
        echarts: {
          name: 'echarts',
          test: /[\\/]node_modules[\\/]echarts[\\/]/,
          priority: 20,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // vant UI库单独打包
        vant: {
          name: 'vant',
          test: /[\\/]node_modules[\\/]vant[\\/]/,
          priority: 20,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // vue相关库
        vue: {
          name: 'vue',
          test: /[\\/]node_modules[\\/](vue|vue-router|vuex)[\\/]/,
          priority: 20,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // axios单独打包
        axios: {
          name: 'axios',
          test: /[\\/]node_modules[\\/]axios[\\/]/,
          priority: 20,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // dayjs单独打包
        dayjs: {
          name: 'dayjs',
          test: /[\\/]node_modules[\\/]dayjs[\\/]/,
          priority: 20,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // 公共模块
        common: {
          name: 'common',
          minChunks: 2,
          priority: 5,
          chunks: 'all',
          reuseExistingChunk: true
        },
        // 异步加载的公共模块
        async: {
          name: 'async',
          minChunks: 2,
          priority: 1,
          chunks: 'async',
          reuseExistingChunk: true
        },
        // 默认配置
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true
        }
      }
    },
    runtimeChunk: {
      name: 'runtime'
    }
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env': env
    }),
    new MiniCssExtractPlugin({
      filename: utils.assetsPath('css/[name].[contenthash].css'),
      chunkFilename: utils.assetsPath('css/[id].[contenthash].css')
    }),
    new OptimizeCSSPlugin({
      cssProcessorOptions: config.build.productionSourceMap
        ? { 
            safe: true, 
            map: { inline: false },
            discardComments: { removeAll: true }, // 移除所有注释
            discardEmpty: true, // 移除空规则
            discardDuplicates: true, // 移除重复规则
            mergeLonghand: true, // 合并简写属性
            mergeRules: true, // 合并规则
            minifyFontValues: true, // 压缩字体值
            minifyGradients: true, // 压缩渐变
            minifyParams: true, // 压缩参数
            minifySelectors: true, // 压缩选择器
            normalizeCharset: true, // 标准化字符集
            normalizeUrl: true, // 标准化URL
            orderedValues: true, // 排序值
            reduceIdents: true, // 减少标识符
            reduceInitial: true, // 减少初始值
            reduceTransforms: true, // 减少变换
            uniqueSelectors: true, // 唯一选择器
            zindex: false // 不处理z-index
          }
        : { 
            safe: true,
            discardComments: { removeAll: true },
            discardEmpty: true,
            discardDuplicates: true,
            mergeLonghand: true,
            mergeRules: true,
            minifyFontValues: true,
            minifyGradients: true,
            minifyParams: true,
            minifySelectors: true,
            normalizeCharset: true,
            normalizeUrl: true,
            orderedValues: true,
            reduceIdents: true,
            reduceInitial: true,
            reduceTransforms: true,
            uniqueSelectors: true,
            zindex: false
          }
    }),
    new HtmlWebpackPlugin({
      filename: config.build.index,
      template: 'index.html',
      inject: true,
      minify: {
        removeComments: true, // 移除注释
        collapseWhitespace: true, // 移除空白
        removeAttributeQuotes: true, // 移除属性引号
        removeEmptyAttributes: true, // 移除空属性
        removeOptionalTags: true, // 移除可选标签
        removeRedundantAttributes: true, // 移除冗余属性
        removeScriptTypeAttributes: true, // 移除script的type属性
        removeStyleLinkTypeAttributes: true, // 移除style和link的type属性
        useShortDoctype: true, // 使用短doctype
        minifyCSS: true, // 压缩内联CSS
        minifyJS: true, // 压缩内联JS
        caseSensitive: false, // 不区分大小写
        keepClosingSlash: false, // 不保留闭合斜杠
        sortAttributes: true, // 排序属性
        sortClassName: true // 排序类名
      }
    }),
    new webpack.HashedModuleIdsPlugin(),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../static'),
          to: config.build.assetsSubDirectory,
          globOptions: {
            ignore: ['.*']
          }
        }
      ]
    })
  ]
})

if (config.build.productionGzip) {
  const CompressionWebpackPlugin = require('compression-webpack-plugin')

  webpackConfig.plugins.push(
    new CompressionWebpackPlugin({
      filename: '[path].gz[query]',
      algorithm: 'gzip',
      test: new RegExp(
        '\\.(' +
        config.build.productionGzipExtensions.join('|') +
        ')$'
      ),
      threshold: 8192, // 降低阈值，压缩更多文件
      minRatio: 0.7, // 降低压缩比要求，提高压缩率
      deleteOriginalAssets: false, // 保留原文件
      compressionOptions: {
        level: 9 // 最高压缩级别
      }
    })
  )
}

if (config.build.bundleAnalyzerReport) {
  const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
  webpackConfig.plugins.push(new BundleAnalyzerPlugin())
}

module.exports = webpackConfig
