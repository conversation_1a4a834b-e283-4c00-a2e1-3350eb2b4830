# 项目结构说明

```
hr_mobile/
├── .babelrc                    # Babel 配置文件
├── .editorconfig               # 编辑器配置文件
├── .eslintignore               # ESLint 忽略配置
├── .eslintrc.js                # ESLint 配置文件
├── .gitignore                  # Git 忽略配置
├── .nvmrc                      # Node.js 版本配置
├── .postcssrc.js               # PostCSS 配置文件
├── index.html                  # 主页 HTML 模板
├── OPTIMIZATION.md             # 优化说明文档
├── package-lock.json           # npm 锁定依赖版本文件
├── package.json                # 项目配置和依赖声明
├── README.md                   # 项目说明文档
├── .augment/                   # 增强规则目录
│   └── rules/
├── .cursor/                    # Cursor 规则目录
│   └── rules/
├── .git/                       # Git 版本控制目录
├── build/                      # 构建相关配置
│   ├── build.js
│   ├── buildTest.js
│   ├── check-versions.js
│   ├── logo.png
│   ├── utils.js
│   ├── vue-loader.conf.js
│   ├── webpack.base.conf.js
│   ├── webpack.dev.conf.js
│   ├── webpack.prod.conf.js
│   └── webpack.test.conf.js
├── config/                     # 环境配置文件
│   ├── dev.env.js              # 开发环境配置
│   ├── index.js                # 配置入口文件
│   ├── prod.env.js             # 生产环境配置
│   └── test.env.js             # 测试环境配置
├── hr_mobile/
│   └── static/                 # 静态资源目录
├── node_modules/               # npm 依赖包目录
├── scripts/                    # 自定义脚本目录
├── src/                        # 源代码目录
│   ├── App.vue                 # Vue 根组件
│   ├── constants.js            # 常量定义文件
│   ├── header.vue              # 头部组件
│   ├── main.js                 # Vue 入口文件
│   ├── sw.js                   # Service Worker 文件
│   ├── assets/                 # 静态资源目录
│   │   ├── logo.png
│   │   ├── img/                # 图片资源目录
│   │   └── styles/             # 样式文件目录
│   ├── components/             # 公共组件目录
│   │   ├── layout/             # 布局相关组件
│   │   │   └── bottomNav.vue   # 底部导航组件
│   │   ├── overlayLoading/     # 加载遮罩组件
│   │   │   ├── index.js
│   │   │   └── index.vue
│   │   └── ProgressBar/        # 进度条组件
│   │       └── index.vue
│   ├── http/                   # HTTP 请求相关
│   │   ├── api.js              # API 接口定义
│   │   ├── env.js              # 环境变量配置
│   │   └── request.js          # 请求封装
│   ├── plugins/                # 插件目录
│   │   └── vant.js             # Vant UI 插件配置
│   ├── router/                 # 路由配置
│   │   └── index.js            # 路由入口文件
│   ├── store/                  # Vuex 状态管理
│   │   └── index.js            # 状态管理入口文件
│   ├── utils/                  # 工具函数目录
│   │   ├── auth.js             # 认证工具
│   │   ├── cache.js            # 缓存工具
│   │   ├── chart.js            # 图表工具
│   │   ├── data.js             # 数据处理工具
│   │   ├── document.js         # 文档工具
│   │   ├── index.js            # 工具入口文件
│   │   └── track.js            # 埋点工具
│   └── views/                  # 页面视图目录
│       ├── assetsMap/          # 资产地图页面
│       │   └── index.vue
│       ├── auth/               # 认证相关页面
│       │   └── loginIAM.vue    # IAM 登录页面
│       ├── cecBoard/           # CEC 面板页面
│       │   └── index.vue
│       ├── common/             # 公共页面
│       │   └── noPermission.vue# 无权限页面
│       ├── economyReport/      # 经济报告页面
│       │   ├── index.vue
│       │   └── components/     # 经济报告子组件
│       ├── home/               # 首页
│       │   ├── index copy.vue
│       │   ├── index_fullscreen.vue
│       │   ├── index_old.vue
│       │   └── index.vue
│       ├── hrPicture/          # HR 图表页面
│       │   ├── eduMsg.vue      # 教育信息页面
│       │   ├── hrIndexTwo.vue  # HR 双首页
│       │   └── index.vue       # HR 首页
│       ├── jjyxReport/         # 经济运行报告页面
│       ├── liangjin/           # 两金页面
│       ├── monitor/            # 监控页面
│       ├── siku/               # 四库页面
│       ├── tgMonitor/          # 特钢监控页面
│       ├── upload/             # 上传页面
│       ├── viewReport/         # 查看报告页面
│       ├── zbBoard/            # 指标看板页面
│       └── zbBoardSecond/      # 二级指标看板页面
└── static/                     # 静态资源目录
    ├── app-jssdk.umd.min.js    # JSSDK 文件
    ├── flexible.js             # 移动端适配脚本
    ├── reset.css               # CSS 重置样式
    ├── vconsole.min.js         # 移动端调试工具
    ├── file/                   # PDF 文件目录
    └── pdfh5/                  # PDF H5 查看器
        ├── css/
        └── js/
```