html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img,
figure,
iframe {
  margin: 0;
  padding: 0;
  font-weight: normal;
}

fieldset,
img {
  border: none;
}

address,
caption,
cite,
code,
dfn,
th,
var {
  font-style: normal;
  font-weight: normal;
}

ul,
ol,
li {
  list-style-type: none;
}

html {
  height: 100%;
}

body {
  color: #222;
  height: 100%;
  font-family: "微软雅黑", "PingFang SC", "Helvetica Neue", "Helvetica",
    "STHeitiSC-Light", "Arial", sans-serif;

  background: #fff;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
}

a:hover,
a:active,
a:focus {
  text-decoration: none;
}

input {
  -webkit-appearance: none;
  background: none;
  caret-color: #da0428;
}

input[disabled] {
  background: none;
}

input,
textarea,
button,
select,
label {
  border: none;
  resize: none;
  outline: none;
  font-family: Arial;
}

select {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}

i,
em {
  font-style: normal;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  padding: 0;
  margin: 0;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-overflow-scrolling: touch;
}

input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #cccccc;
  font-weight: 400;
}

input::-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #cccccc;
  font-weight: 400;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #cccccc;
  font-weight: 400;
}

input::-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #cccccc;
  font-weight: 400;
}
textarea {
  -webkit-appearance: none;
}
input[type="text"] {
  -webkit-appearance: none;
}

@font-face {
  font-family: "iconfont"; /* project id 1786143 */
  src: url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.eot");
  src: url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.eot?#iefix")
      format("embedded-opentype"),
    url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.woff2") format("woff2"),
    url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.woff") format("woff"),
    url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.ttf") format("truetype"),
    url("//at.alicdn.com/t/font_1786143_y3q0e80sgeq.svg#iconfont") format("svg");
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

.icon-del:before {
  content: "\e60c";
}

.icon-add:before {
  content: "\e60b";
}
.containerBox {
  width: 6.9rem;
  background: #ffffff;
  box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  border-radius: 0.08rem;
  padding: 0.4rem auto 0.5rem;
}
.discribe {
  padding: 0.4rem 0.3rem;
}
.discribe .detail {
  /* width: 6.3rem; */
  background: #f4f4f4;
  color: #222222;
  font-size: 0.32rem;
  padding: 0.3rem;
  /* margin: 0 auto; */
  border-radius: 0.1rem;
  box-sizing: border-box;
}
/* 边距 */
.mt40 {
  margin-top: 0.4rem !important;
}
.mt80 {
  margin-top: 0.8rem !important;
}
.mt100 {
  margin-top: 1rem !important;
}
.mb30 {
  margin-bottom: 0.3rem !important;
}
.mt10 {
  margin-top: 0.1rem !important;
}
.mb80 {
  margin-bottom: 3rem !important;
}

/* 加粗 */
.weight600 {
  font-weight: 600 !important;
}
/* 主体内容 */
.publicBox {
  /* padding-bottom: calc(1.3rem + 40px); */
  padding-bottom: 1.8rem;
  position: relative;
  box-sizing: border-box;
  overflow-x: hidden;
}
/* 筛选按钮样式 */
.filterBtn {
  width: 1.19rem;
  height: 1.19rem;
  background: #ffffff;
  box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  position: fixed;
  right: 0;
  top: 75vh;
  border-radius: 1.2rem;
  z-index: 199;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #456bc4;
  font-size: 0.46rem;
  font-weight: 600;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/* 按钮 */
.btnBox .reset,
.btnBox .look {
  width: 2.1rem;
  height: 0.72rem;
  display: inline-block;
  font-size: 0.32rem;
  text-align: center;
  line-height: 0.72rem;
  border-radius: 0.06rem;
}
/* 清空重置 */
.btnBox {
  text-align: center;
  /* position: fixed;
  z-index: 9999999;
  bottom: 10%; */
  background: #fff;
  width: 100%;
  padding: 0.3rem;
  border-radius: 0 0 16px 16px;
}
.btnBox .reset {
  background: #ffffff;
  box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  color: #222222;
}
.btnBox .look {
  background: #3e7bfa;
  box-shadow: 0.02rem 0.03rem.1rem 0px rgba(83, 95, 115, 0.29);
  color: #fff;
  margin-left: 0.3rem;
}
/* //分析内容描述 */
.titleContainer {
  background-color: rgba(0, 0, 0, 0);
  box-shadow: -0.02rem 0.13rem 0.2rem 0.01rem rgba(160, 160, 161, 0.1);
  border-radius: 0.08rem;
}
.doduleBox {
  position: relative;
  padding: 0rem 0.3rem 0.3rem;
  background: #fff;
  /* border-radius: 0.08rem; */
  border-bottom-left-radius: 0.08rem;
  border-bottom-right-radius: 0.08rem;
  box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  /* border-radius: 0.08rem;
  padding: 0.4rem 0.3rem;
  background: #fff; */
}
.doduleBox .mt30 {
  background: none;
  padding: 0;
  padding-top: 0.3rem;
  margin-bottom: 0;
}
.doduleBox .lookMore {
  position: absolute;
  right: 0;
  bottom: -1.5rem;
  width: 0.6rem;
  height: 2.4rem;
  background: #d5e1f7;
  box-shadow: 5px 6px 20px 0px rgba(74, 103, 157, 0.1);
  border-radius: 0.1rem;
  color: #456bc4;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
  top: 50%;
}
.doduleBox .lookMore span {
  width: 0.24rem;
  display: block;
  line-height: 0.38rem;
}
.discribBox {
  background: #f4f4f4;
  padding: 0.3rem;
  color: #222222;
  font-size: 0.32rem;
  margin-bottom: 0.15rem;
  border-radius: 0.1rem;
  text-align: justify;
}
/* loading */
.loading-bg {
  margin: 50vh auto 0;
}
/* 有无职称 */
.haveNoPosition {
  display: flex;
  justify-content: center;
  align-items: center;
}
.haveNoPosition .box {
  margin-top: 0.2rem;
  box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
  border-radius: 0.1rem;
  flex: 1;
  text-align: center;
  padding: 0.3rem 0;
}
.haveNoPosition .box h3 {
  padding-bottom: 0.2rem;
  font-size: 0.32rem;
  font-weight: 600;
}
.haveNoPosition .box p {
  font-size: 0.32rem;
  font-weight: 400;
}
.haveNoPosition .box:first-child {
  margin-right: 0.2rem;
}
.topsTip {
  background-color: #fff;
  padding: 0.25rem 0;
  text-align: center;
}
.topsTip div {
  margin: 0 auto;
  width: 5.3rem;
  height: 0.6rem;
  background: #f1f6ff;
  box-shadow: 0.05rem 0.06rem 0.2rem 0px rgba(74, 103, 157, 0.1);
  border-radius: 0.06rem;
  border: 1px solid #3e7bfa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.28rem;
  color: #3E7BFA;
}
.topsTip div img{
  margin-right: 0.1rem;
}
