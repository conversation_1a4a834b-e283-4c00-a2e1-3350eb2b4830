!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).lx=n()}(this,(function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,n,t){return e(t={path:n,exports:{},require:function(e,n){return function(){throw Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}()}},t.exports),t.exports}var i=t((function(e){e.exports=function(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e},e.exports.__esModule=!0,e.exports.default=e.exports})),r=n(i),o=t((function(e){function n(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n&&(i=i.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,i)}return t}e.exports=function(e){for(var t=1;arguments.length>t;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(n){i(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports})),a=n(o),s=t((function(e){e.exports=function(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=Array(n);n>t;t++)i[t]=e[t];return i},e.exports.__esModule=!0,e.exports.default=e.exports})),d=t((function(e){e.exports=function(e){if(Array.isArray(e))return s(e)},e.exports.__esModule=!0,e.exports.default=e.exports})),p=t((function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports})),c=t((function(e){e.exports=function(e,n){if(e){if("string"==typeof e)return s(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,n):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports})),l=t((function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports})),u=n(t((function(e){e.exports=function(e){return d(e)||p(e)||c(e)||l()},e.exports.__esModule=!0,e.exports.default=e.exports}))),m="object"==typeof e&&e&&e.Object===Object&&e,y="object"==typeof self&&self&&self.Object===Object&&self,v=m||y||Function("return this")(),g=v.Symbol,f=Object.prototype,x=f.hasOwnProperty,w=f.toString,h=g?g.toStringTag:void 0;var b=function(e){var n=x.call(e,h),t=e[h];try{e[h]=void 0;var i=!0}catch(e){}var r=w.call(e);return i&&(n?e[h]=t:delete e[h]),r},S=Object.prototype.toString;var E=function(e){return S.call(e)},q=g?g.toStringTag:void 0;var C=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":q&&q in Object(e)?b(e):E(e)};var I=function(e,n){return function(t){return e(n(t))}},O=I(Object.getPrototypeOf,Object);var _=function(e){return null!=e&&"object"==typeof e},A=Function.prototype.toString,N=Object.prototype.hasOwnProperty,z=A.call(Object);var T=function(e){if(!_(e)||"[object Object]"!=C(e))return!1;var n=O(e);if(null===n)return!0;var t=N.call(n,"constructor")&&n.constructor;return"function"==typeof t&&t instanceof t&&A.call(t)==z},j="UNREADY",k="READY",B="CALLBACK",L="PROMISE",K={config:1e4,device:2e4,media:3e4,ui:4e4,biz:5e4,pay:6e4,utils:9e4,internal:1e5,storage:11e4,open:12e4},D="Android",R="iOS",P="Mac",M="Windows",V="Linux",F="Unknown",U=navigator.userAgent.toLowerCase(),J=/android/gi.test(U),W=!!U.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/i),H=/iphone/gi.test(U),G=/ipad/gi.test(U),Y=/macintosh/gi.test(U),X=/windows/gi.test(U),$=/linux/gi.test(U),Q=/(lanxin|es360messenger)/gi.test(U),Z={isAndroid:J,isiOS:W,isiPhone:H,isiPad:G,isMac:Y,isWindows:X,isLanxinApp:Q,isLanxinMobile:Q&&(J||W),isLanxinPc:Q&&(Y||X||$),isLinux:$};T(window.LanxinJsBridgeHanlder)&&T(window.LanxinJsBridgeHanlder.platform)&&(Z=a(a({},Z),window.LanxinJsBridgeHanlder.platform));var ee=[{title:"选择联系人",name:"biz.chooseContacts",description:"打开蓝信通讯录，选择人员",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseContacts,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      type: ['friend', 'staff', 'sector'],\n      canChooseExternal: false,\n      max: 0,\n      maxTip: '',\n      existentStaffs: [staffIdA, staffIdB],\n      existentSectors: [sectorIdA, sectorIdB],\n      requiredStaffs: [staffIdA, staffIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n      selectGroupMembers: false,\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择人员",description:"选择联系人窗体标题",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},type:{type:Array,required:!1,default:["friend","staff"],description:"选择人员的类型<br />friend - 好友 staff - 组织人员 sector - 组织部门/分支",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=6.6.5",linux:void 0}},canChooseExternal:{type:Boolean,required:!1,description:"是否可以选择跨组织的外部联系人",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=6.6.5",linux:void 0}},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},existentStaffs:{type:Array,required:!1,description:"已选择的组织人员列表",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=7.7.10",linux:void 0}},existentSectors:{type:Array,required:!1,description:"已选择的部门/分支列表",version:{ios:">=7.6.15",android:">=7.6.15",windows:">=7.6.15",mac:">=7.7.10",linux:void 0}},requiredStaffs:{type:Array,required:!1,description:"必选的组织人员列表",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=7.7.10",linux:void 0}},requiredSectors:{type:Array,required:!1,description:"必选的部门/分支列表",version:{ios:void 0,android:void 0,windows:void 0,mac:">=7.7.10",linux:void 0}},selectGroupMembers:{type:Boolean,required:!1,description:"是否选中群成员，仅在群入口此参数有效",version:{ios:">=7.5.0",android:">=7.5.0",windows:">=7.5.0",mac:">=7.7.10",linux:void 0}}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      staffs: [{\n        name: '张三',\n        avatar: '',\n        staffId: '1001'\n      }],\n      sectors: [{\n        name: '蓝信开放平台',\n        sectorId: '10011',\n        count:'10'\n      }]\n    }\n  }",response:{staffs:{type:Array,description:"已选择组织人员列表，具体参考上方返回值"},sectors:{type:Array,description:"已选择部门/分支列表，具体参考上方返回值"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.5.0",linux:void 0}},{title:"选择部门",name:"biz.chooseDepartments",description:"选择部门。接口会返回部门的信息，是以部门为维度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseDepartments,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      max: 20,\n      maxTip: '',\n      existentSectors: [sectorIdA, sectorIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择部门",description:"选择部门窗体标题"},multiple:{type:Boolean,required:!1,default:!0,description:"\t是否允许多选"},max:{type:Number,required:!1,default:20,description:"可选最大分支数量，当允许多选时生效"},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示"},existentSectors:{type:Array,required:!1,description:"已选择部门/分支列表"},requiredSectors:{type:Array,required:!1,description:"必选的部门/分支列表",version:{ios:void 0,android:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sectors: [{\n        name: '蓝信开放平台',\n        sectorId: '10011',\n        count:'10'\n      }]\n    }\n  }",response:{sectors:{type:Array,description:"已选择部门/分支列表"},name:{type:String,description:"分支名"},sectorId:{type:String,description:"分支id"},count:{type:String,description:"分支包含的人数"}},errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.6.15",android:">=7.6.15",windows:void 0,mac:">=7.7.10"}},{title:"选择蓝信联系人和手机联系人",name:"biz.chooseContactsAndPhoneContacts",description:"选人和通讯录。选中人会返回人的信息，选中通讯录会返回通讯录的信息，既选中人也选中通讯录会同时返回两者信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseContactsAndPhoneContacts,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      max: 0,\n      maxTip: '',\n      existentContacts: [staffIdA, staffIdB],\n      existentPhoneContacts: [phoneNumberA, phoneNumberB],\n      requiredContacts: [staffIdA, staffIdB],\n      requiredPhoneContacts: [phoneNumberA, phoneNumberB],\n      selectGroupMembers: false,\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择人员",description:"选择联系人窗体标题"},multiple:{type:Boolean,required:!1,default:!0,description:"选择联系人窗体标题"},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效"},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示"},existentContacts:{type:Array,required:!1,description:"已选择的组织人员列表"},existentPhoneContacts:{type:Array,required:!1,description:"已选择的通讯录列表"},requiredContacts:{type:Array,required:!1,description:"必选联系人（不可取消选中状态）",version:{ios:">=7.6.15",android:">=7.6.15"}},requiredPhoneContacts:{type:Array,required:!1,description:"必选通讯录列表（不可取消选中状态）",version:{ios:">=7.6.15",android:">=7.6.15"}},selectGroupMembers:{type:Boolean,required:!1,description:"是否选中群成员，仅在群入口此参数有效",version:{ios:">=7.5.0",android:">=7.5.0"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      contacts: [{\n        name: '张三',\n        avatar: '',\n        staffId: '1001'\n      }],\n      phoneContacts: [{\n        name: '蓝信开放平台',\n        phoneNumber: 188xxxx1234\n      }]\n    }\n  }",response:{contacts:{type:Array,description:"选择的联系人列表，具体参考上方返回值"},phoneContacts:{type:Array,description:"选择的手机通讯录列表，具体参考上方返回值"}},errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">=6.0.0",android:">=6.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"打开联系人蓝名片",name:"biz.openContactsCard",description:"打开联系人蓝名片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openContactsCard',\n    meta: {},\n    payload: {\n      staffId: '10001'\n    }\n  }",payload:{staffId:{type:String,required:!0,description:"联系人staffId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.0.0",mac:">6.6.0"}},{title:"打开群聊",name:"biz.openGroupChat",description:"打开群聊，后续不再维护，建议使用biz.openChat",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openGroupChat',\n    meta: {},\n    payload: {\n      groupId: 'abcd1234'\n    }\n  }",payload:{groupId:{type:String,required:!0,description:"群聊Id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.3.0",android:">6.3.0",windows:">6.3.0",mac:">6.6.0"}},{title:"选择会话",name:"biz.chooseChat",description:"调起客户端会话列表，选择会话后返回会话的openId",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseChat,\n    meta: {},\n    payload: {\n      title: '',\n      allowCreate: true,\n      max: 9,\n      type: [1, 2]\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择",description:"选择窗口的标题",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},allowCreate:{type:Boolean,required:!1,default:!0,description:"是否允许创建会话",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},max:{type:Number,default:9,required:!1,validator:function(e){return e>0&&100>=e},description:"选择会话最大数量，支持1 - 100",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}},type:{type:Array,required:!1,default:[1,2],description:"选择会话的类型 <br /> 1 - 群聊 <br /> 2 - 单聊",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      chatList: [{\n        chatId: 'xxxx',\n      }]\n    }\n  }",response:{chatList:{type:Array,description:"已选择会话列表，具体参考上方返回值"}},errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"获取免登code",name:"biz.getAuthCode",description:"获取免登授权码，不需要config",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getAuthCode',\n    meta: {},\n    payload: {\n      appId: 'appId01'\n    }\n  }",payload:{appId:{type:String,required:!0,description:"线下申请的应用id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      authCode: 'hYLK98jkf0m'\n    }\n  }",response:{authCode:{type:String,description:"免登授权码，只能使用一次，用于换取Token"}},errorCode:{61:{message:"APP_IS_NOT_INSTALL",description:"应用未安装，未对接到组织上"},62:{message:"INVALID_REDIRECT_URL",description:"无效的重定向地址(内部-70260002)，需要检查获取code的域名是否在可信域名中"},63:{message:"GET_AUTH_CODE_FAILED",description:"其他原因导致的获取code失败"}},type:"call",version:{ios:">6.6.0",android:">6.6.0",windows:">6.6.110",mac:">6.6.0"}},{title:"获取用户token",name:"biz.getVisitorToken",description:"获取端内蓝信用户Token",requestExample:"\n  {\n    api: 'internal.biz.getVisitorToken',\n    meta: {},\n    payload: {\n      timestamp: '1231123',\n      nonceStr: 'qweqweqwe',\n      signature: 'qweqweqweqweqweqe'\n    }\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      deviceId：'',\n      center: {\n        userId: 'abc123',\n        token: 'asdasd',\n        domain: ''\n      },\n      org: {\n        userId: {\n          oid: 123,\n          uid: 1231,\n        },\n        token: '',\n        domain: ''\n      }\n    }\n  }",type:"call"},{title:"选择云盘文件",name:"biz.chooseCloudFile",description:"调起客户端云盘，选择云文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseCloudFile,\n    meta: {},\n    payload: {\n      multiple: true,\n      max: 9,\n      maxTip: '当前选择文件数已超出最大限制',\n      existent: [mediaId1, mediaId2],\n      types: ['pdf', 'jpg', 'doc'],\n    }\n  }",payload:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选，默认true"},max:{type:Number,required:!1,default:9,description:"可选最大数量，当允许多选时生效"},maxTip:{type:String,required:!1,default:"当前选择文件数已超出最大限制",description:"选择数量超出最大限制提示"},existent:{type:Array,required:!1,description:"已经选择的文件列表"},types:{type:Array,required:!1,description:"需要选择的文件类型(文件名后缀)，不传则可以选择全部文件"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      fileList: [{\n        mediaId: 'xxxx',\n        name:'我的文档.doc',\n        size: 1000,\n        type: 'doc'\n      }]\n    }\n  }",response:{fileList:{type:Array,description:"已选择文件列表，具体参考上方返回值"}},errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"兑换OpenId失败等"},71:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.86",windows:void 0,mac:">6.6.202",linux:void 0}},{title:"选择本地文件",name:"biz.chooseLocalFile",description:"选择系统本地文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseLocalFile,\n    meta: {},\n    payload: {\n      multiple: true,\n      max: 3,\n      maxTip: '',\n      maxSize: 1024,\n      types: ['pdf', 'jpg', 'doc'],\n    }\n  }",payload:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选，默认true"},max:{type:Number,required:!1,default:9,description:"可选最大数量，当允许多选时生效，最小为1"},maxTip:{type:String,required:!1,default:"当前选择文件数已超出最大限制",description:"选择数量超出最大限制提示"},maxSize:{type:Number,required:!1,description:"最大文件大小，单位：Byte"},types:{type:Array,required:!1,description:"需要选择的文件类型(文件名后缀)，不传则可以选择全部文件"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      fileList: [{\n        localId: 'xxxx',\n        name:'我的文档.doc',\n        size: 1000,\n        type: 'doc'\n      }]\n    }\n  }",response:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选",version:{android:void 0}},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效，最小为1",version:{android:void 0}},maxTip:{type:String,required:!1,description:"最大文件大小，单位：Byte",version:{android:void 0}}},errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:">7.0.70",mac:">7.0.70",linux:void 0}},{title:"获取分支成员",name:"biz.getSectorMembers",description:"获取分支下所有人员",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:'\n  {\n    api: \'biz.getSectorMembers\',\n    meta: {},\n    payload: {\n      sectors: ["sectorId1","sectorId2"]\n    }\n  }',payload:{sectors:{type:Array,required:!0,description:"一组分支sectorId"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: [{\n      name: '张三',\n      avatar: '',\n      staffId: '1001'\n    },{\n      name: '张三',\n      avatar: '',\n      staffId: '1001'\n    }]\n  }",response:{name:{type:String,description:"组织人员姓名"},avatar:{type:String,description:"组织人员头像Url"},staffId:{type:String,description:"组织人员 staffId(OPENID)"}},errorCode:{81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.67",android:">7.0.67",windows:void 0,mac:void 0}},{title:"创建群会话",name:"biz.createGroupChat",description:"创建群会话",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.createGroupChat',\n    meta: {},\n    payload: {\n      name: '',\n      requiredStaffs: [staffIdA, staffIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n    }\n  }",payload:{name:{type:String,required:!1,description:"群名,不填写则为前几位成员或分支名称拼接"},requiredStaffs:{type:Array,required:!1,description:"默认选中的群成员列表，不可取消选中"},requiredSectors:{type:Array,required:!1,description:"默认选中的用户列表部门 / 分支列表"}},responseExample:'\n  {\n    status: {\n      code: 0,\n      message: \'OK\'\n    },\n    meta: {},\n    data: {\n      name:"aaaa",\n      groupId:"openGroupId"\n    }\n  }',response:{name:{type:String,description:"群名称"},groupId:{type:String,description:"群id"}},errorCode:{141:{message:"LIMITED_NUMBER_OF_PERSON",description:"人数受限（>=3人)"}},type:"call",version:{ios:">=7.7.15",android:">=7.7.15",windows:void 0,mac:void 0,linux:void 0}},{title:"搜索",name:"biz.search",description:"用于JS调用Native的搜索接口",deploy:[{isCustomized:!0,company:"政协",application:""}],requestExample:"\n  {\n    api: 'biz.search',\n    meta: {},\n    payload: {\n      keyword: String,\n      pageIndex: Number,\n      searchType: Number,\n      count: Number,\n      coid: Number,\n      ccid: umber,\n      ctype: Number,\n      name,\n      value,\n      exclude\n    }\n  }",payload:{keyword:{type:String,required:!0,description:"搜索关键词"},pageIndex:{type:Number,default:0,required:!1,description:"分页索引"},searchType:{type:Number,required:!0,description:"搜索类型，支持按位或组合。<br />1 - 联系人<br />2 - 群<br />4 - 全局聊天记录<br />8 - 公众号<br />16 - 文章<br />32 - 收藏<br />64 - 文档<br />128 - 单个会话中的搜索<br />256 - 通讯录<br />512 - 群成员<br />1024 - 备忘录<br />2048 - 会话分组"},count:{type:Number,required:!0,description:"搜索条数"},coid:{type:Number,default:0,required:!1,description:"发起人所在组织"},ccid:{type:Number,default:0,required:!1,description:"会话id"},ctype:{type:Number,default:0,required:!1,description:"会话类型"},name:{type:String,required:!1,description:"name value exclude三个字段连用,name=委员, value=是, exclude=true. 含义: 排除掉委员"},value:{type:String,required:!1},exclude:{type:Boolean,required:!1}},ResponseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n        data:String  //返回json\n    }\n  }",response:{data:{type:String,description:"json"}},errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"定位到消息位置(政协)",name:"biz.locateToMessage",description:"打开会话详情，定位到指定的消息位置",deploy:[{isCustomized:!0,company:"北京政协",application:""}],requestExample:"\n  {\n    api: 'biz.locateToMessage',\n    meta: {},\n    payload: {\n      coid: Number,\n      ccid: Number,\n      seq: Number,\n      chatType: Number,\n      name: String\n    }\n  }",payload:{coid:{type:Number,required:!0,description:"会话组织ID"},ccid:{type:Number,required:!0,description:"会话ID"},seq:{type:Number,required:!1,description:"消息索引"},chatType:{type:Number,required:!0,description:"会话类型<br/>0-单聊 1-群聊 7-systemApp"},name:{type:String,required:!1,description:"名称"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{121:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"转换openId",name:"biz.transformImplIds",description:"将核心id转换为openId",deploy:[{isCustomized:!0,company:"政协",application:""}],requestExample:"\n  {\n    api: 'biz.transformImplIds',\n    meta: {},\n    payload: {\n      implType: Number,\n      implIds: [{\n        oid:Number,\n        fid:Number,\n        uid:Number,\n        appId:Number\n      }]\n    }\n  }",payload:{implType:{type:Number,required:!0,description:"要转换的id类型：<br />1 - appId<br />2 - staffId<br />6 - mediaId"},implIds:{type:Array,required:!0,description:"核心id组成的数组<br />oid - 组织id<br />fid - 文件id<br />uid - 用户id<br />appId应用id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      openIds: {\n        xxx_xxx: String\n      }\n    }\n  }",response:{openIds:{type:Array,description:"转换后的id数组，\b格式如返回结果，key格式为「oid_fid/uid/appId」,value为所需openId"}},errorCode:{111:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"打开会话",name:"biz.openChat",description:"打开指定会话窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openChat',\n    meta: {},\n    payload: {\n      chatId: 'abcd1234'\n    }\n  }",payload:{chatId:{type:String,required:!0,description:"会话的openId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{151:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"其他应用打开",name:"biz.otherAppOpen",description:"其他应用打开文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.otherAppOpen,\n    meta: {},\n    payload: {\n      localId: '123'\n    }\n  }",payload:{localId:{type:String,required:!0},filename:{type:String,required:!1}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXISTS",description:"指定要打开的本地文件不存在"},"-11":{message:"FAIL",description:"文件打开失败，没有匹配到可以打开文档的第三方应用"},171:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"获取蓝信设置",name:"biz.getSetting",description:"获取蓝信的app内设置，支持下载设置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getSetting',\n    meta: {},\n    payload: {\n      type: 'fileDownload'\n    }\n  }",payload:{type:{type:String,required:!0,description:"所需设置类型，<br /> fileDownload - 下载设置"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      noWifiSizeLimit：0\n    }\n  }",response:{noWifiSizeLimit:{type:Number,description:"type为fileDownload时返回，小于noWifiSizeLimit MB的文件可自动下载<br />-1 - 全部自动 0 - 不允许自动下载<br />其他按照大小设定 例如：小于100M自动下载  返回 100"}},errorCode:{161:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0,linux:void 0}},{title:"打开指定页面",name:"biz.openSpecificView",description:"打开客户端指定页面",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openSpecificView',\n    meta: {},\n    payload: {\n      type: String,\n      version: String,\n      query: {}\n    }\n  }",payload:{type:{type:String,required:!0,description:"打开的openUrl type，例如搜索页为search"},version:{type:String,required:!1,default:"v1",description:"openUrl版本"},resize:{type:Boolean,required:!1,default:!0},query:{type:Object,required:!1,default:{},description:"唤起openUrl传递的参数 需要和各业务约定"}},payloadExtra:{title:"query说明",headers:["页面","type","version","query"],rows:[["search","search","v1",""],["复制到个人云盘","operateDocument","v1",'{operate: "copyPerson", fileId="123"}'],["文件中转页","fileRelayPage","v1","{mediaId:{oid: 123, fid: 34}, spaceType: 1}"],["打开webview(mac&windows)","openLocalApp","v1",'{oid: xxx, url: "xxx", openByBrowser: false, // 是否使用外置浏览器打开 biz: "cooperation" // 业务标识 }']]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"获取初始化数据",name:"biz.initWindowData",description:"H5初始化页面后，向Native拿页面传入数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.initWindowData',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}},{title:"发送数据",name:"biz.sendWindowData",description:"h5回传数据给Native",deplloy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.sendWindowData',\n    meta: {},\n    payload: {\n      requestTag:String,\n      isFinish:Boolean, // 是否关闭页面\n      data:Object,\n      status: {\n        code: 0,\n        message: 'OK'\n      },\n    }\n  }",payload:{requestTag:{type:String,required:!1,description:"该次页面回传数据的唯一标示，H5在url上通过requestTag来获取，用于日志记录，及页面数据绑定。"},isFinish:{type:Boolean,default:!1,required:!1,description:"回传后是否关闭H5"},data:{type:Object,required:!1,description:"H5回传给Native的数据"},status:{type:Object,required:!1,description:"",default:{code:0,message:"ok"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"设置剪切板内容",name:"biz.setClipboardData",description:"设置剪切板内容,只支持文本内容",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.setClipboardData,\n    meta: {},\n    payload: {\n      content: ''\n    }\n  }",payload:{content:{type:String,required:!0,description:"向剪切板中设置的内容"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"获取剪切板内容",name:"biz.getClipboardData",description:"获取剪切板内容,只支持文本内容,如果剪切板没有内容返回空字符串",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.getClipboardData,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: ''\n    }\n  }",response:{content:{type:String,default:""}},errorCode:{221:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"获取微信发票",name:"biz.getWxInvoice",description:"获取剪切板内容,只支持文本内容,如果剪切板没有内容返回空字符串",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.getWxInvoice,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      // 具体信息取决于微信发票，以下response仅供参考，参考 https://developers.weixin.qq.com/doc/offiaccount/WeChat_Invoice/E_Invoice/Reimburser_API_List.html#5\n    }\n  }",response:{card_id:{type:String,required:!0,description:"微信发票信息的json字符串"},begin_time:{type:Number,required:!0,description:"发票有效起止日期"},end_time:{type:String,required:!0,description:"发票的有效期截止时间"},openid:{type:String,required:!0,description:"用户标识"},type:{type:String,required:!0,description:"发票的类型，如广东增值税普通发票"},payee:{type:String,required:!0,description:"发票的收款方"},detail:{type:String,required:!0,description:"发票详情"},user_info:{type:Object,required:!0,description:"用户可在发票票面看到的主要信息"}},errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"添加联系人",name:"biz.addContact",description:"添加联系人",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.addContact,\n    meta: {},\n    payload: {\n      name: '蓝信用户-1',\n      phone: '13666666666'\n    }\n  }",payload:{name:{type:String,required:!0,description:"联系人姓名"},phone:{type:String,required:!0,description:"联系人手机号"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"添加失败"},241:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"创建会话",name:"biz.createSingleChat",description:"创建会话，目前支持私聊和智能机器人",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.createSingleChat',\n    meta: {},\n    payload: {\n      type: 'normal',\n      id: ''\n    }\n  }",payload:{type:{type:String,required:!1,default:"normal",description:"normal - 普通私聊; bot - 智能机器人(目前仅支持应用机器人)"},id:{type:String,required:!0,description:"人员或者应用机器人对应应用的appId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{chatId:{type:String,description:"会话id"}},errorCode:{261:{message:"-",description:"保留"},"-1":{message:"INVALID_REQUEST",description:"参数错误，id或type错误等"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}}],ne=[{title:"选择图片",name:"media.chooseImage",description:"选择图片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.chooseImage',\n    meta: {},\n    payload: {\n      count: 1,\n      quality: 'high',\n      sourceType: ['album', 'camera'],\n    }\n  }",payload:{count:{type:Number,required:!1,default:9,description:"最多可以选择的图片张数，默认9"},sourceType:{type:Array,required:!1,default:["album","camera"],description:"album - 从相册选图，camera - 使用相机<br />当使用camera方式选择图片的时候忽略count 【仅在移动端生效】"},sizeType:{type:Array,required:!1,default:["original","compressed"],description:"original - 原图，compressed - 压缩图<br />选择图片大小选项，默认二者都有【仅在移动端生效】"},quality:{type:String,required:!1,default:"low"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      tempFiles: [{localId: 'xxxxxxxx', path:\"\", base64: \"\" , type:\"\" , size:102400, name: 'xxx'}]\n    }\n  }",response:{tempFiles:{type:Array,description:"图片的本地文件列表"},localId:{type:String,description:"本地文件ID"},path:{type:String,description:"本地文件路径"},base64:{type:String,description:"图片经 base 64 编码后的字符串，该字段只适用于展位图使用"},type:{type:String,description:"本地文件类型，mimeType"},size:{type:String,description:"本地文件大小，单位：Byte"},name:{type:String,description:"文件名称"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.6.110",mac:">6.6.202"}},{title:"预览图片",name:"media.previewImage",description:"预览图片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    {\n      api: 'media.previewImage',\n      meta: {},\n      payload: {\n        current: '',\n        urls: [],\n        actions:[]\n      }\n    }\n  }",payload:{current:{type:[Number,String],required:!1,description:"当前显示图片的链接，不填则默认为 urls 的第一张"},urls:{type:Array,required:!0,description:"需要预览的图片链接列表，支持本地链接（localId）和远程链接"},actions:{type:Array,required:!1,description:'长按图片支持的事件["save","share"]<br />save - 保存到本地<br />share - 转发功能，长按选择后再选择会话或群 '}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:">6.6.110",mac:">6.6.202"}},{title:"压缩图片",name:"media.compressImage",description:"压缩图片接口，可选压缩质量",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.compressImage',\n    meta: {},\n    payload: {\n      filePaths: ['localId', 'localId']\n      ratio: 80\n    }\n  }",payload:{filePaths:{type:Array,required:!0,description:"图片的路径数组"},ratio:{type:Number,required:!1,default:80,description:"压缩比例，0-100数值，数值越小，质量越低，压缩率越高（降质量）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      tempFilePaths: ['tempLocalId']\n    }\n  }",response:{tempFilePaths:{type:Array,description:"压缩后的图片路径数组"}},errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"获取图片信息",name:"media.getImageInfo",description:"通过图片本地id获取图片信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.getImageInfo',\n    meta: {},\n    payload: {\n      src: 'localId'\n  }",payload:{src:{type:String,required:!0,description:"图片的路径，可以是相对路径、临时文件路径、存储文件路径"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      width: 100,\n      height: 100,\n      size: 102400\n    }\n  }",response:{width:{type:Number,required:!1,description:"图片宽度，单位 px。不考虑旋转"},height:{type:Number,required:!1,description:"图片高度，单位 px。不考虑旋转"},size:{type:Number,required:!1,description:"文件大小，单位：Byte"}},errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"选择视频",name:"media.chooseVideo",description:"选择视频",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.chooseVideo',\n    meta: {},\n    payload: {\n      sourceType: ['album', 'camera'],\n      compressed: true,\n      maxDuration: 300,\n      needThumbnail: true\n    }\n  }",payload:{sourceType:{type:Array,default:["album","camera"],description:"album - 从相册选图，camera - 使用相机<br />当使用camera方式选择图片的时候忽略count 【仅在移动端生效】"},compressed:{type:Boolean,default:!0,description:"\t是否压缩所选择的视频文件"},maxDuration:{type:Number,default:300,description:"拍摄视频最长拍摄时间，单位秒"},needThumbnail:{type:Boolean,default:!1,description:"是否需要缩略图",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:">=7.31.30"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: 'xxxxxx',\n      duration: 50,\n      size: 60340,\n      height: 320,\n      width: 640,\n      name: 'xxx',\n      thumbnail: ''\n    }\n  }",response:{localId:{type:String,description:"视频本地ID"},duration:{type:Number,description:"视频时长"},type:{type:String,description:"视频文件类型"},size:{type:Number,description:"本地文件大小，单位：Byte"},height:{type:Number,description:"返回选定视频的高度"},width:{type:Number,description:"返回选定视频的宽度"},name:{type:String,description:"文件名称"},thumbnail:{type:String,description:"缩略图base64，如果 payload中needThumbnail为true会返回缩略图，可用作视频封面"}},errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.6.110",mac:">6.6.202"}},{title:"预览视频",name:"media.previewVideo",description:"预览视频",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.previewVideo',\n    meta: {},\n    payload: {\n      url: 'xxxxxxxx',\n      startTime: 3,\n      orientation: 'landscape'\n    }\n  }",payload:{url:{type:String,required:!0,description:"视频URL(http/https) 或视频选择/下载后的localId（本地路径的唯一标识）"},startTime:{type:Number,required:!1,description:"当前视频播放起始时间点，单位s，int类型"},orientation:{type:String,required:!1,description:"支持横屏，默认portrait，支持portrait | landscape，定义输出设备中的页面可见区域高度是否大于或等于宽度"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      endTime:100,\n      finished: true\n    }\n  }",response:{endTime:{type:Number,description:"当前视频播放的进度，点击关闭按钮回传，单位s"},finished:{type:Boolean,description:"当前视频播放是否结束"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"开始录音",name:"media.startRecord",description:"开始录音",deploy:[{isCustomized:!1,company:"公有云",application:""}],payload:{format:{type:String,required:!1,description:"需要的音频格式"}},requestExample:"\n  {\n    api: 'media.startRecord',\n    meta: {},\n    payload: {\n      format: 'mp3'\n    }\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"停止录音",name:"media.stopRecord",description:"停止录音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.stopRecord',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: 'xxxxxx',\n      duration: 75\n    }\n  }",response:{localId:{type:String,description:"文件本地ID"},duration:{type:Number,description:"录音时长 单位：秒"},type:{type:String,description:"音频文件类型"},size:{type:Number,description:"音频文件大小，单位：Byte"}},errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"监听录音停止",name:"media.onRecordEnd",description:"达到录音时长限制，被系统打断时会触发该事件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onRecordEnd',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      duration: 75\n    }\n  }",docPayload:{localId:{type:String,required:!1,description:"文件本地ID"},duration:{type:String,required:!1,description:"录音时长 单位：秒"},type:{type:String,required:!1,description:"音频文件类型"},size:{type:Number,required:!1,description:"音频文件大小，单位：Byte"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"播放语音",name:"media.playVoice",description:"播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.playVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx',\n      startOver: false\n    }\n  }",payload:{localId:{type:String,required:!0,description:"选择或下载音频后，获取的音频localId"},startOver:{type:Boolean,required:!1,default:!1,description:"是否重新播放当前音频（对于暂停的音频默认继续当前进度播放）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{81:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"暂停播放语音",name:"media.pauseVoice",description:"暂停播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.pauseVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"需要暂停播放的音频localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"停止播放语音",name:"media.stopVoice",description:"停止播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.stopVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"正在播放音频的localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听语音播放中",name:"media.onVoicePlay",description:"监听语音播放中",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onVoicePlay',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      currentTime: 1212\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"当前播放音频的localId"},currentTime:{type:Number,required:!1,description:"当前播放进度 单位 S/秒"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听语音播放完毕",name:"media.onVoicePlayEnd",description:"监听语音播放完毕",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onVoicePlayEnd',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx'\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"音频文件的本地ID"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{121:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听音频中断开始",name:"media.onAudioInterruptionBegin",description:"以下场景会触发此事件：闹钟、电话、FaceTime 通话、语音聊天、视频聊天。此事件触发后，应用内所有音频会暂停",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onAudioInterruptionBegin',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"监听音频中断结束",name:"media.onAudioInterruptionEnd",description:"在收到 onAudioInterruptionBegin 事件之后，应用内所有音频会暂停，收到此事件之后才可再次播放成功。",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onAudioInterruptionEnd',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"监听语音识别",name:"media.onRecognizeSpeech",description:"语音识别结果回传，配合startRecognizeSpeech使用",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onRecognizeSpeech',\n    meta: {},\n    payload: {\n      text: String,\n    }\n  }",docPayload:{text:{type:String,required:!0,description:"语音识别出来的文字"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{161:{message:"-",description:"保留"},"-8":{message:"NOT_EXIST",description:"未先调用录制，请先调用录制接口"}},type:"listen",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"结束语音识别",name:"media.releaseRecognizeSpeech",description:"结束语音识别",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.releaseRecognizeSpeech',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      text: ''\n    }\n  }",response:{text:{type:String,required:!1,description:"语音识别出来的文字"}},errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"开始语音识别",name:"media.startRecognizeSpeech",description:"开始语音识别",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.startRecognizeSpeech',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-9":{message:"用户未授权录音功能",description:"首次使用的授权"},"-10":{message:"开启中",description:"首次使用的授权"},"-11":{message:"语音识别中",description:"不可重启开启"},151:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"开始朗读文本",name:"media.readAloudStart",description:"开始朗读文本",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.readAloudStart',\n    meta: {},\n    payload: {\n      content : String,\n    }\n  }",payload:{content:{type:String,required:!0,description:"文本内容，内容大小限制未定"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"停止朗读文本",name:"media.readAloudStop",description:"停止朗读文本",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.readAloudStop',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"监听朗读",name:"media.onReadAloudReceive",description:"接收朗读状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onReadAloudReceive',\n    meta: {},\n    payload: {\n      state: Number,\n    }\n  }",docPayload:{state:{type:Number,required:!0,description:"朗读状态<br />0 - 开始朗读<br />1 - 朗读结束<br />2 - 朗读出错（预留）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"listen",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0,linux:void 0}},{title:"转发文件",name:"media.forwardFiles",description:"调用客户端转发文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.forwardFiles',\n    meta: {},\n    payload: {\n      list: [\n        {\n          type: 'cooperate',\n          fileId: '',\n          authority: ['edit']\n        }\n      ]\n    }\n  }",payload:{list:{type:Array,required:!0,description:"文件列表"},type:{type:String,required:!1,default:"invite",description:'转发类型< br/>invite - "邀请" forward - "转发"'}},payloadExtra:{title:"list数据结构",headers:["字段","类型","必填","默认","说明","ios版本","android版本","windows版本","mac版本","linux版本"],rows:[["type","String","否","noCooperate","cooperate - 协作文件<br />noCooperate - 非协作文件",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"],["fileId","String","是","-","file的openId",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"],["authority","Array","否","-","read 阅读<br />edit 编辑<br />forward 转发<br />download 下载或转存",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{241:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:">=7.30.30",mac:">=7.30.30"}},{title:"保存到系统相册",name:"media.saveToPhotoAlbum",description:"保存图片或视频到系统相册",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: media.saveToPhotosAlbum,\n    meta: {},\n    payload: {\n      url: ''\n    }\n  }",payload:{url:{type:String,required:!0,description:"图片或视频的url或者localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"保存后的文件localId"}},errorCode:{251:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"重新播放语音",name:"media.resumeVoice",description:"重新播放暂停语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.resumeVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"需要重新播放的音频localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{261:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}}],te={250:{message:"NOT_INIT",description:"未初始化蓝牙适配器"},251:{message:"NOT_AVAILABLE",description:"当前蓝牙适配器不可用"},252:{message:"NO_DEVICE",description:"没有找到指定设备"},253:{message:"CONNECTION_FAIL",description:"连接失败"},254:{message:"NO_SERVICE",description:"没有找到指定服务"},255:{message:"NO_CHARACTERISTIC",description:"没有找到指定特征值"},256:{message:"NO_CONNECTION",description:"当前连接已断开"},257:{message:"PROPERTY_NOT_SUPPORT",description:"当前特征值不支持此操作"},258:{message:"SYSTEM_ERROR",description:"其余所有系统上报的异常"},259:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持"},260:{message:"RESETTING",description:"蓝牙重置中，如果重置成功会回调onBluetoothAdapterStateChange"},261:{message:"UNAUTHORIZED",description:"应用尚未被授权使用蓝牙"},262:{message:"BLUETOOTH_CLOSE",description:"蓝牙已关闭"},263:{message:"UNKNOWN",description:"未知错误"}},ie=[{title:"设置导航栏标题",name:"ui.setNavigationBarTitle",description:"设置导航栏标题",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarTitle',\n    meta: {},\n    payload: {\n      title: '标题'\n    }\n  }",payload:{title:{type:String,required:!1,description:"控制标题文本，空字符串表示显示默认文本"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}},{title:"设置导航栏菜单",name:"ui.setNavigationBarMenu",description:"设置导航栏菜单",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarMenu',\n    meta: {},\n    payload: {\n      type: 'dropdown',\n      forceShowDialog: false\n      items: [{\n        id: 1,\n        name: '菜单1'\n      },{\n        id: 2,\n        name: '菜单2'\n      }]\n    }\n  }",payload:{type:{type:String,required:!1,default:"dropdown",description:"菜单类型<br />dropdown - 右上角显示的下拉<br />actionSheet - 下方菜单"},forceShowDialog:{type:Boolean,required:!1,default:!1,description:"当菜单只有一项时是否显示dialog，默认不限制，直接显示在右上角"},items:{type:Array,description:"菜单项列表，如果需要清空菜单可传[]"}},payloadExtra:{title:"items数据结构",headers:["字段","类型","必填","说明","ios","android","mac","windows","linux"],rows:[["id","Number","是","菜单id，当点击是会将对应的菜单id回传",">6.5.8",">6.5.8","undefined","undefined","undefined"],["name","String","是","菜单名称",">6.5.8",">6.5.8","undefined","undefined","undefined"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",triggerName:"internal.ui.onNavigationBarMenuClick",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}},{title:"设置导航栏背景色",name:"ui.setNavigationBarBgColor",description:"全屏模式下设置导航栏颜色，状态栏会同步更新，仅在全屏模式下生效, 即在url后面拼接lx_fullscreen=true参数",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarBgColor',\n    meta: {},\n    payload: {\n        color:\"FF556677\"\n    }\n  }",payload:{color:{type:String,required:!0,description:"设置的十六进制色值必须是AARRGGBB"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">7.6.30",android:">7.6.30",windows:void 0,mac:void 0}},{title:"关闭当前窗口",name:"ui.closeWindow",description:"关闭当前窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.closeWindow',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:">6.6.110",mac:">6.6.202"}},{title:"打开新视图窗口",name:"ui.openView",description:"打开新视图窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.openView',\n    meta: {},\n    payload: {\n      mode: 'webview',\n      navigationBarBackgroundColor: '#4E74BB',\n      navigationBarFrontStyle: 'white',\n      url: 'http://lanxin.cn',\n      useSplitScreen:false\n    }\n  }",payload:{mode:{type:String,required:!1,default:"webview",description:"窗体模式 dialog / webview"},navigationBarBackgroundColor:{type:String,required:!1,default:"#4E74BB",description:"导航栏背景颜色值 HexColor。mode=webview时生效"},navigationBarFrontStyle:{type:String,required:!1,default:"white",description:"前景样式，包括按钮、标题、状态栏的前景色 black / white。mode=webview时生效"},url:{type:String,required:!0,description:"需要打开的h5url链接,必填 URLEncode"},useSplitScreen:{type:Boolean,required:!1,default:!1,description:"是否分屏。仅在pad生效"}},responseExample:"{\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"重置屏幕状态",name:"ui.resetView",description:"重置屏幕状态(恢复到竖屏状态)，需与ui.rotateView配合使用。",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.resetView',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"旋转webview",name:"ui.rotateView",description:"旋转webview到横屏状态并隐藏页面导航栏。开发者在使用此JSAPI后，需要提供重置按钮(ui.resetView)，保证用户可以返回竖屏状态或退出页面注：横屏状态下iOS禁止手势返回操作",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.rotateView',\n    meta: {},\n    payload: {\n      showStatusBar: true,\n      clockwise: true,\n    }\n  }",payload:{showStatusBar:{type:Boolean,default:!0,description:"是否显示状态栏(iOS)"},clockwise:{type:Boolean,default:!0,description:"是否为顺时针方向旋转"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"设置ios回弹效果",name:"ui.webViewBounce",description:"用于控制是否启用iOS webview的回弹效果",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.webViewBounce',\n    meta: {},\n    payload: {\n      enable : true //控制是否启用webview的回弹效果\n    }\n  }",payload:{enable:{type:Boolean,required:!0,default:!0,description:"控制是否启用webview的回弹效果<br />true - 启用回弹效果<br />false - 禁用回弹效果"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"设置扩展屏",name:"ui.setExtendedView",description:"当这个应用配置的是扩展屏应用时，开发者可以调用该事件用于控制底部Tab和顶部导航栏的显示和隐藏",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setExtendedView',\n    meta: {},\n    payload: {\n      fullscreen: true\n    }\n  }",payload:{fullscreen:{type:Boolean,required:!1,description:"控制是否全屏显示，当应用配置的是扩展屏应用时生效<br />true - 全屏显示，隐藏底部Tab和顶部导航栏<br />false - 显示底部Tab和顶部导航栏"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{80:{message:"",description:"不是扩展屏或设置失败"},81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面不可见",name:"ui.onViewPause",description:"监听页面不可见",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onViewPause',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面重新可见",name:"ui.onViewResume",description:"监听页面重新可见",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onViewResume',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面返回",name:"ui.onHistoryBack",description:"监听页面返回",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onHistoryBack',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: true\n    }\n  }",response:{result:{type:Boolean,description:"是否要触发页面返回, false 不触发页面返回否则触发页面返回"}},errorCode:{51:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"获取主题信息",name:"ui.getThemeInfo",description:"获取客户端主题信息(字号缩放等)",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.getThemeInfo',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      textSizeAdjust: 1\n    }\n  }",response:{textSizeAdjust:{type:Number,required:!1,description:"客户端设置的字体缩放系数(基于标准字号)<br />1 - 标准<br />1.25 - 大号<br />1.5 - 特大"}},errorCode:{121:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.70",android:">7.0.70",windows:void 0,mac:void 0}},{title:"打开新视图窗口",name:"ui.openApp",description:"打开新视图窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.openApp',\n    meta: {},\n    payload: {\n      type: 'webview',\n      appId: '12234-4432',\n      url: '',\n      data: ''\n    }\n  }",payload:{type:{type:String,required:!0,description:"应用类型blueprint、webview"},url:{type:String,required:!1,description:"url, type为webview时必传"},appId:{type:String,required:!0,description:"蓝信下线申请的应用Id，比如 12234-4432"},data:{type:String,required:!1,description:"透传Json字符串，需要进行URLencode之后传递，type为blueprint时必传"}},errorCode:{61:{message:"-",description:"保留"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"设置屏幕方向",name:"ui.setScreenDirection",description:"设置屏幕方向",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setScreenDirection',\n    meta: {},\n    payload: {\n      direction: 0\n    }\n  }",payload:{direction:{type:Number,required:!0,description:"屏幕方向，默认跟随原生设置<br />1 - auto - 自重力感应<br />2 - landscape - 横屏<br />3 - portrait - 竖屏"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"监听页面关闭",name:"ui.onCloseWindow",description:"监听页面关闭，关闭webview即触发",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onCloseWindow',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: true\n    }\n  }",response:{result:{type:Boolean,description:"返回true页面关闭，返回false页面不关闭"}},errorCode:{151:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"设置actionSheet",name:"ui.setActionSheet",description:"设置下方actionSheet，用法类似setNavigationBarMenu",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setActionSheet',\n    meta: {},\n    payload: {\n      items: [{\n        id: 1,\n        name: '菜单1'\n      },{\n        id: 2,\n        name: '菜单2'\n      }]\n    }\n  }",payload:{items:{type:Array,required:!0,description:"菜单项列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{161:{message:"-",description:"保留"}},type:"call",triggerName:"internal.ui.onActionSheetClick",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"设置窗口大小",name:"ui.resizeWindow",description:"Windows/Linux 设置窗口大小",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.resizeWindow',\n    meta: {},\n    payload: {\n      action: minimize,\n      size: [800,600],\n      location: 'top'\n    }\n  }",payload:{action:{type:String,required:!0,description:"minimize - 最小化<br />maximize - 最大化<br />restore - 恢复初始尺寸"},size:{type:Array,required:!1,description:"[width, height]"},location:{type:String,required:!1,description:"窗口位置，例如center - 居中。可设置值包括top/left/bottom/right/left-top/left-bottom/right-top/right-bottom"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}},{title:"监听导航栏自定义菜单展开",name:"ui.onNavigationBarMenuOpen",description:"监听导航栏自定义菜单展开",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onNavigationBarMenuOpen',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"listen",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"监听主题信息变化",name:"ui.onThemeInfoChange",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onThemeInfoChange',\n    meta: {},\n    payload: {\n      theme: 'dark'\n    }\n  }",docPayload:{theme:{type:String,description:"主题<br />dark - 深色模式；light - 浅色模式"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"listen",version:{ios:">8.4.0",android:">8.4.0",windows:void 0,mac:void 0}}],re=[{title:"发起支付",name:"pay.requestPayment",description:"发起支付",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'pay.requestPayment',\n    meta: {},\n    payload: {\n      appId: '123456',\n      orderId: '123123',\n      token: 'uioasdfkljsadfhasdf',\n      package: 'oiuasdfklsjdfkasdf'\n    }\n  }",payload:{appId:{type:String,required:!0,description:"发起支付的第三方应用appId"},orderId:{type:String,required:!0,description:"支付订单ID，通过蓝信支付平台下单后得到的支付订单ID"},token:{type:String,required:!0,description:"支付Token，通过蓝信支付平台下单后得到的Token"},package:{type:String,required:!0,description:'支付package，通过蓝信支付平台下单后得到的package字段<br />备注：package 是key1=value1&key2=value2结构AES加密字串，<br />native使用appId通过核心服务解密接口得到：,<br />"payment_url=xxxxxxxxx". 用于拼接打开收银台openUrl,<br />"callback_url=xxxxxxxxx". 用于收银台状态回调地址'}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      orderId: 'order100010'\n    }\n  }",response:{orderId:{type:String,description:"发起支付的订单ID"}},errorCode:{11:{message:"REQUEST_PAYMENT_FAILED",description:"发起支付失败"},12:{message:"PAYMENT_FAILED",description:"支付失败"},13:{message:"PAYMENT_RESULT_EXCEPTION",description:"获取支付结果异常  "},14:{message:"PAYMENT_ORDER_EXCEPTION",description:"支付订单异常"},15:{message:"PAYMENT_ORDER_EXPIRED",description:"支付订单已过期"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}}],oe=[{title:"分享",name:"utils.share",description:"自定义分享",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.share',\n    meta: {},\n    payload: {\n      title: '',\n      content: '',\n      url: '',\n      pcUrl: '',\n      image: ''\n    }\n  }",docPayload:{title:{type:String,required:!0,description:"分享标题"},content:{type:String,required:!0,description:"分享内容"},url:{type:String,required:!0,description:"分享url地址"},pcUrl:{type:String,required:!1,description:"分享的pc端url地址",version:{ios:">=8.0.0",android:">=8.0.0",mac:">=7.30.30",windows:">=7.30.30"}},image:{type:String,required:!1,description:"分享图片的URL地址"}},payload:{title:String,content:String,url:String,pcUrl:String,image:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.6.110",mac:">6.6.202"}},{title:"上传文件",name:"utils.uploadFile",description:"使用multipart/form-data方式上传到第三方服务",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.uploadFile',\n    meta: {},\n    payload: {\n      url: 'https://example.lanxin.cn/upload',\n      filePath: tempFilePaths[0],\n      localId: 'xxxxx',\n      name: 'file',\n      headers: {},\n      formData:{\n        'user': 'test'\n      }\n    }\n  }",payload:{url:{type:String,required:!0,description:"开发者需要上传的自己服务器地址"},localId:{type:String,required:!0,description:"要上传文件的本地ID"},name:{type:String,required:!0,description:"文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容"},headers:{type:Object,required:!1,description:"HTTP 请求 Header, header 中不能设置 Referer"},formData:{type:Object,required:!1,description:"HTTP 请求中其他额外的 form data，KV键值对"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      responseBody: '',\n      statusCode: 200\n    }\n  }",response:{responseBody:{type:String,description:"HTTP 请求返回体"},statusCode:{type:String,description:"状态码"}},errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"上传文件至S3",name:"utils.uploadFileToS3",description:"文件上传至S3存储服务",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.uploadFileToS3',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      endpoint: '',\n      accessKey: '',\n      secretKey: '',\n      sessionToken: '',\n      region: '',\n      bucket: '',\n      object: ''\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"本地文件ID"},endpoint:{type:String,required:!0,description:"S3的接入点Url"},accessKey:{type:String,required:!0,description:"S3 accessKey"},secretKey:{type:String,required:!0,description:"S3 secretKey"},sessionToken:{type:String,required:!0,description:"S3 sessionToken"},region:{type:String,required:!0,description:"S3 region"},bucket:{type:String,required:!0,description:"S3 bucket"},object:{type:String,required:!0,description:"S3 object"}},payload:{localId:{type:String,required:!0},endpoint:String,accessKey:String,secretKey:String,sessionToken:String,region:String,bucket:String,object:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXIST",description:"上传文件丢失"},31:{message:"UPLOAD_FAILED",description:"上传失败"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"分块上传S3",name:"utils.uploadFileToS3InChunks",description:"分片上传S3，断点续传",requestExample:"\n  {\n    api: 'utils.uploadFileToS3InChunks',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      endpoint: '',\n      accessKey: '',\n      secretKey: '',\n      sessionToken: '',\n      region: '',\n      bucket: '',\n      object: '',\n      chunkSize: 1024,\n      maxChunkSize: 2048,\n      maxChunkNumber: 20\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"本地文件ID"},endpoint:{type:String,required:!0,description:"S3的接入点Url"},accessKey:{type:String,required:!0,description:"S3 accessKey"},secretKey:{type:String,required:!0,description:"S3 secretKey"},sessionToken:{type:String,required:!0,description:"S3 sessionToken"},region:{type:String,required:!0,description:"S3 region"},bucket:{type:String,required:!0,description:"S3 bucket"},object:{type:String,required:!0,description:"S3 object"},chunkSize:{type:Number,required:!1,description:"期望的每片上传的字节大小,单位byte"},maxChunkSize:{type:Number,required:!1,description:"每片允许上传的最大限制,单位byte"},maxChunkNumber:{type:Number,required:!1,description:"上传允许的最大分片数量"}},payload:{localId:{type:String,required:!0},endpoint:String,accessKey:String,secretKey:String,sessionToken:String,region:String,bucket:String,object:String,chunkSize:Number,maxChunkSize:Number,maxChunkNumber:Number},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"上传失败"},"-8":{message:"-",description:"访问的文件不存在"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"下载文件",name:"utils.downloadFile",description:"将文件下载到本地",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: utils.downloadFile,\n    meta: {},\n    payload: {\n      url: 'https://example.lanxin.cn/lanxin.png',\n      location: 'sandbox'\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件的url地址"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"文件本地id"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"文件预览",name:"utils.previewFile",description:"文件预览",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.previewFile',\n    meta: {},\n    payload: {\n      url: '524288-45644556456',\n      name: 'xxxx.doc',\n      size: 1212122,\n      type: 'pdf',\n      moreHidden: false\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件URL，或openMediaId，或文件选择/下载后的localId"},name:{type:String,required:!1,description:"需要预览文件的文件名(不填的话取url的最后部分)"},size:{type:Number,required:!1,description:"需要预览文件的大小，单位 B"},type:{type:String,required:!1,description:"需要预览文件的类型"},moreHidden:{type:Boolean,required:!1,default:!1,description:"是否隐藏文件预览的右上角更多按钮"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXIST",description:"本地资源文件不存在"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"服务器内部错误(OpenId兑换失败，不是有效的OpenId)"},51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"保存文件",name:"utils.saveFile",description:"保存文件到云盘/本地",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.saveFile',\n    meta: {},\n    payload: {\n      url: 'xxxxxx',\n      target: ['local', 'cloud']\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件URL，或文件选择/下载后的localId"},target:{type:Array,default:["local","cloud"],description:"文件保存位置，local 本地，cloud 云盘，默认为两者都可选"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"签批",name:"utils.signFile",description:"wps签批本地文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: utils.signFile,\n    meta: {},\n    payload: {\n      platform: 'OFD',\n      url: 'local://xxxx',\n      mode: 'viewer',\n      permissions: ['read','review'],\n      area: 'default'\n    }\n  }",payload:{platform:{type:String,required:!0,description:"使用的签批文件，支持OFD"},url:{type:String,required:!0,description:"文件的localId, 仅支持ofd格式"},mode:{type:String,required:!0,description:"进入的模式，支持viewer、editor"},permissions:{type:Array,required:!0,description:"允许的权限[read、review、approve]"},cover:{type:Boolean,required:!1,default:!0},zoomPageNumber:{type:Number,required:!1},zoomRect:{type:Array,required:!1}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"完成签批后的本地文件id"}},errorCode:{90:{message:"UNSUPPORT FORMAT TYPES",description:"不支持的文件格式"},91:{message:"INVALID PERMISSION",description:"无效的权限或权限不合法"}},type:"call",version:{ios:">7.29.30",android:">=7.11.15",windows:void 0,mac:void 0,linux:void 0}},{title:"监听文件上传进度",name:"utils.onUploadProgress",description:"监听文件上传进度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.onUploadProgress',\n    meta: {},\n    payload: {\n      localId: 'xxxxx',\n      total: 10000,\n      loaded: 1000,\n    }\n  }",docPayload:{localId:{type:String,description:"文件下载后的本地ID，用于文件预览，音频播放，图片查看等"},total:{type:Number,description:"文件总大小，单位 B"},loaded:{type:Number,description:"已上传大小，单位 B"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n        localId: 'xxxxx',\n        total: 10000,\n        loaded: 1000,\n    }\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"监听文件下载进度",name:"utils.onDownloadProgress",description:"监听文件下载进度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.onUploadProgress',\n    meta: {},\n    payload: {\n      url: 'xxxxx',\n      total: 10000,\n      loaded: 1000,\n    }\n  }",docPayload:{url:{type:String,description:"文件下载的url"},total:{type:Number,description:"文件总大小，单位 B"},loaded:{type:Number,description:"已上传大小，单位 B"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{81:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}}],ae=[{title:"获取用户token",name:"internal.biz.getVisitorToken",description:"获取端内蓝信用户Token",deploy:[{isCustomized:!0,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'internal.biz.getVisitorToken',\n    meta: {},\n    payload: {\n      timestamp: '1231123',\n      nonceStr: 'qweqweqwe',\n      signature: 'qweqweqweqweqweqe'\n    }\n  }",payload:{timestamp:{type:String,required:!1,description:"生成签名的时间戳(单位: 秒)"},nonceStr:{type:String,required:!1,description:"生成签名的随机串"},signature:{type:String,required:!1,description:"签名"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      deviceId：'',\n      center: {\n        userId: 'abc123', // 字符串 认证center服务返回的userid\n        token: 'asdasd', // 字符串 centertoken的value\n        domain: '' // 字符串目前是lxcenter\n      },\n      org: {\n        userId: {\n            oid: 123, // 整型\n            uid: 1231 // 整型\n        },\n        token: '', // 字符串 组织token的value\n        domain: '' // 字符串：lx.org.{组织id}\n      }\n    }\n  }",response:{deviceId:{type:String,description:"给webview生成token时生成的device_id"},center:{type:Object,description:"center相关信息"},org:{type:Object,description:"org相关信息"}},errorCode:{11:{message:"-",description:"保留"}},type:"call"},{title:"获取平台接入点",name:"internal.biz.getPlatformEndPoint",description:"获取当前组织接入点信息",deploy:[{isCustomized:!0,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'internal.biz.getPlatformEndPoint',\n    meta: {},\n    payload: {}\n  }",responseExample:'\n  {\n    status: {\n      code: 0,\n      message: \'OK\'\n    },\n    meta: {},\n    data: {\n      webEdge:[{host:"xxxx",port:"xxxx"},{host:"xxxx",port:"xxxx"}]\n      webEdges:[{host:"xxxx",port:"xxxx"}]\n    }\n  }',response:{webEdge:{type:Array,description:"websocket_endpoints"},webEdges:{type:Array,description:"websockets_endpoints"},host:{type:String,description:"接入点的host"},port:{type:String,description:"接入点的端口"}},errorCode:{161:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.27.30",android:">=7.27.30",windows:void 0,mac:void 0}},{title:"接收朗读状态",name:"internal.media.onReadAloudReceive",description:"内部jssdk接口，和media.onReadAloudReceive[Native -> Js]功能一致，不需要config",type:"listen",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"开始朗读文本",name:"internal.media.readAloudStart",description:"内部jssdk接口，和media.onReadAloudReceive[Native -> Js]功能一致，不需要config",payload:{content:{type:String,required:!0},sceneId:{type:Number}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"停止朗读文本",name:"internal.media.readAloudStop",description:"内部jssdk接口，和media.readAloudStart功能一致，不需要config",type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"向go发送数据",name:"internal.go.sendData",description:"JS调用GO的通用内部接口, 通过传入协议好的PB数据，进行方法调用或者注册notify",deploy:[{isCustomized:!0,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'internal.go.sendData',\n    meta: {},\n    payload: {\n      data: String,\n    }\n  }",payload:{data:{type:String,required:!0,description:"key为base64编码的pb数据"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{81:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:">=7.30.30",mac:">=7.30.30"}},{title:"监听go数据返回",name:"internal.go.onReceiveData",description:"GO调用JS的接口，响应JS的方法调用或主动发notify给JS， 返回协议好的PB数据，通过解析数据做不同的动作",deploy:[{isCustomized:!0,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'internal.go.onReceiveData',\n    meta: {},\n    payload: {\n      data: String,\n    }\n  }",docPayload:{data:{type:String,required:!0,description:"base64编码的protoBuf数据"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"notify",version:{ios:">=7.29.30",android:">=7.29.30",windows:">=7.30.30",mac:">=7.30.30"}},{title:"拉取驾驶舱数据",name:"internal.biz.fetchCockpitData",description:"根据所传入的key，拉取value的接口",deploy:[{isCustomized:!0,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'internal.biz.fetchCockpitData',\n    meta: {},\n    payload: {\n      api: '/lx.api.DataAccess/CreateData',\n      dataKey: '',\n    }\n  }",payload:{api:{api:String,required:!0,description:'接口<br />Create = "/lx.api.DataAccess/CreateData"<br />Creates = "/lx.api.DataAccess/CreateDatas"<br />Modify = "/lx.api.DataAccess/ModifyData"<br />Modifys = "/lx.api.DataAccess/ModifyDatas"<br />Fetch = "/lx.api.DataAccess/FetchData"<br />Fetchs = "/lx.api.DataAccess/FetchDatas"'},dataKey:{type:String,required:!0,description:"所需数据的key，key为base64编码的数据，解码后为protoBuf数据"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      dataValue: ''\n    }\n  }",response:{dataValue:{type:String,description:"key所对应的value数据，value为base64编码的string, 解码后为protoBuf数据"}},errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.27.30",android:">=7.27.30",windows:void 0,mac:void 0}}],se=ae||ae,de=[{title:"获取缓存",name:"storage.get",description:"获取应用缓存的数据，以key为标示获取",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.get',\n    meta: {},\n    payload: {\n      cacheKey:String\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"缓存数据对应的key"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      cacheValue: ''\n    }\n  }",response:{cacheValue:{type:String,description:"缓存的value数据"}},errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},41:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"设置缓存",name:"storage.set",description:"应用缓存动态数据，使⽤key-value的形式存储，key为字符串，value为对应的数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.set',\n    meta: {},\n    payload: {\n      cacheKey: '',\n      cacheValue: ''\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"要保存的key"},cacheValue:{type:String,required:!0,description:"要缓存的value数据"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"移除缓存",name:"storage.remove",description:"移除应用缓存动态数据，以key为标示移除对应缓存",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.remove',\n    meta: {},\n    payload: {\n      cacheKey: 'key1'\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"缓存的key"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},51:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"清空缓存",name:"storage.clear",description:"删除应用缓存的全部数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.clear',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"批量获取缓存数据",name:"storage.batchGet",description:"批量获取应用缓存的数据，以key为标示获取",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.batchGet',\n    meta: {},\n    payload: {\n      cacheKeys: [cacheKey1, cacheKey2, cacheKey3]\n    }\n  }",payload:{cacheKeys:{type:Array,required:!0,description:"缓存数据对应的key的数组, cacheKey为String类型"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      cacheValues: {\n        cacheKey1: cacheValue1,\n        cacheKey2: cacheValue2,\n        cacheKey3: cacheValue3\n      }\n    }\n  }",response:{cacheKeys:{type:Array,required:!0,description:"缓存数据对应的key的数组, cacheKey为String类型"}},errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},11:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"批量设置缓存",name:"storage.batchSet",description:"批量应用缓存动态数据，使⽤key-value的形式存储，key为字符串，value为对应的数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.set',\n    meta: {},\n    payload: {\n      cache: {\n        cacheKey1: cacheValue1,\n        cacheKey2: cacheValue2,\n        cacheKey3: cacheValue3\n      }\n    }\n  }",payload:{cache:{type:Object,required:!0,description:"要缓存的cache对象, 以key-value的形式, key, value均为String"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      // 成功, 返回空对象\n      // 如有失败, 返回缓存失败key的数组\n      errorResult: [cacheKey1, cacheKey2]\n    }\n  }",response:{errorResult:{type:Array,description:"缓存失败key的数组"}},errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}}],pe=[{title:"发起音视频会议",name:"open.conference.create",description:"拨打音视频会议和一对一通话，一对一通话时仅支持type和staffs参数",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.create',\n    meta: {},\n    payload: {\n      type: 'voice',\n      staffs: [],\n      title: '蓝信会议',\n      startTime: 1664582400000\n    }\n  }",payload:{initType:{type:String,required:!0,description:"会议初始类型 <br /> call - 1v1通话; meeting - 会议（视频会议）"},voiceVideoType:{type:String,required:!1,default:"voice",description:"通话类型 <br /> voice - 语音通话; video - 视频通话 <br /> 注：initType为meeting时，该参数不生效"},staffs:{type:Array,required:!0,description:"呼叫的对方人员openStaffId组成的数组。 <br /> 注：initType为call时，该参数仅接收一人"},title:{type:String,required:!1,description:"会议标题，长度限制小于等于200 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},startTime:{type:Number,default:0,description:"开始时间，立即开始为0，需要预约为开始时间的时间戳<br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},needRecord:{type:Boolean,default:!1,description:"是否开启会议录像 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},memberJoinMute:{type:Boolean,default:!1,description:"参会人入会时是否静音 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},memberJoinPassword:{type:Number,required:!1,validator:function(e){return 6==="".concat(e).length},description:"参会人入会密码，6位数字，不需要密码时传空 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},hostPassword:{type:Number,required:!1,validator:function(e){return 6==="".concat(e).length},description:"主持密码，6位数字，不需要密码时传空 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},allowAdvenceJoin:{type:Boolean,default:!1,description:"允许参会人提前入会，立即开始的会议不支持此参数 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{meetingNumber:{type:String,description:"会议号"},conferenceId:{type:Number,description:"会议id，取消会议时需要的必传参数"}},errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},251:{message:"NOT_SUPPORT_VOICE_VIDEO",description:"当前组织不支持音视频功能（没有视频会议应用）"},252:{message:"NO_PERMISSION",description:"无权限或者方数"},253:{message:"OVER_LIMIT",description:"当前组织超出方数"},254:{message:"BUSY_LINE",description:"其他视频会议，视频通话，语音通话或直播进行中"},255:{message:"NOT_SUPPORT_MULTIPLE",description:"不支持多人通话，例如：initType为call，staffs超过一人"},256:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持，XP不支持此功能"},257:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title长度超出限制"},258:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"}},type:"call",version:{ios:">=8.3.0",android:">=8.3.0",windows:">=7.33.30",mac:">=7.33.30"}},{title:"加入会议",name:"open.conference.join",description:"加入会议",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.join',\n    meta: {},\n    payload: {\n      meetingNumber: ''\n    }\n  }",payload:{meetingNumber:{type:String,required:!0,description:"需要加入的会议号"},videoOpen:{type:Boolean,required:!1,default:!0,description:"是否开启摄像头"},audioOpen:{type:Boolean,required:!1,default:!0,description:"是否开启麦克风"},password:{type:String,required:!1,description:"入会密码，创建会议时设置了密码的情况下必填。如果加入会议的是主持人则为主持密码，如果加入会议的是参会人则为参会人入会密码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},"-8":{message:"NOT_EXIST",description:"会议号不存在或密码错误"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"取消会议预约",name:"open.conference.cancel",description:"取消预约的会议",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.cancel',\n    meta: {},\n    payload: {\n      conferenceId: ''\n    }\n  }",payload:{conferenceId:{type:Number,required:!0,description:"会议id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},"-8":{message:"NOT_EXIST",description:"会议号不存在，会议号错误或者会议已开始"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"发起直播",name:"open.living.create",description:"发起直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.create',\n    meta: {},\n    payload: {\n      startTime: '0',\n      title: '蓝信发起的直播'\n    }\n  }",payload:{title:{type:String,required:!0,description:"直播标题，长度限制小于等于32"},startTime:{type:Number,required:!1,default:0,description:"开始时间，立即开始为0，需要预约为开始时间的时间戳"},permission:{type:String,default:"all",description:"可观看人员<br /> all - 全部 <br />remindStaffs - 提醒的人员"},remindStaffs:{type:Array,required:!1,description:"开播提醒的人员 <br />permission 为remind时必传，为all时可选"},remindContent:{type:String,required:!1,description:"开播提醒的内容，remind为true时该参数生效，长度限制小于等于100"},needReplay:{type:Boolean,required:!1,default:!0,description:"是否生成直播回放"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      livingId: ''\n    }\n  }",response:{livingId:{type:String,description:"直播id"}},errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"},11:{message:"REMIND_STAFFS_ILLEGAL",description:"提醒人员不合法，例如remindStaffs中有不可观看直播人员"},12:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title或者remindContent长度超出限制"},13:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"观看直播",name:"open.living.watch",description:"观看直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.watch',\n    meta: {},\n    payload: {\n      livingId: '123'\n    }\n  }",payload:{livingId:{type:String,required:!0,description:"直播id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      id: ''\n    }\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"取消直播预约",name:"open.living.cancel",description:"取消预约的直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.cancel',\n    meta: {},\n    payload: {\n      livingId: ''\n    }\n  }",payload:{livingId:{type:String,required:!0,description:"需要取消的直播的id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"},"-8":{message:"NOT_EXIST",description:"直播id不存在"},12:{message:"STATUS_CHANGE",description:"直播状态改变，例如预约的直播已经开播"}},type:"call",version:{ios:">=8.3.0",android:">=8.3.0",windows:">=7.33.30",mac:">=7.33.30"}}],ce=[].concat(u([{title:"连接低功耗蓝牙",name:"device.createBLEConnection",description:"连接低功耗蓝牙",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.createBLEConnection',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"断开低功耗蓝牙连接",name:"device.closeBLEConnection",description:"断开低功耗蓝牙连接",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.closeBLEConnection',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"根据uuid获取已连接状态的设备",name:"device.getConnectedBluetoothDevices",description:"根据uuid获取已连接状态的设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getConnectedBluetoothDevices',\n    meta: {},\n    payload: {\n      services: []\n    }\n  }",payload:{services:{type:Array,description:"蓝牙设备主 service 的 uuid 列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{devices:{type:Array,description:"设备列表"}},responseExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"]]},errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"监听低功耗蓝牙状态的改变",name:"device.onBLEConnectStateChange",description:"监听低功耗蓝牙状态的改变",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBLEConnectStateChange',\n    meta: {},\n    payload: {\n      deviceId: ''\n      connected: true\n    }\n  }",docPayload:{deviceId:{type:String,description:"蓝牙设备id"},connected:{type:Boolean,description:"连接目前的状态"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"listen",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"获取蓝牙设备所有service",name:"device.getBLEDeviceService",description:"获取蓝牙设备所有service",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBLEDeviceService',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      services: []\n    }\n  }",response:{services:{type:Array,description:"设备服务列表"}},responseExtra:{title:"service 数据结构",headers:["字段","类型","说明"],rows:[["uuid","String","蓝牙设备服务的 uuid"],["isPrimary","Boolean","该服务是否为主服务"]]},errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"获取蓝牙设备某个服务中的所有 characteristic（特征值）",name:"device.getBLEDeviceCharacteristics",description:"获取蓝牙设备某个服务中的所有 characteristic（特征值）",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBLEDeviceCharacteristics',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      characteristics: ''\n    }\n  }",response:{characteristics:{type:Array,description:"设备特征值列表"}},responseExtra:{title:"characteristics 数据结构",headers:["字段","类型","说明"],rows:[["characteristicId","String","蓝牙设备特征值的uuid"],["serviceId","String","蓝牙设备特征值对应服务的 uuid"],["value","String","蓝牙设备特征值对应的十六进制值(hexString)"],["properties","Object","该特征值支持的操作类型"]]},errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"读取低功耗蓝牙设备的特征值的二进制数据值",name:"device.readBLECharacteristicValue",description:"读取低功耗蓝牙设备的特征值的二进制数据值",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.readBLECharacteristicValue',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"},characteristicId:{type:String,required:!0,description:"蓝牙特征值的 uuid"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      value: ''\n    }\n  }",response:{value:{type:String,description:"蓝牙设备的十六进制特征值(hexString)"}},errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"写入低功耗蓝牙设备的特征值的二进制数据值",name:"device.writeBLECharacteristicValue",description:"写入低功耗蓝牙设备的特征值的二进制数据值，需要设备支持写入",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.writeBLECharacteristicValue',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n      characteristicId: '',\n      value: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"},characteristicId:{type:String,required:!0,description:"蓝牙特征值的 uuid"},value:{type:String,required:!0,description:"蓝牙设备的十六进制特征值(hexString)"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"获取网络类型",name:"device.getNetworkType",description:"获取设备网络类型",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getNetworkType',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      networkType: 'wifi'\n    }\n  }",response:{networkType:{type:String,description:"wifi - wifi网络<br />2g - 2g网络<br />3g - 3g网络<br />4g - 4g网络<br />none - 无网络<br />unknown - 不常见网络类型"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取网络接入信息",name:"device.getNetworkInterface",description:"获取设备网络(热点)接入信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getNetworkInterface',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      ssid: '',\n      macAddress: '',\n      lac: '',\n      cid: ''\n    }\n  }",response:{ssid:{type:String,description:"当前网络接入点名称"},macAddress:{type:String,description:"当前网络接入点mac地址"},lac:{type:String,description:"当前设备所连网络基站的小区号",version:{android:">=8.0.0",ios:">=8.0.0"}},cid:{type:String,description:"当前设备所连网络基站的基站号",version:{android:">=8.0.0",ios:">=8.0.0"}}},errorCode:{"-8":{message:"NOT_EXIST",description:"当前手机连接的既不是WIFI也不是热点"},"-4":{message:"NO_PERMISSON",description:"获取wifi信息需要定位权限，无位置权限"},"-2":{message:"NO_SUPPORT",description:"暂不支持5G基站信息获取"},21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取地理位置",name:"device.getLocation",description:"获取设备地理位置信息",requestExample:"\n  {\n    api: 'device.getLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:"坐标<br />wgs84 - gps坐标<br />gcj02 - 火星坐标"}},responseExample:"\n  {\n    {\n      status: {\n        code: 0,\n        message: 'OK'\n      },\n      meta: {},\n      data: {\n        latitude: '',\n        longitude: '',\n        speed: '',\n        accuracy: '',\n        address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座',\n        province: '北京',\n        city: '北京',\n        district: '朝阳区',\n        road: '电子城国际电子总部A座'\n      }\n    }\n  }",response:{latitude:{type:Number,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,description:"经度，浮点数，范围为-180~180，负数表示西经"},speed:{type:Number,description:"速度，浮点数，单位m/s"},accuracy:{type:Number,description:"位置的精确度"},address:{type:String,description:"格式化地址，如：北京市朝阳区酒仙桥6号电子城国际电子总部A座"}},errorCode:{31:{message:"-",description:"保留"},32:{message:"定位失败，请稍后重试"},33:{message:"定位失败，请检查网络"},34:{message:"定位失败，服务受限"},35:{message:"定位失败，请开启系统权限"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取设备信息",name:"device.getSystemInfo",description:"获取设备系统信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getSystemInfo',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      isSystemOverrided: true,\n      isAndroidDeveloperMode: true,\n      installedAndroidApps: [],\n      systemType: 'iOS',\n      systemVersion: '14.2.1'\n      deviceHeight: ''\n      deviceBrand: 'Apple',\n      deviceModel: '',\n      deviceID: '',\n      deviceWidth: '',\n    }\n  }",response:{isSystemOverrided:{type:Boolean,description:"Android是否被Root,IOS是否被越狱",version:{mac:void 0,windows:void 0,linux:void 0}},isAndroidDeveloperMode:{type:Boolean,description:"Android 开发者模式是否被开启",version:{ios:void 0,mac:void 0,windows:void 0,linux:void 0}},installedAndroidApps:{type:Array,description:"Android 安装的App列表",version:{ios:void 0,mac:void 0,windows:void 0,linux:void 0}},systemType:{type:String,description:"系统类型<br />ios android mac windows"},deviceHeight:{type:String,description:"屏幕高度"},deviceWidth:{type:String,description:"屏幕宽度"},windowWidth:{type:String,description:"可使用窗口宽度",version:{mac:void 0,windows:void 0,linux:void 0}},windowHeight:{type:String,description:"可使用窗口高度",version:{mac:void 0,windows:void 0,linux:void 0}},safeArea:{type:Object,description:"屏幕下方安全区域",version:{mac:void 0,windows:void 0,linux:void 0}},navigationBarSafeArea:{type:Object,description:"屏幕上方安全区域",version:{mac:void 0,windows:void 0,linux:void 0}},webviewVersion:{type:String,description:"webview 版本号"},deviceID:{type:String,description:"设备id，Android卸载安装会清除",version:{mac:void 0,windows:void 0,linux:void 0}},deviceModel:{type:String,description:"设备型号，mac和windows没有时返回PC"},deviceBrand:{type:String,description:"设备品牌，mac和windows没有时返回PC"}},errorCode:{71:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.11.30",android:">=7.11.30",windows:">7.30.30",mac:">7.30.30"}},{title:"选择地理位置",name:"device.chooseLocation",description:"选择地理位置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.chooseLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      poiKeyWords: ['美食','医疗']\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:""},poiKeyWords:{type:Array,required:!1,description:'附近的兴趣点默认搜索词为: ["美食","宾馆","购物","生活服务","金融","旅游景点","教育","政府机构","公司企业","医疗"]'}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      latitude: '',\n      longitude: '',\n      address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座',\n      province: '北京',\n      city: '北京',\n      district: '朝阳区',\n      road: '869国际电子总部A座',\n      street: '酒仙桥中路',\n      accuracy: 10,\n      speed: 1,\n    }\n  }",response:{latitude:{type:Number,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,description:"经度，浮点数，范围为-180~180，负数表示西经"},address:{type:String,description:"格式化地址，如：北京市朝阳区酒仙桥6号电子城国际电子总部A座"},provide:{type:String,description:"省份，如：北京市"},city:{type:String,description:"城市，如：北京市"},district:{type:String,description:"行政区，如：朝阳区"},road:{type:String,description:"街道，如：869国际电子总部A座"},street:{type:String,description:"iOS端：街道，如：酒仙桥中路 "},accuracy:{type:String,description:"iOS端：实际的定位精度半径"},speed:{type:Number,description:"iOS端：速度"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"打开地理位置",name:"device.displayLocation",description:"使用内置地图打开地理位置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.chooseLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      latitude: 116.497154,\n      longitude: 39.989328,\n      name: '360企业安全'，\n      address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座'\n    }\n  }",docPayload:{coordinateType:{type:String,default:"wgs84",description:'默认为"wgs84"代表gps坐标<br />可传入"gcj02"代表火星坐标'},latitude:{type:Number,required:!0,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,required:!0,description:"经度，浮点数，范围为-180~180，负数表示西经"},name:{type:String,required:!1,description:"用于显示地图位置名称信息，不填则显示当前位置在地图上的位置"},address:{type:String,required:!1,description:"用于显示地图位置详细地址信息，不填则显示当前位置在地图上的位置详细信息"}},payload:{coordinateType:{type:String,default:"wgs84"},latitude:Number,longitude:Number,name:String,address:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"扫一扫",name:"device.scanCode",description:"调起客户端扫码界面进行扫码",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.scanCode',\n    meta: {},\n    payload: {\n      type: ['qrCode', 'barCode'],\n    }\n  }",payload:{type:{type:Array,required:!1,default:["qrCode","barCode"],description:"指定扫二维码还是一维码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: '121212121212'\n    }\n  }",response:{result:{type:String,description:"扫码结果"}},errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.15",android:">6.6.15",windows:void 0,mac:void 0}},{title:"开启持续定位",name:"device.geoLocationStart",description:"开启持续定位",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStart',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      targetAccuracy: 200\n      scanSpan: 1000,\n      iOSDistanceFilter: 200\n      useCache: true,\n      withReGeocode: false,\n      callBackInterval: 1000\n      sceneId: '123',\n      uploadWebService: true,\n      authDescribe: ''\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:'默认为"wgs84"代表gps坐标，可传入"gcj02"代表火星坐标'},targetAccuracy:{type:Number,required:!1,description:"iOS 参数（iOS必传）：期望定位精度半径(单位米)定位结果尽量满足该参数要求，不保证小于该误差，开发者需要读取返回结果的 accuracy 字段校验坐标精度。建议按照业务需求设置定位精度，推荐采用200m，可获得较好的精度和较短的响应时长。",version:{android:void 0}},iOSDistanceFilter:{type:Number,required:!1,description:"iOS 参数（iOS必传）：位置变更敏感度，单位为m，此值会影响端callback回调速率。",version:{android:void 0}},useCache:{type:Boolean,required:!1,description:"Android 参数： 是否使用缓存的地理位置信息。<br />true（默认）：如果定位失败，返回缓存的地理位置信息。<br />false：返回实时的地理位置信息，如果定位失败，返回nil",version:{ios:void 0}},withReGeocode:{type:Boolean,required:!1,default:!1,description:"是否需要带有逆地理编码信息。该功能需要网络请求，请根据自己的业务场景使用。"},callBackInterval:{type:Number,required:!1,description:"数据回传最小时间间隔，单位ms。"},sceneId:{type:String,required:!0,description:"定位场景id。对于同一id，不可连续start，否则会报错。不同scenceId互不影响"},uploadWebService:{type:Boolean,required:!1,default:!1,description:"退出应用后，是否要继续上传位置信息到webService服务器，需要时传true"},authDescribe:{type:String,required:!1,description:"获取持续定位的授权描述，即使用持续定位将要做什么，当uploadWebService=true时，必传"},scanSpan:{type:[Number,String],required:!1,description:"Android 参数（Android必传）：设置的扫描间隔，单位是ms",version:{ios:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sceneId: ''\n    }\n  }",response:{sceneId:{type:String,description:"定位场景id。对于同一id，不可连续start，否则会报错。不同scenceId互不影响"}},errorCode:{"-11":{message:"持续定位应用数量达到上限",description:"应用数量达到上限 3个"},35:{message:"用户未授权",description:"用户未授权使用"},81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"停止持续定位",name:"device.geoLocationStop",description:"停止持续定位",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStop',\n    meta: {},\n    payload: {\n      sceneId: String,\n      useUploadWebService: Boolean\n    }\n  }",payload:{sceneId:{type:String,required:!1,description:"需要停止定位场景id"},useUploadWebService:{type:Boolean,required:!1,description:"在开始定位时，是否开启了退出应用后继续上传位置信息到webService的能力，如果开启了传true"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sceneId: String\n    }\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"获取持续定位状态",name:"device.geoLocationStatus",description:"获取批量连续定位状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStatus',\n    meta: {},\n    payload: {\n      sceneIds: Array,\n    }\n  }",payload:{sceneIds:{type:Array,required:!1,description:"需要查询定位场景id列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      0001:1   //0001是场景ID， 1:持续定位的状态\n      0002:0   //0002是场景ID， 0:持续定位的状态\n    }\n  }",response:{key:{type:String,description:"持续定位的场景ID"},value:{type:Number,description:"持续定位的状态<br />1 - 表示正在持续定位中<br /> 0 - 表示未持续定位"}},errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"接收持续定位的位置信息",name:"device.onGeoLocationReceive",description:"接收持续定位的位置信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onGeoLocationReceive',\n    meta: {},\n    payload: {\n      longitude: 123,\n      latitude: 123,\n      accuracy: 123,\n      address: '',\n      province: '',\n      city: '',\n      district: '',\n      road: '',\n      street: '',\n      speed: 123,\n      sceneId: '123'\n    }\n  }",docPayload:{longitude:{type:Number,required:!1,description:"经度"},latitude:{type:Number,required:!1,description:"纬度"},accuracy:{type:Number,required:!1,description:"实际的定位精度半径 (单位米)"},address:{type:String,required:!1,description:"格式化地址"},province:{type:String,required:!1,description:"省份"},city:{type:String,required:!1,description:"城市"},district:{type:String,required:!1,description:"行政区"},speed:{type:Number,required:!1,description:"速度"},road:{type:String,required:!1,description:"街道"},street:{type:String,required:!1,description:"街道"},sceneId:{type:String,required:!0,description:"定位场景id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"获取健康数据",name:"device.getHealthInfo",description:"获取当天健康数据（仅支持”步数“）",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:'\n  {\n    api: \'device.getHealthInfo\',\n    meta: {},\n    payload: {\n      beginTime:Number,\n      endTime:Number,\n      type:["stepCount"],\n      authDescription:""\n    }\n  }',payload:{beginTime:{type:Number,required:!1,description:"采集数据起始时间（1970年起始），单位：秒；不传采集当天数据"},endTime:{type:Number,required:!1,description:"采集数据结束时间（1970年起始），单位：秒；不传采集当天数据"},type:{type:Array,required:!0,description:"健康数据类型<br />stepCount - 步数"},authDescription:{type:String,required:!1,description:"蓝信授权描述，默认：允许应用获取你的[应用名称]运动数据，该功能需允许[应用名称]读取系统“健康”应用中的步数数据。"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      stepCount: Number,\n    }\n  }",response:{stepCount:{type:Number,description:"健康数据<br />stepCount - 行走的步数，单位：步；"}},errorCode:{121:{message:"-",description:"保留"},"-10":{message:"当前设备不支持获取健康数据",description:"设备不支持"},"-11":{message:"用户未授权使用",description:"用户拒绝授权ISV应用访问[蓝信]"},"-12":{message:"用户未授权使用",description:"用户拒绝授权[蓝信]访问系统“健康”信息"},"-13":{message:"用户查看授权说明",description:"用户查看授权说明"},"-14":{message:"当前未采集到数据",description:"当前范围内没有采集到数据，或者用户未开启权限"}},type:"call",version:{ios:">=7.27.30",android:">=7.27.30",windows:void 0,mac:void 0}},{title:"改变wifi状态",name:"device.changeWifiStatus",description:"开启/关闭wifi开关",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.changeWifiStatus',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"监听wifi状态变化",name:"device.onWifiStatusChange",description:"监听wifi状态发生改变，开关开启、开关关闭、已连接、连接断开",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onWifiStatusChange',\n    meta: {},\n    payload: {\n      status：1\n    }\n  }",docPayload:{status:{type:Number,description:"wifi状态，ios只能监听到开启关闭<br />1 - 开启<br />2 - 关闭<br />3 - 已连接<br />4 - 已断开连接"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"获取wifi列表",name:"device.getWifiList",description:"获取wifi列表",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getWifiList',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      list: [{\n        SSID: '',\n        BSSID: '',\n        secure: true,\n        signalStrength: 1\n      }]\n    }\n  }",response:{list:{type:Array,description:"wifi列表"},SSID:{type:String,description:"wifi的bssid"},secure:{type:Boolean,description:"wifi是否安全"},signalStrength:{type:String,description:"wifi强度"}},errorCode:{151:{message:"-",description:"保留"}},type:"call",version:{ios:void 0,android:">=8.0.0",windows:void 0,mac:void 0}},{title:"连接wifi",name:"device.connectWifi",description:"连接指定wifi",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.connectWifi',\n    meta: {},\n    payload: {\n      SSID: '',\n      BSSID: '',\n      password: ''\n    }\n  }",payload:{SSID:{type:String,required:!0,description:"wifi的ssid"},BSSID:{type:String,required:!1,description:"wifi的bssid"},password:{type:String,default:"",description:"wifi密码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"获取wifi信息",name:"device.getWifiInfo",description:"获取当前连接wifi的信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.connectWifi',\n    meta: {},\n    payload: {}\n  }",response:{SSID:{type:String,description:"wifi的ssid"},BSSID:{type:String,description:"wifi的bssid"},secure:{type:Boolean,description:"wifi是否安全"},signalStrength:{type:Number,description:"wifi强度  0-4  4最强  0最弱"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      SSID: '',\n      BSSID: '',\n      secure: true,\n      signalStrength: 1\n    }\n  }",errorCode:{161:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"获取wifi状态",name:"device.getWifiStatus",description:"获取当前wifi状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getWifiStatus',\n    meta: {},\n    payload: {}\n  }",response:{status:{type:Number,description:"1 - 开启未连接<br />2 - 关闭<br />3 - 开启"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      status: 1\n    }\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"设置屏幕亮度",name:"device.setScreenBrightness",description:"设置屏幕亮度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setScreenBrightness',\n    meta: {},\n    payload: {\n      brightness: 0.5\n    }\n  }",payload:{brightness:{type:Number,required:!0,description:"屏幕亮度，0-1，0最暗，1最亮"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"设置屏幕常亮",name:"device.setKeepScreenOn",description:"设置屏幕常亮",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setKeepScreenOn',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"拨打本地电话",name:"device.makePhoneCall",description:"拨打本地电话",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.makePhoneCall',\n    meta: {},\n    payload: {\n      phoneNumber: ''\n    }\n  }",payload:{phoneNumber:{type:String,required:!0,description:"拨打的电话号码"},type:{type:String,required:!1,default:"person",description:"拨打电话类型，本人号码拨打还是企业号码拨打<br />person - 本人号码 company - 企业号码",version:{ios:">=8.3.0",android:">=8.3.0",windows:void 0,mac:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"读取NFC",name:"device.readNfc",description:"读取NFC芯片内容",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: device.readNfc,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: \"xxxx\",\n    }\n  }",response:{content:{type:String,description:"NFC芯片内容"}},errorCode:{221:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"NFC写入",name:"device.writeNfc",description:"NFC数据写入",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: device.writeNfc,\n    meta: {},\n    payload: {}\n  }",payload:{content:{type:String,required:!0,description:"NFC芯片内容"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: \"xxxx\",\n    }\n  }",response:{content:{type:String,description:"NFC芯片内容"}},errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"监听截屏",name:"device.onCaptureScreen",description:"监听截屏",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onCaptureScreen',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{241:{message:"-",description:"保留"}},type:"listen",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"初始化蓝牙模块",name:"device.openBluetoothAdapter",description:"初始化蓝牙模块",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.openBluetoothAdapter',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"关闭蓝牙模块",name:"device.closeBluetoothAdapter",description:"关闭蓝牙模块",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.closeBluetoothAdapter',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"监听蓝牙适配器状态变化",name:"device.onBluetoothAdapterStateChange",description:"监听蓝牙适配器状态变化",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBluetoothAdapterStateChange',\n    meta: {},\n    payload: {\n      discovering: true,\n      available: true\n    }\n  }",docPayload:{discovering:{type:Boolean,description:"是否正在搜索设备"},available:{type:Boolean,description:"蓝牙适配器是否可用"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"listen",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"获取蓝牙模块状态",name:"device.getBluetoothAdapterState",description:"获取蓝牙模块状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBluetoothAdapterState',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      discovering: false,\n      available: true\n    }\n  }",response:{discovering:{type:Boolean,description:"是否正在搜索设备"},available:{type:Boolean,description:"蓝牙适配器是否可用"}},errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"开始搜索附近的蓝牙设备",name:"device.startBluetoothDevicesDiscovery",description:"开始搜索附近的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.startBluetoothDevicesDiscovery',\n    meta: {},\n    payload: {\n      services: [],\n      allowDuplicatesKey: false,\n      interval: 0\n    }\n  }",payload:{services:{type:Array,required:!1,description:"要搜索的蓝牙设备主 service 的 uuid 列表。某些蓝牙设备会广播自己的主 service 的 uuid。如果设置此参数，则只搜索广播包有对应 uuid 的主服务的蓝牙设备。"},allowDuplicatesKey:{type:Boolean,required:!1,description:"是否允许重复上报同一设备。"},interval:{type:Number,required:!1,default:0,description:"上报设备的间隔，默认为0，意思是找到新设备立即上报。单位ms"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"停止搜索附近的蓝牙设备",name:"device.stopBluetoothDevicesDiscovery",description:"停止搜索附近的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.stopBluetoothDevicesDiscovery',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"获取已搜索到的蓝牙设备",name:"device.getBluetoothDevices",description:"获取已搜索到的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBluetoothDevices',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{devices:{type:Array,description:"设备列表"}},responseExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"],["RSSI","Number","当前蓝牙设备信号强度"],["advertisData","String","当前蓝牙设备的广播数据段中的ManufacturerData数据段的base64字符串"],["advertisServiceUUIDs","Array","当前蓝牙设备的广播数据段中的ServiceUUIDs数据段"],["localName","String","当前蓝牙设备的广播数据段中的LocalName数据段"],["serviceData","Object","key为serviceId，value为当前蓝牙设备的广播数据段中的ServiceData数据段的base64字符串"]]},errorCode:te,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"监听寻找新设备",name:"device.onBluetoothDeviceFound",description:"监听寻找新设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBluetoothDeviceFound',\n    meta: {},\n    payload: {\n      devices: []\n    }\n  }",docPayload:{devices:{type:Array,description:"新搜索到的设备列表"}},payloadExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"],["RSSI","Number","当前蓝牙设备信号强度"],["advertisData","ArrayBuffer","当前蓝牙设备的广播数据段中的ManufacturerData数据段"],["advertisServiceUUIDs","Array","当前蓝牙设备的广播数据段中的ServiceUUIDs数据段"],["localName","String","当前蓝牙设备的广播数据段中的LocalName数据段"],["serviceData","ArrayBuffer","当前蓝牙设备的广播数据段中的ServiceData数据段"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:te,type:"listen",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"震动",name:"device.setVibrate",description:"手机震动，可设置长震动和短震动",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setVibrate',\n    meta: {},\n    payload: {\n      duration: \"short\"\n    }\n  }",payload:{duration:{type:String,required:!0,description:"震动长短<br />short - 短震动；long - 长震动"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{281:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}}]),u(ne),u(ee),u(ie),u(re),u(oe),u(se),u(de),u(pe));var le=function(e){var n=typeof e;return null!=e&&("object"==n||"function"==n)};var ue=function(e){if(!le(e))return!1;var n=C(e);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n},me=n(t((function(e){e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=c(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var i=0,r=function(){};return{s:r,n:function(){return e.length>i?{done:!1,value:e[i++]}:{done:!0}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(s)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports})));var ye=localStorage.getItem("jssdk-debug"),ve=function(e){"ERROR"!==ye&&"INFO"!==ye||console.log("%c[jssdk error]:","color: Crimson",e)},ge=function(e,n){"INFO"===ye&&(e.includes("request")?console.log("%c[jssdk ".concat(e," ↑]"),"color: CornflowerBlue",n):console.log("%c[jssdk ".concat(e," ↓]"),"color: LimeGreen",n))},fe=[],xe={proxy:function(e,n){return ge("request","".concat(e.name,": ").concat(JSON.stringify(n))),new Promise((function(t,i){(function(e){if(!Array.isArray(e))throw new TypeError("Middleware stack must be an array!");var n,t=me(e);try{for(t.s();!(n=t.n()).done;)if("function"!=typeof n.value)throw new TypeError("Middleware must be composed of functions!")}catch(e){t.e(e)}finally{t.f()}return function(n,t){var i=-1;return function r(o){if(i>=o)return Promise.reject(Error("next() called multiple times"));i=o;var a=e[o];if(o===e.length&&(a=t),!a)return Promise.resolve();try{return Promise.resolve(a(n,(function(){return r(o+1)})))}catch(e){return Promise.reject(e)}}(0)}})(fe)({apiMeta:e,req:n}).then((function(n){if("call"===e.type){var r=n.res,o=r.status;r.callbackType===L&&(o&&0===o.code?t(r.data):i(o))}})).catch((function(n){i({message:n.message}),ve("".concat(e.name," : ").concat(n.message))}))}))},use:function(e){fe.push(e)}},we=t((function(e){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})),he=n(we),be=t((function(e){var n=we.default;function t(){e.exports=t=function(){return i},e.exports.__esModule=!0,e.exports.default=e.exports;var i={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(e,n,t){e[n]=t.value},s="function"==typeof Symbol?Symbol:{},d=s.iterator||"@@iterator",p=s.asyncIterator||"@@asyncIterator",c=s.toStringTag||"@@toStringTag";function l(e,n,t){return Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}),e[n]}try{l({},"")}catch(e){l=function(e,n,t){return e[n]=t}}function u(e,n,t,i){var r=Object.create((n&&n.prototype instanceof v?n:v).prototype),o=new _(i||[]);return a(r,"_invoke",{value:q(e,t,o)}),r}function m(e,n,t){try{return{type:"normal",arg:e.call(n,t)}}catch(e){return{type:"throw",arg:e}}}i.wrap=u;var y={};function v(){}function g(){}function f(){}var x={};l(x,d,(function(){return this}));var w=Object.getPrototypeOf,h=w&&w(w(A([])));h&&h!==r&&o.call(h,d)&&(x=h);var b=f.prototype=v.prototype=Object.create(x);function S(e){["next","throw","return"].forEach((function(n){l(e,n,(function(e){return this._invoke(n,e)}))}))}function E(e,t){function i(r,a,s,d){var p=m(e[r],e,a);if("throw"!==p.type){var c=p.arg,l=c.value;return l&&"object"==n(l)&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){i("next",e,s,d)}),(function(e){i("throw",e,s,d)})):t.resolve(l).then((function(e){c.value=e,s(c)}),(function(e){return i("throw",e,s,d)}))}d(p.arg)}var r;a(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,r){i(e,n,t,r)}))}return r=r?r.then(o,o):o()}})}function q(e,n,t){var i="suspendedStart";return function(r,o){if("executing"===i)throw Error("Generator is already running");if("completed"===i){if("throw"===r)throw o;return N()}for(t.method=r,t.arg=o;;){var a=t.delegate;if(a){var s=C(a,t);if(s){if(s===y)continue;return s}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if("suspendedStart"===i)throw i="completed",t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);i="executing";var d=m(e,n,t);if("normal"===d.type){if(i=t.done?"completed":"suspendedYield",d.arg===y)continue;return{value:d.arg,done:t.done}}"throw"===d.type&&(i="completed",t.method="throw",t.arg=d.arg)}}}function C(e,n){var t=e.iterator[n.method];if(void 0===t){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=void 0,C(e,n),"throw"===n.method))return y;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var i=m(t,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var r=i.arg;return r?r.done?(n[e.resultName]=r.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=void 0),n.delegate=null,y):r:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function I(e){var n={tryLoc:e[0]};1 in e&&(n.catchLoc=e[1]),2 in e&&(n.finallyLoc=e[2],n.afterLoc=e[3]),this.tryEntries.push(n)}function O(e){var n=e.completion||{};n.type="normal",delete n.arg,e.completion=n}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function A(e){if(e){var n=e[d];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var t=-1,i=function n(){for(;++t<e.length;)if(o.call(e,t))return n.value=e[t],n.done=!1,n;return n.value=void 0,n.done=!0,n};return i.next=i}}return{next:N}}function N(){return{value:void 0,done:!0}}return g.prototype=f,a(b,"constructor",{value:f,configurable:!0}),a(f,"constructor",{value:g,configurable:!0}),g.displayName=l(f,c,"GeneratorFunction"),i.isGeneratorFunction=function(e){var n="function"==typeof e&&e.constructor;return!!n&&(n===g||"GeneratorFunction"===(n.displayName||n.name))},i.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,l(e,c,"GeneratorFunction")),e.prototype=Object.create(b),e},i.awrap=function(e){return{__await:e}},S(E.prototype),l(E.prototype,p,(function(){return this})),i.AsyncIterator=E,i.async=function(e,n,t,r,o){void 0===o&&(o=Promise);var a=new E(u(e,n,t,r),o);return i.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(b),l(b,c,"Generator"),l(b,d,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),i.keys=function(e){var n=Object(e),t=[];for(var i in n)t.push(i);return t.reverse(),function e(){for(;t.length;){var i=t.pop();if(i in n)return e.value=i,e.done=!1,e}return e.done=!0,e}},i.values=A,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t,i){return a.type="throw",a.arg=e,n.next=t,i&&(n.method="next",n.arg=void 0),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return t("end");if(this.prev>=r.tryLoc){var s=o.call(r,"catchLoc"),d=o.call(r,"finallyLoc");if(s&&d){if(r.catchLoc>this.prev)return t(r.catchLoc,!0);if(r.finallyLoc>this.prev)return t(r.finallyLoc)}else if(s){if(r.catchLoc>this.prev)return t(r.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(r.finallyLoc>this.prev)return t(r.finallyLoc)}}}},abrupt:function(e,n){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(this.prev>=i.tryLoc&&o.call(i,"finallyLoc")&&i.finallyLoc>this.prev){var r=i;break}}r&&("break"===e||"continue"===e)&&n>=r.tryLoc&&r.finallyLoc>=n&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=n,r?(this.method="next",this.next=r.finallyLoc,y):this.complete(a)},complete:function(e,n){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&n&&(this.next=n),y},finish:function(e){for(var n=this.tryEntries.length-1;n>=0;--n){var t=this.tryEntries[n];if(t.finallyLoc===e)return this.complete(t.completion,t.afterLoc),O(t),y}},catch:function(e){for(var n=this.tryEntries.length-1;n>=0;--n){var t=this.tryEntries[n];if(t.tryLoc===e){var i=t.completion;if("throw"===i.type){var r=i.arg;O(t)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,t){return this.delegate={iterator:A(e),resultName:n,nextLoc:t},"next"===this.method&&(this.arg=void 0),y}},i}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})),Se=n(be),Ee=t((function(e){function n(e,n,t,i,r,o,a){try{var s=e[o](a),d=s.value}catch(e){return void t(e)}s.done?n(d):Promise.resolve(d).then(i,r)}e.exports=function(e){return function(){var t=this,i=arguments;return new Promise((function(r,o){var a=e.apply(t,i);function s(e){n(a,r,o,s,d,"next",e)}function d(e){n(a,r,o,s,d,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports})),qe=n(Ee),Ce=n(t((function(e){e.exports=function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports}))),Ie=n(t((function(e){function n(e,n){for(var t=0;n.length>t;t++){var i=n[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}e.exports=function(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports})));var Oe=function(e){return void 0===e};var _e={call:function(e){var n=e.api,t=e.meta,i=e.payload;return new Promise((function(e){window.LanxinJsBridge.callHandler(n,{meta:t,payload:i},e)}))},listen:function(e){return window.LanxinJsBridge.registerHandler(e.api,e.callback)}},Ae={init:function(){if(!window.LanxinJsBridge){var e;window.LanxinJsBridge={registerHandler:function(e,n){t[e]=n},callHandler:function(e,n,t){2===arguments.length&&"function"==typeof n&&(t=n,n=null);p({handlerName:e,data:n},t)},disableJavscriptAlertBoxSafetyTimeout:d,_fetchQueue:function(){var t=JSON.stringify(n);n=[],window.lx_client&&window.lx_client.call?window.lx_client.call("".concat(i,"://return/_fetchQueue/").concat(t)):e.contentWindow.location.replace("".concat(i,"://return/_fetchQueue/").concat(t))},_checkHandler:function(n){n=JSON.parse(n);var r=n.handlerName,o={callbackId:n.callbackId,handlerName:r,result:!!t[r]};e.contentWindow.location.replace("".concat(i,"://return/_checkHandler/").concat(JSON.stringify(o)))},_reset:function(){(t={})._disableJavascriptAlertBoxSafetyTimeout=d},_handleMessageFromNative:function(e){!function(e){s?setTimeout(n):n();function n(){var n,i=JSON.parse(e);if(i.responseId){if(!(n=o[i.responseId]))return;n(i.responseData),delete o[i.responseId]}else{if(i.callbackId){var r=i.callbackId;n=function(e){p({handlerName:i.handlerName,responseId:r,responseData:e})}}var a=t[i.handlerName];a?a(i.data,n):console.log("LanxinJsBridge: WARNING: no handler for message from ObjC:",i)}}}(e)}};var n=[],t={},i="lx",r="__wvjb_queue_message__",o={},a=1,s=!0;(e=document.createElement("iframe")).style.display="none",e.src="",document.documentElement.appendChild(e),t._disableJavascriptAlertBoxSafetyTimeout=d}function d(){s=!1}function p(t,s){if(s){a++;var d="cb_".concat(a,"_").concat((new Date).getTime());o[d]=s,t.callbackId=d}n.push(t),window.lx_client&&window.lx_client.call?window.lx_client.call("".concat(i,"://").concat(r)):e.contentWindow.location.replace("".concat(i,"://").concat(r))}},call:_e.call,listen:_e.listen};function Ne(){if(!window.LanxinJsBridge){var e;window.LanxinJsBridge={registerHandler:function(e,n){t[e]=n},callHandler:function(e,n,t){2===arguments.length&&"function"==typeof n&&(t=n,n=null);d({handlerName:e,data:n},t)},disableJavscriptAlertBoxSafetyTimeout:s,_fetchQueue:function(){var e=JSON.stringify(n);return n=[],e},_checkHandler:function(e){e=JSON.parse(e);var n=e.handlerName;return JSON.stringify({callbackId:e.callbackId,handlerName:n,result:!!t[n]})},_reset:function(){(t={})._disableJavascriptAlertBoxSafetyTimeout=s},_handleMessageFromObjC:function(e){!function(e){a?setTimeout(n):n();function n(){var n,i=JSON.parse(e);if(i.responseId){if(!(n=r[i.responseId]))return;n(i.responseData),delete r[i.responseId]}else{if(i.callbackId){var o=i.callbackId;n=function(e){d({handlerName:i.handlerName,responseId:o,responseData:e})}}var a=t[i.handlerName];a?a(i.data,n):console.log("LanxinJsBridge: WARNING: no handler for message from ObjC:",i)}}}(e)}};var n=[],t={},i="__wvjb_queue_message__",r={},o=1,a=!0;(e=document.createElement("iframe")).style.display="none",e.src="".concat("lx","://").concat(i),document.documentElement.appendChild(e),t._disableJavascriptAlertBoxSafetyTimeout=s}function s(){a=!1}function d(t,a){if(a){o++;var s="cb_".concat(o,"_").concat((new Date).getTime());r[s]=a,t.callbackId=s}n.push(t),e.src="".concat("lx","://").concat(i)}}var ze,Te={call:function(e){var n=e.api,t=e.meta,i=e.payload;return new Promise((function(e){window.LanxinJsBridge.callHandler(n,{meta:t,payload:i},e)}))},listen:function(e){return window.LanxinJsBridge.registerHandler(e.api,e.callback)}},je={init:Ne,call:Te.call,listen:Te.listen},ke={init:Ne,call:Te.call,listen:Te.listen},Be={init:Ne,call:Te.call,listen:Te.listen},Le={init:Ne,call:Te.call,listen:Te.listen},Ke=R,De=P,Re=M,Pe=V,Me=F,Ve=(r(ze={},D,Ae),r(ze,Ke,je),r(ze,De,ke),r(ze,Re,Be),r(ze,Pe,Le),r(ze,Me,je),ze),Fe=function(){function e(){Ce(this,e)}var n;return Ie(e,null,[{key:"create",value:(n=qe(Se().mark((function e(){var n;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!T(window.LanxinJsBridgeHandler)||!ue(window.LanxinJsBridgeHandler.createBridge)){e.next=2;break}return e.abrupt("return",window.LanxinJsBridgeHandler.createBridge());case 2:if(n=Ve[W?R:J?D:Y?P:X?M:$?V:F],Z.isLanxinApp){e.next=6;break}throw Error("Application is not in Lanxin App.");case 6:if(!Oe(n)){e.next=8;break}throw Error("Can not find appropriate Bridge.");case 8:return e.next=10,n.init();case 10:return e.abrupt("return",n);case 11:case"end":return e.stop()}}),e)}))),function(){return n.apply(this,arguments)})}]),e}(),Ue=null;function Je(e){Ue=e}function We(){return(We=qe(Se().mark((function e(n,t){return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==Ue){e.next=4;break}return e.next=3,Fe.create();case 3:Ue=e.sent;case 4:return n.bridge=Ue,e.next=7,t();case 7:return e.abrupt("return",n);case 8:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function He(){return(He=qe(Se().mark((function e(n,t){var i,r,o,s,d,p,c,l,u,m;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.req,"call"!==(i=n.apiMeta).type){e.next=16;break}return s=(o=r||{}).success,d=o.fail,p=o.complete,n.req={},n.req.api=i.name,n.req.payload=a({},r),e.next=8,t();case 8:l=(c=n.res).status,u=c.data,m=L,!Oe(p)&&ue(p)&&(p(u,l),m=B),l&&0===l.code?!Oe(s)&&ue(s)&&(s(u),m=B):!Oe(d)&&ue(d)&&(d(l),m=B),n.res.callbackType=m,e.next=18;break;case 16:return e.next=18,t();case 18:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ge(){return(Ge=qe(Se().mark((function e(n,t){var i,r,o;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.req,o=null,"listen"===(i=n.apiMeta).type&&(o=r,n.req={},n.req.api=i.name,n.req.callback=o),e.next=5,t();case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ye(){return(Ye=qe(Se().mark((function e(n,t){var i,r,o;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.req,o=null,"notify"===(i=n.apiMeta).type&&(o=r,n.req={},n.req.api=i.name,n.req.callback=o),e.next=5,t();case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Xe=Object.prototype;var $e=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||Xe)},Qe=I(Object.keys,Object),Ze=Object.prototype.hasOwnProperty;var en,nn=function(e){if(!$e(e))return Qe(e);var n=[];for(var t in Object(e))Ze.call(e,t)&&"constructor"!=t&&n.push(t);return n},tn=v["__core-js_shared__"],rn=(en=/[^.]+$/.exec(tn&&tn.keys&&tn.keys.IE_PROTO||""))?"Symbol(src)_1."+en:"";var on=function(e){return!!rn&&rn in e},an=Function.prototype.toString;var sn=function(e){if(null!=e){try{return an.call(e)}catch(e){}try{return e+""}catch(e){}}return""},dn=/^\[object .+?Constructor\]$/,pn=RegExp("^"+Function.prototype.toString.call(Object.prototype.hasOwnProperty).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var cn=function(e){return!(!le(e)||on(e))&&(ue(e)?pn:dn).test(sn(e))};var ln=function(e,n){return null==e?void 0:e[n]};var un=function(e,n){var t=ln(e,n);return cn(t)?t:void 0},mn=un(v,"DataView"),yn=un(v,"Map"),vn=un(v,"Promise"),gn=un(v,"Set"),fn=un(v,"WeakMap"),xn="[object Map]",wn="[object Promise]",hn="[object Set]",bn="[object WeakMap]",Sn="[object DataView]",En=sn(mn),qn=sn(yn),Cn=sn(vn),In=sn(gn),On=sn(fn),_n=C;(mn&&_n(new mn(new ArrayBuffer(1)))!=Sn||yn&&_n(new yn)!=xn||vn&&_n(vn.resolve())!=wn||gn&&_n(new gn)!=hn||fn&&_n(new fn)!=bn)&&(_n=function(e){var n=C(e),t="[object Object]"==n?e.constructor:void 0,i=t?sn(t):"";if(i)switch(i){case En:return Sn;case qn:return xn;case Cn:return wn;case In:return hn;case On:return bn}return n});var An=_n;var Nn=function(e){return _(e)&&"[object Arguments]"==C(e)},zn=Object.prototype,Tn=zn.hasOwnProperty,jn=zn.propertyIsEnumerable,kn=Nn(function(){return arguments}())?Nn:function(e){return _(e)&&Tn.call(e,"callee")&&!jn.call(e,"callee")},Bn=kn,Ln=Array.isArray;var Kn=function(e){return"number"==typeof e&&e>-1&&e%1==0&&9007199254740991>=e};var Dn=function(e){return null!=e&&Kn(e.length)&&!ue(e)};var Rn=function(){return!1},Pn=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t?v.Buffer:void 0;e.exports=(r?r.isBuffer:void 0)||Rn})),Mn={};Mn["[object Float32Array]"]=Mn["[object Float64Array]"]=Mn["[object Int8Array]"]=Mn["[object Int16Array]"]=Mn["[object Int32Array]"]=Mn["[object Uint8Array]"]=Mn["[object Uint8ClampedArray]"]=Mn["[object Uint16Array]"]=Mn["[object Uint32Array]"]=!0,Mn["[object Arguments]"]=Mn["[object Array]"]=Mn["[object ArrayBuffer]"]=Mn["[object Boolean]"]=Mn["[object DataView]"]=Mn["[object Date]"]=Mn["[object Error]"]=Mn["[object Function]"]=Mn["[object Map]"]=Mn["[object Number]"]=Mn["[object Object]"]=Mn["[object RegExp]"]=Mn["[object Set]"]=Mn["[object String]"]=Mn["[object WeakMap]"]=!1;var Vn=function(e){return _(e)&&Kn(e.length)&&!!Mn[C(e)]};var Fn=function(e){return function(n){return e(n)}},Un=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t&&m.process,o=function(){try{var e=i&&i.require&&i.require("util").types;return e||r&&r.binding&&r.binding("util")}catch(e){}}();e.exports=o})),Jn=Un&&Un.isTypedArray,Wn=Jn?Fn(Jn):Vn,Hn=Object.prototype.hasOwnProperty;var Gn=function(e){if(null==e)return!0;if(Dn(e)&&(Ln(e)||"string"==typeof e||"function"==typeof e.splice||Pn(e)||Wn(e)||Bn(e)))return!e.length;var n=An(e);if("[object Map]"==n||"[object Set]"==n)return!e.size;if($e(e))return!nn(e).length;for(var t in e)if(Hn.call(e,t))return!1;return!0};function Yn(e){var n={type:[],required:!1,validator:void 0,default:void 0};return(Ln(e)||ue(e))&&(n.type=[].concat(e)),T(e)&&(Object.assign(n,e),n.type=[].concat(n.type)),n}var Xn=/^(String|Number|Boolean|Function|Symbol)$/;function $n(e,n){var t,i,r=!1,o=(i=(t=n)&&(""+t).match(/^\s*function (\w+)/))?i[1]:"";if(Xn.test(o)){var a=he(e);(r=a===o.toLowerCase())||"object"!==a||(r=e instanceof n)}else r="Object"===o?T(e):"Array"===o?Ln(e):e instanceof n;return{valid:r,expectedType:o}}function Qn(e){return Object.prototype.toString.call(e).slice(8,-1)}function Zn(){return(Zn=qe(Se().mark((function e(n,t){var i,r,o,a,s,d,p,c,l,u,m;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=(i=n.req).payload,!(r=n.apiMeta.payload)){e.next=26;break}e.t0=Se().keys(r);case 5:if((e.t1=e.t0()).done){e.next=26;break}if(s=o[a=e.t1.value],d=Yn(r[a]),Oe(s)&&!Oe(d.default)&&(s=o[a]=d.default),!d.required){e.next=14;break}if("number"==typeof s||"boolean"==typeof s||!Gn(s)){e.next=14;break}throw Error('Missing required payload: "'.concat(a,'".'));case 14:if(Oe(s)){e.next=24;break}if(!ue(d.validator)||d.validator(s)){e.next=17;break}throw Error('Invalid payload: validator check failed for payload: "'.concat(a,'".'));case 17:if(d.type.length<=0){e.next=24;break}for(p=d.type,c=!1,l=[],u=0;p.length>u&&!c;u++)m=$n(s,p[u]),l.push(m.expectedType||""),c=m.valid;if(c){e.next=24;break}throw Error("".concat('Invalid payload: type check failed for payload "'.concat(a,'".')+" Expected ".concat(l.join(", "))+", got ".concat(Qn(s),". \n")).concat(JSON.stringify(i,null,2)));case 24:e.next=5;break;case 26:return e.next=28,t();case 28:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var et=function(e,n){return e===n||e!=e&&n!=n};var nt=function(e,n){for(var t=e.length;t--;)if(et(e[t][0],n))return t;return-1},tt=Array.prototype.splice;var it=function(e){var n=this.__data__,t=nt(n,e);return t>=0&&(t==n.length-1?n.pop():tt.call(n,t,1),--this.size,!0)};var rt=function(e){var n=this.__data__,t=nt(n,e);return 0>t?void 0:n[t][1]};var ot=function(e){return nt(this.__data__,e)>-1};var at=function(e,n){var t=this.__data__,i=nt(t,e);return 0>i?(++this.size,t.push([e,n])):t[i][1]=n,this};function st(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}st.prototype.clear=function(){this.__data__=[],this.size=0},st.prototype.delete=it,st.prototype.get=rt,st.prototype.has=ot,st.prototype.set=at;var dt=st;var pt=function(){this.__data__=new dt,this.size=0};var ct=function(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t};var lt=function(e){return this.__data__.get(e)};var ut=function(e){return this.__data__.has(e)},mt=un(Object,"create");var yt=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},vt=Object.prototype.hasOwnProperty;var gt=function(e){var n=this.__data__;if(mt){var t=n[e];return"__lodash_hash_undefined__"===t?void 0:t}return vt.call(n,e)?n[e]:void 0},ft=Object.prototype.hasOwnProperty;var xt=function(e){var n=this.__data__;return mt?void 0!==n[e]:ft.call(n,e)};var wt=function(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=mt&&void 0===n?"__lodash_hash_undefined__":n,this};function ht(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}ht.prototype.clear=function(){this.__data__=mt?mt(null):{},this.size=0},ht.prototype.delete=yt,ht.prototype.get=gt,ht.prototype.has=xt,ht.prototype.set=wt;var bt=ht;var St=function(e){var n=typeof e;return"string"==n||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==e:null===e};var Et=function(e,n){var t=e.__data__;return St(n)?t["string"==typeof n?"string":"hash"]:t.map};var qt=function(e){var n=Et(this,e).delete(e);return this.size-=n?1:0,n};var Ct=function(e){return Et(this,e).get(e)};var It=function(e){return Et(this,e).has(e)};var Ot=function(e,n){var t=Et(this,e),i=t.size;return t.set(e,n),this.size+=t.size==i?0:1,this};function _t(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}_t.prototype.clear=function(){this.size=0,this.__data__={hash:new bt,map:new(yn||dt),string:new bt}},_t.prototype.delete=qt,_t.prototype.get=Ct,_t.prototype.has=It,_t.prototype.set=Ot;var At=_t;var Nt=function(e,n){var t=this.__data__;if(t instanceof dt){var i=t.__data__;if(!yn||199>i.length)return i.push([e,n]),this.size=++t.size,this;t=this.__data__=new At(i)}return t.set(e,n),this.size=t.size,this};function zt(e){var n=this.__data__=new dt(e);this.size=n.size}zt.prototype.clear=pt,zt.prototype.delete=ct,zt.prototype.get=lt,zt.prototype.has=ut,zt.prototype.set=Nt;var Tt=zt;var jt=function(e,n){for(var t=-1,i=null==e?0:e.length;++t<i&&!1!==n(e[t],t,e););return e},kt=function(){try{var e=un(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Bt=function(e,n,t){"__proto__"==n&&kt?kt(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t},Lt=Object.prototype.hasOwnProperty;var Kt=function(e,n,t){var i=e[n];Lt.call(e,n)&&et(i,t)&&(void 0!==t||n in e)||Bt(e,n,t)};var Dt=function(e,n,t,i){var r=!t;t||(t={});for(var o=-1,a=n.length;++o<a;){var s=n[o],d=i?i(t[s],e[s],s,t,e):void 0;void 0===d&&(d=e[s]),r?Bt(t,s,d):Kt(t,s,d)}return t};var Rt=function(e,n){for(var t=-1,i=Array(e);++t<e;)i[t]=n(t);return i},Pt=/^(?:0|[1-9]\d*)$/;var Mt=function(e,n){var t=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==t||"symbol"!=t&&Pt.test(e))&&e>-1&&e%1==0&&n>e},Vt=Object.prototype.hasOwnProperty;var Ft=function(e,n){var t=Ln(e),i=!t&&Bn(e),r=!t&&!i&&Pn(e),o=!t&&!i&&!r&&Wn(e),a=t||i||r||o,s=a?Rt(e.length,String):[],d=s.length;for(var p in e)!n&&!Vt.call(e,p)||a&&("length"==p||r&&("offset"==p||"parent"==p)||o&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||Mt(p,d))||s.push(p);return s};var Ut=function(e){return Dn(e)?Ft(e):nn(e)};var Jt=function(e,n){return e&&Dt(n,Ut(n),e)};var Wt=function(e){var n=[];if(null!=e)for(var t in Object(e))n.push(t);return n},Ht=Object.prototype.hasOwnProperty;var Gt=function(e){if(!le(e))return Wt(e);var n=$e(e),t=[];for(var i in e)("constructor"!=i||!n&&Ht.call(e,i))&&t.push(i);return t};var Yt=function(e){return Dn(e)?Ft(e,!0):Gt(e)};var Xt=function(e,n){return e&&Dt(n,Yt(n),e)},$t=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t?v.Buffer:void 0,o=r?r.allocUnsafe:void 0;e.exports=function(e,n){if(n)return e.slice();var t=e.length,i=o?o(t):new e.constructor(t);return e.copy(i),i}}));var Qt=function(e,n){var t=-1,i=e.length;for(n||(n=Array(i));++t<i;)n[t]=e[t];return n};var Zt=function(e,n){for(var t=-1,i=null==e?0:e.length,r=0,o=[];++t<i;){var a=e[t];n(a,t,e)&&(o[r++]=a)}return o};var ei=function(){return[]},ni=Object.prototype.propertyIsEnumerable,ti=Object.getOwnPropertySymbols,ii=ti?function(e){return null==e?[]:Zt(ti(e=Object(e)),(function(n){return ni.call(e,n)}))}:ei;var ri=function(e,n){return Dt(e,ii(e),n)};var oi=function(e,n){for(var t=-1,i=n.length,r=e.length;++t<i;)e[r+t]=n[t];return e},ai=Object.getOwnPropertySymbols?function(e){for(var n=[];e;)oi(n,ii(e)),e=O(e);return n}:ei;var si=function(e,n){return Dt(e,ai(e),n)};var di=function(e,n,t){var i=n(e);return Ln(e)?i:oi(i,t(e))};var pi=function(e){return di(e,Ut,ii)};var ci=function(e){return di(e,Yt,ai)},li=Object.prototype.hasOwnProperty;var ui=function(e){var n=e.length,t=new e.constructor(n);return n&&"string"==typeof e[0]&&li.call(e,"index")&&(t.index=e.index,t.input=e.input),t},mi=v.Uint8Array;var yi=function(e){var n=new e.constructor(e.byteLength);return new mi(n).set(new mi(e)),n};var vi=function(e,n){var t=n?yi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)},gi=/\w*$/;var fi=function(e){var n=new e.constructor(e.source,gi.exec(e));return n.lastIndex=e.lastIndex,n},xi=g?g.prototype:void 0,wi=xi?xi.valueOf:void 0;var hi=function(e){return wi?Object(wi.call(e)):{}};var bi=function(e,n){var t=n?yi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)};var Si=function(e,n,t){var i=e.constructor;switch(n){case"[object ArrayBuffer]":return yi(e);case"[object Boolean]":case"[object Date]":return new i(+e);case"[object DataView]":return vi(e,t);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return bi(e,t);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(e);case"[object RegExp]":return fi(e);case"[object Symbol]":return hi(e)}},Ei=Object.create,qi=function(){function e(){}return function(n){if(!le(n))return{};if(Ei)return Ei(n);e.prototype=n;var t=new e;return e.prototype=void 0,t}}();var Ci=function(e){return"function"!=typeof e.constructor||$e(e)?{}:qi(O(e))};var Ii=function(e){return _(e)&&"[object Map]"==An(e)},Oi=Un&&Un.isMap,_i=Oi?Fn(Oi):Ii;var Ai=function(e){return _(e)&&"[object Set]"==An(e)},Ni=Un&&Un.isSet,zi=Ni?Fn(Ni):Ai,Ti="[object Arguments]",ji="[object Function]",ki="[object Object]",Bi={};Bi[Ti]=Bi["[object Array]"]=Bi["[object ArrayBuffer]"]=Bi["[object DataView]"]=Bi["[object Boolean]"]=Bi["[object Date]"]=Bi["[object Float32Array]"]=Bi["[object Float64Array]"]=Bi["[object Int8Array]"]=Bi["[object Int16Array]"]=Bi["[object Int32Array]"]=Bi["[object Map]"]=Bi["[object Number]"]=Bi[ki]=Bi["[object RegExp]"]=Bi["[object Set]"]=Bi["[object String]"]=Bi["[object Symbol]"]=Bi["[object Uint8Array]"]=Bi["[object Uint8ClampedArray]"]=Bi["[object Uint16Array]"]=Bi["[object Uint32Array]"]=!0,Bi["[object Error]"]=Bi[ji]=Bi["[object WeakMap]"]=!1;var Li=function e(n,t,i,r,o,a){var s,d=1&t,p=2&t,c=4&t;if(i&&(s=o?i(n,r,o,a):i(n)),void 0!==s)return s;if(!le(n))return n;var l=Ln(n);if(l){if(s=ui(n),!d)return Qt(n,s)}else{var u=An(n),m=u==ji||"[object GeneratorFunction]"==u;if(Pn(n))return $t(n,d);if(u==ki||u==Ti||m&&!o){if(s=p||m?{}:Ci(n),!d)return p?si(n,Xt(s,n)):ri(n,Jt(s,n))}else{if(!Bi[u])return o?n:{};s=Si(n,u,d)}}a||(a=new Tt);var y=a.get(n);if(y)return y;a.set(n,s),zi(n)?n.forEach((function(r){s.add(e(r,t,i,r,n,a))})):_i(n)&&n.forEach((function(r,o){s.set(o,e(r,t,i,o,n,a))}));var v=l?void 0:(c?p?ci:pi:p?Yt:Ut)(n);return jt(v||n,(function(r,o){v&&(r=n[o=r]),Kt(s,o,e(r,t,i,o,n,a))})),s};var Ki=function(e){return Li(e,5)};function Di(){return(Di=qe(Se().mark((function e(n,t){var i,r,o,a;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("call"!==(i=n.apiMeta).type){e.next=11;break}return e.next=4,t();case 4:n.res=Ki(n.res),o=(r=n.res).status.code,a=i.name.split(".").shift(),o>0&&!Oe(K[a])&&(r.status.code=o+K[a]),e.next=13;break;case 11:return e.next=13,t();case 13:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ri(){return Ri=qe(Se().mark((function e(n,t){var i,r;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("call"!==n.apiMeta.type){e.next=6;break}return i=n.bridge,r=n.req,e.next=4,i.call(r);case 4:n.res=e.sent,ge("response","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Ki(n.res)),";"));case 6:return e.next=8,t();case 8:case"end":return e.stop()}}),e)}))),Ri.apply(this,arguments)}var Pi=function(e){return"symbol"==typeof e||_(e)&&"[object Symbol]"==C(e)},Mi=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Vi=/^\w*$/;var Fi=function(e,n){if(Ln(e))return!1;var t=typeof e;return!("number"!=t&&"symbol"!=t&&"boolean"!=t&&null!=e&&!Pi(e))||(Vi.test(e)||!Mi.test(e)||null!=n&&e in Object(n))};function Ui(e,n){if("function"!=typeof e||null!=n&&"function"!=typeof n)throw new TypeError("Expected a function");var t=function(){var i=arguments,r=n?n.apply(this,i):i[0],o=t.cache;if(o.has(r))return o.get(r);var a=e.apply(this,i);return t.cache=o.set(r,a)||o,a};return t.cache=new(Ui.Cache||At),t}Ui.Cache=At;var Ji=Ui;var Wi=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Hi=/\\(\\)?/g,Gi=function(e){var n=Ji(e,(function(e){return 500===t.size&&t.clear(),e})),t=n.cache;return n}((function(e){var n=[];return 46===e.charCodeAt(0)&&n.push(""),e.replace(Wi,(function(e,t,i,r){n.push(i?r.replace(Hi,"$1"):t||e)})),n}));var Yi=function(e,n){for(var t=-1,i=null==e?0:e.length,r=Array(i);++t<i;)r[t]=n(e[t],t,e);return r},Xi=g?g.prototype:void 0,$i=Xi?Xi.toString:void 0;var Qi=function e(n){if("string"==typeof n)return n;if(Ln(n))return Yi(n,e)+"";if(Pi(n))return $i?$i.call(n):"";var t=n+"";return"0"==t&&1/n==-Infinity?"-0":t};var Zi=function(e){return null==e?"":Qi(e)};var er=function(e,n){return Ln(e)?e:Fi(e,n)?[e]:Gi(Zi(e))};var nr=function(e){if("string"==typeof e||Pi(e))return e;var n=e+"";return"0"==n&&1/e==-Infinity?"-0":n};var tr=function(e,n){for(var t=0,i=(n=er(n,e)).length;null!=e&&i>t;)e=e[nr(n[t++])];return t&&t==i?e:void 0};var ir=function(e,n,t){var i=null==e?void 0:tr(e,n);return void 0===i?t:i};function rr(){return rr=qe(Se().mark((function e(n,t){var i,r,o,a,s,d,p;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.apiMeta,r=n.bridge,a=ir(o=n.req,"payload.trigger"),("notify"===i.type||ue(a))&&(s=o.api,d=o.callback,"notify"!==i.type&&ue(a)&&(s=i.triggerName,d=a),p=function(){var e=qe(Se().mark((function e(t){var i;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.payload,ge("callback request","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Ki(t.payload)))),e.t0=ue(d),!e.t0){e.next=6;break}return e.next=6,d(i);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),r.listen({api:s,callback:p})),e.next=5,t();case 5:case"end":return e.stop()}}),e)}))),rr.apply(this,arguments)}function or(){return or=qe(Se().mark((function e(n,t){var i,r,o,a,s,d,p;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.apiMeta,r=n.bridge,a=ir(o=n.req,"payload.trigger"),("listen"===i.type||ue(a))&&(s=o.api,d=o.callback,"listen"!==i.type&&ue(a)&&(s=i.triggerName,d=a),p=function(){var e=qe(Se().mark((function e(t,i){var r,o,a,s;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(ge("callback request","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Ki(t.payload)))),r=t.payload,a=void 0===(o=t.meta)?{}:o,e.t0=d,!e.t0){e.next=7;break}return e.next=6,d(r);case 6:e.t0=e.sent;case 7:(s=e.t0)&&ge("callback response","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Ki(s)))),i({status:{code:0,message:"OK"},meta:a,data:{result:s}});case 10:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),r.listen({api:s,callback:p})),e.next=5,t();case 5:case"end":return e.stop()}}),e)}))),or.apply(this,arguments)}function ar(){return ar=qe(Se().mark((function e(n,t){var i,r;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.bridge,r=n.req,"listen"===n.apiMeta.type&&i.call({api:"internal.utils.notifyRegisterAction",meta:{},payload:{type:r.callback?"start":"stop",api:r.api}}),e.next=4,t();case 4:case"end":return e.stop()}}),e)}))),ar.apply(this,arguments)}function sr(){return(sr=qe(Se().mark((function e(n,t){return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return T(window.LanxinJsBridgeHandler)&&ue(window.LanxinJsBridgeHandler.afterMiddlewareProcessed)&&window.LanxinJsBridgeHandler.afterMiddlewareProcessed(n),e.next=3,t();case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var dr=[function(e,n){return We.apply(this,arguments)},function(e,n){return Ge.apply(this,arguments)},function(e,n){return Ye.apply(this,arguments)},function(e,n){return He.apply(this,arguments)},function(e,n){return Zn.apply(this,arguments)},function(e,n){return Di.apply(this,arguments)},function(e,n){return or.apply(this,arguments)},function(e,n){return rr.apply(this,arguments)},function(e,n){return Ri.apply(this,arguments)},function(e,n){return ar.apply(this,arguments)},function(e,n){return sr.apply(this,arguments)}];function pr(){}function cr(){cr.init.call(this)}function lr(e){return void 0===e._maxListeners?cr.defaultMaxListeners:e._maxListeners}function ur(e,n,t){if(n)e.call(t);else for(var i=e.length,r=hr(e,i),o=0;i>o;++o)r[o].call(t)}function mr(e,n,t,i){if(n)e.call(t,i);else for(var r=e.length,o=hr(e,r),a=0;r>a;++a)o[a].call(t,i)}function yr(e,n,t,i,r){if(n)e.call(t,i,r);else for(var o=e.length,a=hr(e,o),s=0;o>s;++s)a[s].call(t,i,r)}function vr(e,n,t,i,r,o){if(n)e.call(t,i,r,o);else for(var a=e.length,s=hr(e,a),d=0;a>d;++d)s[d].call(t,i,r,o)}function gr(e,n,t,i){if(n)e.apply(t,i);else for(var r=e.length,o=hr(e,r),a=0;r>a;++a)o[a].apply(t,i)}function fr(e,n,t,i){var r,o,a,s;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if((o=e._events)?(o.newListener&&(e.emit("newListener",n,t.listener?t.listener:t),o=e._events),a=o[n]):(o=e._events=new pr,e._eventsCount=0),a){if("function"==typeof a?a=o[n]=i?[t,a]:[a,t]:i?a.unshift(t):a.push(t),!a.warned&&(r=lr(e))&&r>0&&a.length>r){a.warned=!0;var d=Error("Possible EventEmitter memory leak detected. "+a.length+" "+n+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=n,d.count=a.length,s=d,"function"==typeof console.warn?console.warn(s):console.log(s)}}else a=o[n]=t,++e._eventsCount;return e}function xr(e,n,t){var i=!1;function r(){e.removeListener(n,r),i||(i=!0,t.apply(e,arguments))}return r.listener=t,r}function wr(e){var n=this._events;if(n){var t=n[e];if("function"==typeof t)return 1;if(t)return t.length}return 0}function hr(e,n){for(var t=Array(n);n--;)t[n]=e[n];return t}T(window.LanxinJsBridgeHandler)&&ue(window.LanxinJsBridgeHandler.beforeMiddlewareLoad)&&(dr=window.LanxinJsBridgeHandler.beforeMiddlewareLoad(dr)),pr.prototype=Object.create(null),cr.EventEmitter=cr,cr.usingDomains=!1,cr.prototype.domain=void 0,cr.prototype._events=void 0,cr.prototype._maxListeners=void 0,cr.defaultMaxListeners=10,cr.init=function(){this.domain=null,cr.usingDomains&&undefined.active,this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new pr,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},cr.prototype.setMaxListeners=function(e){if("number"!=typeof e||0>e||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},cr.prototype.getMaxListeners=function(){return lr(this)},cr.prototype.emit=function(e){var n,t,i,r,o,a,s,d="error"===e;if(a=this._events)d=d&&null==a.error;else if(!d)return!1;if(s=this.domain,d){if(n=arguments[1],!s){if(n instanceof Error)throw n;var p=Error('Uncaught, unspecified "error" event. ('+n+")");throw p.context=n,p}return n||(n=Error('Uncaught, unspecified "error" event')),n.domainEmitter=this,n.domain=s,n.domainThrown=!1,s.emit("error",n),!1}if(!(t=a[e]))return!1;var c="function"==typeof t;switch(i=arguments.length){case 1:ur(t,c,this);break;case 2:mr(t,c,this,arguments[1]);break;case 3:yr(t,c,this,arguments[1],arguments[2]);break;case 4:vr(t,c,this,arguments[1],arguments[2],arguments[3]);break;default:for(r=Array(i-1),o=1;i>o;o++)r[o-1]=arguments[o];gr(t,c,this,r)}return!0},cr.prototype.on=cr.prototype.addListener=function(e,n){return fr(this,e,n,!1)},cr.prototype.prependListener=function(e,n){return fr(this,e,n,!0)},cr.prototype.once=function(e,n){if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');return this.on(e,xr(this,e,n)),this},cr.prototype.prependOnceListener=function(e,n){if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,xr(this,e,n)),this},cr.prototype.removeListener=function(e,n){var t,i,r,o,a;if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');if(!(i=this._events))return this;if(!(t=i[e]))return this;if(t===n||t.listener&&t.listener===n)0==--this._eventsCount?this._events=new pr:(delete i[e],i.removeListener&&this.emit("removeListener",e,t.listener||n));else if("function"!=typeof t){for(r=-1,o=t.length;o-- >0;)if(t[o]===n||t[o].listener&&t[o].listener===n){a=t[o].listener,r=o;break}if(0>r)return this;if(1===t.length){if(t[0]=void 0,0==--this._eventsCount)return this._events=new pr,this;delete i[e]}else!function(e,n){for(var t=n,i=t+1,r=e.length;r>i;t+=1,i+=1)e[t]=e[i];e.pop()}(t,r);i.removeListener&&this.emit("removeListener",e,a||n)}return this},cr.prototype.removeAllListeners=function(e){var n,t;if(!(t=this._events))return this;if(!t.removeListener)return 0===arguments.length?(this._events=new pr,this._eventsCount=0):t[e]&&(0==--this._eventsCount?this._events=new pr:delete t[e]),this;if(0===arguments.length){for(var i,r=Object.keys(t),o=0;r.length>o;++o)"removeListener"!==(i=r[o])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=new pr,this._eventsCount=0,this}if("function"==typeof(n=t[e]))this.removeListener(e,n);else if(n)do{this.removeListener(e,n[n.length-1])}while(n[0]);return this},cr.prototype.listeners=function(e){var n,t=this._events;return t&&(n=t[e])?"function"==typeof n?[n.listener||n]:function(e){for(var n=Array(e.length),t=0;n.length>t;++t)n[t]=e[t].listener||e[t];return n}(n):[]},cr.listenerCount=function(e,n){return"function"==typeof e.listenerCount?e.listenerCount(n):wr.call(e,n)},cr.prototype.listenerCount=wr,cr.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var br=[{name:"config",payload:{debug:{type:Boolean,default:!1},appId:{type:String,required:!0},timestamp:Number,nonceStr:String,signature:String},type:"call"}],Sr={DEBUG:"DEBUG"},Er=new(function(){function e(){Ce(this,e)}return Ie(e,[{key:"set",value:function(e,n){Sr[e]?this.key=n:console.error("Env has no key: ",e)}},{key:"get",value:function(e){return this[e]}}]),e}()),qr=j,Cr=new cr;var Ir={config:function(e){qr===k&&(Cr=new cr,qr=j),window.LanxinJsBridge&&window.LanxinJsBridge._reset&&window.LanxinJsBridge._reset();var n=null;return br.forEach((function(e){"config"===e.name&&(n=e)})),Er.set("DEBUG",n.debug),e.success=function(){qr=k,console.info("[Lanxin] JsApi Config Success..."),Cr.listeners("ready").length>0&&Cr.emit("ready")},e.fail=function(e){qr=j,console.error("[Lanxin] JsApi Config Error..."),Cr.listeners("error").length>0&&Cr.emit("error",e)},xe.proxy(n,e)},ready:function(e){qr===k?e():Cr.on("ready",e)},error:function(e){Cr.on("error",e)}};return function(){if(T(window.lx))throw Error("Can not init Js SDK twice.");dr.forEach((function(e){return xe.use(e)}));var e=function(e){var n={};return e.forEach((function(e){e.name.split(".").reduce((function(n,t,i,r){if(i===r.length-1){if(ue(n[t]))throw Error('function "'.concat(t,'" alread existed.'));n[t]=xe.proxy.bind(null,e)}else T(n[t])||(n[t]={});return n[t]}),n)})),n}(u(ce));return a(a(a({version:"0.1.87"},Ir),e),{},{mock:Je,platform:Z})}()}));
